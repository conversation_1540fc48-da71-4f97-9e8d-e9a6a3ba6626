package com.pennypal.fintech.service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.entity.AccountBalance;
import com.pennypal.fintech.entity.MxCustomers;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.AccountBalanceRepository;
import com.pennypal.fintech.repository.MxCustomersRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserRepository;

import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MxService {

    @Value("${mx.base-url}")
    private String baseUrl;

    @Value("${mx.client-id}")
    private String clientId;

    @Value("${mx.api-key}")
    private String apiKey;

    private final RestTemplate restTemplate;

    private final AccountRepository accountRepository;

    private final AccountBalanceRepository accountBalanceRepository;

    private final MxCustomersRepository mxCustomersRepository;

    private final SubCategoryRepository subCategoryRepository;

    private final TransactionRepository transactionRepository;

    private final UserRepository userRepository;

    private final CategoryService categoryService;

    private final TransactionService transactionService;

    private final ApplicationEventPublisher eventPublisher;

    public MxService(RestTemplate restTemplate,
                     AccountRepository accountRepository,
                     AccountBalanceRepository accountBalanceRepository,
                     MxCustomersRepository mxCustomersRepository,
                     SubCategoryRepository subCategoryRepository,
                     TransactionRepository transactionRepository,
                     UserRepository userRepository,
                     CategoryService categoryService,
                     TransactionService transactionService,
                     ApplicationEventPublisher eventPublisher) {
        this.restTemplate = restTemplate;
        this.accountRepository = accountRepository;
        this.accountBalanceRepository = accountBalanceRepository;
        this.mxCustomersRepository = mxCustomersRepository;
        this.subCategoryRepository = subCategoryRepository;
        this.transactionRepository = transactionRepository;
        this.userRepository = userRepository;
        this.categoryService = categoryService;
        this.transactionService = transactionService;
        this.eventPublisher = eventPublisher;
    }

    public String createMxUser(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null or empty");
        }

        log.info("Creating MX user with ID: " + userId);
        String url = baseUrl + "/users";
        log.info("URL: " + url);

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(clientId, apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

        Map<String, Object> userBody = Map.of(
            "id", userId
        );
        Map<String, Object> body = Map.of("user", userBody);

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);
        
        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(
                url, entity, Map.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                Map<String, Object> responseBody = response.getBody();

                if (responseBody == null || !responseBody.containsKey("user")) {
                    throw new RuntimeException("Missing 'user' object in MX response");
                }

                Map<String, Object> user = (Map<String, Object>) responseBody.get("user");

                // Save to DB
                MxCustomers mxCustomers = new MxCustomers();
                mxCustomers.setUserId(userId);
                mxCustomers.setMxCustomerId((String) user.get("guid"));
                mxCustomersRepository.save(mxCustomers);

                return (String) user.get("guid");
            } else {
                throw new RuntimeException("Failed to create MX user: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException e) {
            log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
            throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("Error creating MX user", e);
            throw new RuntimeException("Failed to create MX user", e);
        }
    }

    public String getConnectWidgetUrl(Integer userId) {
        log.info("Getting connect widget URL for user ID: " + userId);

        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        MxCustomers mxCustomer = mxCustomersRepository.findByUserId(userId);
        log.info("MX customer: " + mxCustomer);

        String userGuid = null;
        if (mxCustomer == null) {
            // Create new mx customer
            userGuid = createMxUser(userId);
        } else {
            userGuid = mxCustomer.getMxCustomerId();
        }

        log.info("User GUID: " + userGuid);
        if (userGuid == null || userGuid.trim().isEmpty()) {
            throw new IllegalArgumentException("User GUID cannot be null or empty");
        }

        log.info("Getting connect widget URL for user: " + userGuid);
        String url = baseUrl + "/users/" + userGuid + "/connect_widget_url";
        log.info("URL: " + url);

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(clientId, apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

        Map<String, Object> body = Map.of("connect_widget", Map.of("mode", "aggregation"));

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
            System.out.println("Response: " + response.getBody());

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to get connect widget URL: " + response.getStatusCode());
            }

            Map<String, Object> responseBody = response.getBody();
            if (responseBody == null || !responseBody.containsKey("user")) {
                throw new RuntimeException("Missing 'user' object in MX response");
            }

            Map<String, Object> user = (Map<String, Object>) responseBody.get("user");

            return (String) user.get("connect_widget_url");
        } catch (HttpClientErrorException e) {
            log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
            throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("Error getting connect widget URL", e);
            throw new RuntimeException("Failed to get connect widget URL", e);
        }
    }

    public String getAccounts(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        MxCustomers mxCustomer = mxCustomersRepository.findByUserId(userId);
        log.info("MX customer: " + mxCustomer);
        if (mxCustomer == null) {
            throw new RuntimeException("MX customer not found for user ID: " + userId);
        }

        String userGuid = mxCustomer.getMxCustomerId();

        System.out.println("Getting accounts for user: " + userGuid);
        String url = baseUrl + "/users/" + userGuid + "/accounts";
        System.out.println("URL: " + url);

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(clientId, apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            System.out.println("Response: " + response.getBody());

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to get accounts: " + response.getStatusCode());
            }

            Map<String, Object> responseBody = response.getBody();
            if (responseBody == null || !responseBody.containsKey("accounts")) {
                throw new RuntimeException("Missing 'accounts' object in MX response");
            }

            // Save accounts to database
            saveMxAccountsToDatabase(responseBody, userId);

            return new JSONObject(responseBody).toString();
        } catch (HttpClientErrorException e) {
            log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
            throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("Error getting accounts", e);
            throw new RuntimeException("Failed to get accounts", e);
        }
    }

    public String getTransactions(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        // Fetch MX accounts
        List<Accounts> mxAccounts = accountRepository.findByUserIdAndAuthPartner(userId, "mx");
        if (mxAccounts == null || mxAccounts.isEmpty()) {
            throw new RuntimeException("No MX accounts found for user ID: " + userId);
        }
        log.info("Found {} MX accounts for user ID: {}", mxAccounts.size(), userId);

        for (Accounts account : mxAccounts) {
            try {
                int transactionsProcessed = syncTransactionsForAccount(account.getId());
                log.info("Synced {} transactions for account ID: {}", transactionsProcessed, account.getId());
            } catch (Exception e) {
                log.error("Error syncing account ID: {} for user ID: {}", account.getId(), userId, e);
            }
        }

        return "Transactions synced successfully";
    }

    @Transactional
    private void saveMxAccountsToDatabase(Map<String, Object> responseBody, Integer userId) {
        log.info("Saving MX accounts to database for user ID: {}", userId);
        
        Users user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        List<Map<String, Object>> accountsList = (List<Map<String, Object>>) responseBody.get("accounts");
        List<Accounts> savedAccounts = new ArrayList<>();
        
        for (Map<String, Object> accountData : accountsList) {
            try {
                String mxAccountId = (String) accountData.get("guid");
                log.info("Processing MX account with ID: {}", mxAccountId);
                
                // Check if account already exists
                Optional<Accounts> existingAccount = accountRepository.findByPlaidUniqueNo(mxAccountId);
                Accounts account = existingAccount.orElse(new Accounts());
                
                if (!existingAccount.isPresent()) {
                    account.setUser(user);
                    account.setPlaidUniqueNo(mxAccountId);
                    account.setAuthPartner("mx");
                    account.setInsertDatetime(LocalDateTime.now());
                }
                
                // Map MX fields to account entity
                String institutionCode = (String) accountData.get("institution_code");
                String accountName = (String) accountData.get("name");
                
                // Add capitalized institution code as prefix if available
                if (institutionCode != null && !institutionCode.isEmpty()) {
                    // Capitalize the first letter of each word in institution code
                    String capitalizedInstitution = Arrays.stream(institutionCode.split("_"))
                        .map(word -> word.isEmpty() ? "" : 
                             Character.toUpperCase(word.charAt(0)) + 
                             (word.length() > 1 ? word.substring(1).toLowerCase() : ""))
                        .collect(Collectors.joining(" "));
                    
                    // Set account name with institution prefix
                    account.setAccountName(capitalizedInstitution + " " + accountName);
                } else {
                    account.setAccountName(accountName);
                }
                
                // Handle balance
                if (accountData.get("balance") != null) {
                    if (accountData.get("balance") instanceof Double) {
                        account.setBalance((Double) accountData.get("balance"));
                    } else if (accountData.get("balance") instanceof Integer) {
                        account.setBalance(((Integer) accountData.get("balance")).doubleValue());
                    }
                }
                
                // Handle account type mapping
                String mxType = (String) accountData.get("type");
                account.setAccountType(mapMxAccountTypeToCategory(account, mxType));
                
                // Handle account subtype - available in api as 'type'
                String type = (String) accountData.get("type");
                account.setAccountSubtype(type != null ? type.toLowerCase() : "none");
                
                // Handle account number masking
                String accountNumber = (String) accountData.get("account_number");
                if (accountNumber != null && accountNumber.startsWith("XXXXXX")) {
                    account.setAccountMask(accountNumber.substring(6));
                } else {
                    account.setAccountMask(accountNumber);
                }
                
                // Set institution info
                account.setFinancialInstName((String) accountData.get("institution_code"));
                account.setInstitutionId((String) accountData.get("institution_code"));
                account.setItemId((String) accountData.get("member_guid"));
                
                // Set currency
                account.setCurrencyType((String) accountData.get("currency_code"));
                if (account.getCurrencyType() == null) {
                    account.setCurrencyType("USD");
                }
                
                // Set update time
                account.setUpdateDatetime(LocalDateTime.now());
                account.setLastSyncTime(LocalDateTime.now());
                
                // Save the account
                Accounts savedAccount = accountRepository.save(account);
                savedAccounts.add(savedAccount);
                log.info("Saved account: {}", savedAccount.getAccountName());
                
                // Add entry to account_balances for historical tracking
                try {
                    AccountBalance accountBalance = new AccountBalance();
                    accountBalance.setAccount(savedAccount);
                    accountBalance.setUser(user);
                    accountBalance.setBalance(savedAccount.getBalance());
                    accountBalance.setCurrencyCode(savedAccount.getCurrencyType());
                    accountBalance.setTimestamp(LocalDateTime.now());
                    accountBalanceRepository.save(accountBalance);
                    log.info("Saved balance history for account ID: {}", savedAccount.getId());
                } catch (Exception e) {
                    log.error("Error saving balance history: {}", e.getMessage(), e);
                }
                
            } catch (Exception e) {
                log.error("Error processing account: {}", e.getMessage(), e);
            }
        }
        
        log.info("Successfully saved {} MX accounts for user ID: {}", savedAccounts.size(), userId);
    }

    private String mapMxAccountTypeToCategory(Accounts account, String mxType) {
        if (mxType == null) return "Other";
        
        switch (mxType.toUpperCase()) {
            case "CHECKING":
            case "SAVINGS":
                account.setAccountCategory("Cash");
                return "depository";
            case "CREDIT_CARD":
                account.setAccountCategory("Credit Cards");
                return "credit";
            case "INVESTMENT":
                account.setAccountCategory("Investment Accounts");
                return "investment";
            case "LOAN":
            case "MORTGAGE":
                account.setAccountCategory("Loan");
                return "loan";
            default:
                return "other";
        }
    }

    public int syncTransactionsForAccount(Integer accountId) {
        log.info("Syncing transactions for account ID: {}", accountId);
        Accounts account = accountRepository.findById(accountId)
            .orElseThrow(() -> {
                log.error("Account not found with ID: {}", accountId);
                return new RuntimeException("Account not found with ID: " + accountId);
            });
        
        String userGuid = mxCustomersRepository.findByUserId(account.getUser().getId()).getMxCustomerId();
        if (userGuid == null || userGuid.trim().isEmpty()) {
            throw new IllegalArgumentException("User GUID cannot be null or empty");
        }
        log.info("User GUID: {}", userGuid);

        LocalDate fromDate;
        if (account.getMxLastTxnDate() == null) {
            fromDate = Instant.now().minus(3600, ChronoUnit.DAYS)
                                            .atZone(ZoneId.systemDefault()).toLocalDate();
        } else {
            fromDate = account.getMxLastTxnDate();
        }
        log.info("Fetching transactions from {} for account ID: {}", fromDate, accountId);
        
        String url = baseUrl + "/users/" + userGuid + "/accounts/" + account.getPlaidUniqueNo() + "/transactions?from_date=" + fromDate;
        log.info("URL: " + url);

        // Headers
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(clientId, apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            log.info("Response: " + response.getStatusCode());

            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to get transactions: " + response.getStatusCode());
            }

            Map<String, Object> responseBody = response.getBody();
            if (responseBody == null || !responseBody.containsKey("transactions")) {
                throw new RuntimeException("Missing 'transactions' object in MX response");
            }

            List<Map<String, Object>> transactionsList = (List<Map<String, Object>>) responseBody.get("transactions");
            if (transactionsList == null || transactionsList.isEmpty()) {
                log.info("No new transactions found for account ID: {}", accountId);
                return 0;
            }

            log.info("Processing {} transactions for account ID: {}", transactionsList.size(), accountId);
            
            // Process and save transactions
            int savedCount = saveMxTransactionsToDatabase(transactionsList, account);
            log.info("Saved {} transactions for account ID: {}", savedCount, accountId);

            // Update the last transaction date on the account
            LocalDate latestTransactionDate = transactionsList.stream()
                .map(txn -> {
                    try {
                        String dateStr = (String) txn.get("date");
                        return dateStr != null ? LocalDate.parse(dateStr) : null;
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .max(LocalDate::compareTo)
                .orElse(fromDate);
            log.info("Latest transaction date: {}", latestTransactionDate);
            
            account.setMxLastTxnDate(latestTransactionDate);
            account.setLastSyncTime(LocalDateTime.now());
            accountRepository.save(account);
            
            log.info("Successfully synced {} transactions for account ID: {}", savedCount, accountId);
            return savedCount;
        } catch (HttpClientErrorException e) {
            log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
            throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
        } catch (Exception e) {
            log.error("Error syncing transactions for account ID: {}", accountId, e);
            throw new RuntimeException("Failed to sync transactions for account ID: " + accountId, e);
        }
    }

    @Transactional
    private int saveMxTransactionsToDatabase(List<Map<String, Object>> transactionsList, Accounts account) {
        log.info("Saving {} MX transactions to database for account ID: {}", 
            transactionsList.size(), account.getId());
        
        Users user = account.getUser();
        if (user == null) {
            throw new RuntimeException("Account has no associated user");
        }

        // Collect all categories of new transactions
        Set<String> MxCategories = transactionsList
            .stream()
            .map(txn -> (String) txn.get("category"))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        log.info("Mx categories: {}", MxCategories);

        // Get category mappings
        Map<String, String> finalMappings = getCategoryMappings(MxCategories);
        log.info("Final mappings: {}", finalMappings);
        
        // Collect all transaction IDs of new transactions
        Set<String> mxTransactionIds = transactionsList
            .stream()
            .map(txn -> (String) txn.get("guid"))
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        log.info("Number of new transaction IDs: {}", mxTransactionIds.size());
        
        // Find existing transactions with these IDs
        Map<String, Transactions> existingTransactionsMap = transactionRepository
            .findByTransactionIdIn(mxTransactionIds)
            .stream()
            .collect(Collectors.toMap(Transactions::getTransactionId, Function.identity()));
        log.info("Found {} existing transactions", existingTransactionsMap.size());

        // Fetch all subcategories
        Map<String, Integer> subCategoryIdMap = subCategoryRepository
            .findAll()
            .stream()
            .collect(Collectors.toMap(SubCategory::getSubCategory, SubCategory::getId, (a, b) -> a));
        log.info("Number of sub categories: {}", subCategoryIdMap.size());
        
        List<Transactions> transactionsToSave = new ArrayList<>();
        List<TransactionDto> newTransactionDtos = new ArrayList<>();
        
        for (Map<String, Object> mxTxn : transactionsList) {
            try {
                String txnId = (String) mxTxn.get("guid");
                if (txnId == null) {
                    log.warn("Skipping transaction with null ID");
                    continue;
                }
                
                boolean isNew = !existingTransactionsMap.containsKey(txnId);
                Transactions transaction = isNew ? new Transactions() : existingTransactionsMap.get(txnId);
                
                // Set basic transaction properties
                transaction.setTransactionId(txnId);
                transaction.setAccount(account);
                transaction.setUser(user);

                // Set category
                String category = (String) mxTxn.get("category");
                if (category != null) {
                    String pfcCategory = finalMappings.getOrDefault(category, "Uncategorized");
                    transaction.setCategory(pfcCategory);
                    transaction.setSubCategoryId(subCategoryIdMap.getOrDefault(pfcCategory, null));
                } else {
                    transaction.setCategory("Uncategorized");
                }

                // Set description
                String description = (String) mxTxn.get("description");
                if (description == null) {
                    description = (String) mxTxn.get("original_description");
                }
                transaction.setDescription(description != null ? description : "Unknown");
                
                // Set transaction amount
                Object amountObj = mxTxn.get("amount");
                if (amountObj != null) {
                    double amount;
                    if (amountObj instanceof Double) {
                        amount = (Double) amountObj;
                    } else if (amountObj instanceof Integer) {
                        amount = ((Integer) amountObj).doubleValue();
                    } else if (amountObj instanceof String) {
                        amount = Double.parseDouble((String) amountObj);
                    } else {
                        log.warn("Unknown amount type: {}", amountObj.getClass());
                        amount = 0.0;
                    }
                    transaction.setTransactionAmount(amount);
                }
                
                // Set transaction date
                String dateStr = (String) mxTxn.get("date");
                if (dateStr != null) {
                    try {
                        LocalDate date = LocalDate.parse(dateStr);
                        transaction.setTransactionDate(date.atStartOfDay());
                    } catch (Exception e) {
                        log.warn("Error parsing date: {}", dateStr);
                        transaction.setTransactionDate(LocalDateTime.now());
                    }
                } else {
                    transaction.setTransactionDate(LocalDateTime.now());
                }
                
                // Set transaction type
                String type = (String) mxTxn.get("type");
                transaction.setTransactionType(type != null ? type.toLowerCase() : "unknown");
                
                if (isNew) {
                    transaction.setInsertDateTime(LocalDateTime.now());
                }
                transaction.setUpdateDateTime(LocalDateTime.now());
                
                // Add to save list
                transactionsToSave.add(transaction);
                
                // If new, add to DTOs for event publishing
                if (isNew) {
                    TransactionDto dto = transactionService.convertToDto(transaction);
                    newTransactionDtos.add(dto);
                }
            } catch (Exception e) {
                log.error("Error processing transaction: {}", e.getMessage(), e);
            }
        }
        
        // Save all transactions
        if (!transactionsToSave.isEmpty()) {
            transactionRepository.saveAll(transactionsToSave);
            log.info("Saved {} transactions", transactionsToSave.size());
            
            // Publish event for new transactions
            if (!newTransactionDtos.isEmpty()) {
                log.info("Publishing event for {} new transactions", newTransactionDtos.size());
                eventPublisher.publishEvent(new TransactionEvent(this, newTransactionDtos));
            }
        }
        
        return transactionsToSave.size();
    }

    private Map<String, String> getCategoryMappings(Set<String> MxCategories) {
        if (MxCategories.isEmpty()) {
            return Collections.emptyMap();
        }
        
        // Step 1: Fetch matching categories from sub_category table
        List<Object[]> categoryMappingsList = subCategoryRepository.fetchMxSubCategoryMappings(
            new ArrayList<String>(MxCategories));
        log.info("categoryMappingsList: {}", categoryMappingsList);

        Map<String, String> dbCategoryMappings = categoryMappingsList.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],  // Mx category as key
                row -> (String) row[1],  // sub_category as value
                (existing, replacement) -> existing  // In case of duplicate keys, keep the existing value
            ));
        log.info("dbCategoryMappings: {}", dbCategoryMappings);
        
        // Step 2: Get unmatched categories
        Set<String> unmatchedCategories = MxCategories.stream()
            .filter(cat -> !dbCategoryMappings.containsKey(cat))
            .collect(Collectors.toSet());
        log.info("unmatchedCategories: {}", unmatchedCategories);
        
        // Step 3: Fetch PFC mappings for unmatched categories if needed
        Map<String, String> pfcMappings = unmatchedCategories.isEmpty() ? 
            Collections.emptyMap() : categoryService.fetchPfcMappings(unmatchedCategories, "mx");
        log.info("pfcMappings: {}", pfcMappings);
        
        // Step 4: Combine both maps
        Map<String, String> finalMappings = new HashMap<>(dbCategoryMappings);
        finalMappings.putAll(pfcMappings);
        log.info("finalMappings: {}", finalMappings);
        
        return finalMappings;
    }


/**
 * Sync a single MX account by account ID
 */
public String syncSingleAccount(Integer accountId) {
    if (accountId == null) {
        throw new IllegalArgumentException("Account ID cannot be null");
    }

    log.info("Syncing single MX account with ID: {}", accountId);
    
    // Get the account from database
    Accounts account = accountRepository.findById(accountId)
        .orElseThrow(() -> new RuntimeException("Account not found with ID: " + accountId));
    
    // Verify this is an MX account
    if (!"mx".equals(account.getAuthPartner())) {
        throw new RuntimeException("Account ID " + accountId + " is not an MX account");
    }

    Integer userId = account.getUser().getId();
    
    // Get MX customer info
    MxCustomers mxCustomer = mxCustomersRepository.findByUserId(userId);
    if (mxCustomer == null) {
        throw new RuntimeException("MX customer not found for user ID: " + userId);
    }

    String userGuid = mxCustomer.getMxCustomerId();
    String accountGuid = account.getPlaidUniqueNo(); // This stores the MX account GUID
    
    log.info("Syncing MX account GUID: {} for user GUID: {}", accountGuid, userGuid);
    
    // Sync account details first
    syncSingleAccountDetails(userGuid, accountGuid, account);
    
    // Sync transactions for this account
    int transactionsProcessed = syncTransactionsForAccount(accountId);
    
    log.info("Successfully synced account ID: {} with {} transactions", accountId, transactionsProcessed);
    return "Account synced successfully with " + transactionsProcessed + " transactions";
}

/**
 * Sync details for a single MX account
 */
private void syncSingleAccountDetails(String userGuid, String accountGuid, Accounts account) {
    log.info("Syncing account details for MX account GUID: {}", accountGuid);
    
    String url = baseUrl + "/users/" + userGuid + "/accounts/" + accountGuid;
    log.info("URL: {}", url);

    // Headers
    HttpHeaders headers = new HttpHeaders();
    headers.setBasicAuth(clientId, apiKey);
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

    HttpEntity<Void> entity = new HttpEntity<>(headers);

    try {
        ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
        
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to get account details: " + response.getStatusCode());
        }

        Map<String, Object> responseBody = response.getBody();
        if (responseBody == null || !responseBody.containsKey("account")) {
            throw new RuntimeException("Missing 'account' object in MX response");
        }

        Map<String, Object> accountData = (Map<String, Object>) responseBody.get("account");
        
        // Update account details
        updateSingleAccountInDatabase(accountData, account);
        
    } catch (HttpClientErrorException e) {
        log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
        throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
    } catch (Exception e) {
        log.error("Error syncing account details for account GUID: {}", accountGuid, e);
        throw new RuntimeException("Failed to sync account details", e);
    }
}

/**
 * Update a single account in the database
 */
@Transactional
private void updateSingleAccountInDatabase(Map<String, Object> accountData, Accounts account) {
    log.info("Updating single account in database: {}", account.getId());
    
    Users user = account.getUser();
    
    try {
        // Map MX fields to account entity
        String institutionCode = (String) accountData.get("institution_code");
        String accountName = (String) accountData.get("name");
        
        // Add capitalized institution code as prefix if available
        if (institutionCode != null && !institutionCode.isEmpty()) {
            String capitalizedInstitution = Arrays.stream(institutionCode.split("_"))
                .map(word -> word.isEmpty() ? "" : 
                     Character.toUpperCase(word.charAt(0)) + 
                     (word.length() > 1 ? word.substring(1).toLowerCase() : ""))
                .collect(Collectors.joining(" "));
            
            account.setAccountName(capitalizedInstitution + " " + accountName);
        } else {
            account.setAccountName(accountName);
        }
        
        // Handle balance update
        if (accountData.get("balance") != null) {
            Double oldBalance = account.getBalance();
            Double newBalance;
            
            if (accountData.get("balance") instanceof Double) {
                newBalance = (Double) accountData.get("balance");
            } else if (accountData.get("balance") instanceof Integer) {
                newBalance = ((Integer) accountData.get("balance")).doubleValue();
            } else {
                newBalance = oldBalance; // Keep old balance if can't parse
            }
            
            account.setBalance(newBalance);
            
            // Log balance change
            if (oldBalance != null && !oldBalance.equals(newBalance)) {
                log.info("Balance updated for account ID: {} from {} to {}", 
                    account.getId(), oldBalance, newBalance);
            }
        }
        
        // Handle account type mapping
        String mxType = (String) accountData.get("type");
        account.setAccountType(mapMxAccountTypeToCategory(account, mxType));
        
        // Handle account subtype
        String type = (String) accountData.get("type");
        account.setAccountSubtype(type != null ? type.toLowerCase() : "none");
        
        // Handle account number masking
        String accountNumber = (String) accountData.get("account_number");
        if (accountNumber != null && accountNumber.startsWith("XXXXXX")) {
            account.setAccountMask(accountNumber.substring(6));
        } else {
            account.setAccountMask(accountNumber);
        }
        
        // Set institution info
        account.setFinancialInstName((String) accountData.get("institution_code"));
        account.setInstitutionId((String) accountData.get("institution_code"));
        account.setItemId((String) accountData.get("member_guid"));
        
        // Set currency
        account.setCurrencyType((String) accountData.get("currency_code"));
        if (account.getCurrencyType() == null) {
            account.setCurrencyType("USD");
        }
        
        // Set update time
        account.setUpdateDatetime(LocalDateTime.now());
        account.setLastSyncTime(LocalDateTime.now());
        
        // Save the account
        Accounts savedAccount = accountRepository.save(account);
        log.info("Updated account: {}", savedAccount.getAccountName());
        
        // Add entry to account_balances for historical tracking
        try {
            AccountBalance accountBalance = new AccountBalance();
            accountBalance.setAccount(savedAccount);
            accountBalance.setUser(user);
            accountBalance.setBalance(savedAccount.getBalance());
            accountBalance.setCurrencyCode(savedAccount.getCurrencyType());
            accountBalance.setTimestamp(LocalDateTime.now());
            accountBalanceRepository.save(accountBalance);
            log.info("Saved balance history for account ID: {}", savedAccount.getId());
        } catch (Exception e) {
            log.error("Error saving balance history: {}", e.getMessage(), e);
        }
        
    } catch (Exception e) {
        log.error("Error updating account: {}", e.getMessage(), e);
        throw new RuntimeException("Failed to update account", e);
    }
}

/**
 * Get MX account details for a specific account
 */
public String getSingleAccount(Integer accountId) {
    if (accountId == null) {
        throw new IllegalArgumentException("Account ID cannot be null");
    }

    log.info("Getting single MX account details for account ID: {}", accountId);
    
    // Get the account from database
    Accounts account = accountRepository.findById(accountId)
        .orElseThrow(() -> new RuntimeException("Account not found with ID: " + accountId));
    
    // Verify this is an MX account
    if (!"mx".equals(account.getAuthPartner())) {
        throw new RuntimeException("Account ID " + accountId + " is not an MX account");
    }

    Integer userId = account.getUser().getId();
    MxCustomers mxCustomer = mxCustomersRepository.findByUserId(userId);
    if (mxCustomer == null) {
        throw new RuntimeException("MX customer not found for user ID: " + userId);
    }

    String userGuid = mxCustomer.getMxCustomerId();
    String accountGuid = account.getPlaidUniqueNo();
    
    String url = baseUrl + "/users/" + userGuid + "/accounts/" + accountGuid;
    log.info("URL: {}", url);

    // Headers
    HttpHeaders headers = new HttpHeaders();
    headers.setBasicAuth(clientId, apiKey);
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));

    HttpEntity<Void> entity = new HttpEntity<>(headers);

    try {
        ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
        
        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to get account: " + response.getStatusCode());
        }

        Map<String, Object> responseBody = response.getBody();
        if (responseBody == null || !responseBody.containsKey("account")) {
            throw new RuntimeException("Missing 'account' object in MX response");
        }

        // Update account in database
        Map<String, Object> accountData = (Map<String, Object>) responseBody.get("account");
        updateSingleAccountInDatabase(accountData, account);

        return new JSONObject(responseBody).toString();
    } catch (HttpClientErrorException e) {
        log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
        throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
    } catch (Exception e) {
        log.error("Error getting single account", e);
        throw new RuntimeException("Failed to get account details", e);
    }
}
}