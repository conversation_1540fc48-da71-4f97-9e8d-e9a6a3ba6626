// // package com.pennypal.fintech.service;

// // import com.pennypal.fintech.entity.Accounts;
// // import com.pennypal.fintech.repository.AccountRepository;
// // import okhttp3.*;
// // import org.springframework.beans.factory.annotation.Value;
// // import org.springframework.stereotype.Service;

// // import java.io.IOException;
// // import java.util.List;

// // @Service
// // public class InvestmentService {

// //     private final AccountRepository accountRepository;
// //     private final OkHttpClient httpClient = new OkHttpClient();

// //     @Value("${plaid.client_id}")
// //     private String clientId;

// //     @Value("${plaid.secret}")
// //     private String secret;

// //     public InvestmentService(AccountRepository accountRepository) {
// //         this.accountRepository = accountRepository;
// //     }

// //     public String getInvestmentsForUser(int userId) throws IOException {
// //         List<Accounts> userAccounts = accountRepository.findByUser_Id(userId);

// //         if (userAccounts.isEmpty()) {
// //             throw new RuntimeException("No accounts found for user ID: " + userId);
// //         }

// //         StringBuilder investmentResults = new StringBuilder();

// //         for (Accounts account : userAccounts) {
// //             String accessToken = account.getAccessToken();

// //             String url = "https://sandbox.plaid.com/investments/holdings/get";
// //             String payload = "{"
// //                     + "\"client_id\":\"" + clientId + "\","
// //                     + "\"secret\":\"" + secret + "\","
// //                     + "\"access_token\":\"" + accessToken + "\""
// //                     + "}";

// //             RequestBody body = RequestBody.create(payload, MediaType.parse("application/json"));
// //             Request request = new Request.Builder()
// //                     .url(url)
// //                     .post(body)
// //                     .addHeader("Content-Type", "application/json")
// //                     .build();

// //             try (Response response = httpClient.newCall(request).execute()) {
// //                 if (!response.isSuccessful()) {
// //                     investmentResults.append("Failed for account: ").append(account.getId())
// //                             .append(" -> ").append(response.message()).append("\n");
// //                 } else {
// //                     investmentResults.append(response.body().string()).append("\n");
// //                 }
// //             }
// //         }

// //         return investmentResults.toString();
// //     }
// // }

// package com.pennypal.fintech.service;

// import com.pennypal.fintech.entity.Investment;
// import com.pennypal.fintech.entity.DailyInvestmentStock;
// import com.pennypal.fintech.entity.Users;
// import com.pennypal.fintech.dto.InvestmentDto;
// import com.pennypal.fintech.entity.Accounts;
// import com.pennypal.fintech.repository.InvestmentRepository;
// import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
// import com.pennypal.fintech.repository.UserRepository;
// import com.pennypal.fintech.repository.AccountRepository;
// import java.math.RoundingMode;

// import org.json.JSONArray;
// import org.json.JSONObject;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;
// import java.math.BigDecimal;

// import lombok.extern.slf4j.Slf4j;

// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.format.DateTimeFormatter;
// import java.util.List;
// import java.util.Optional;
// import java.util.UUID;
// import java.util.stream.Collectors;

// @Service
// @Slf4j
// public class InvestmentService {

//     private final InvestmentRepository investmentRepository;
//     private final DailyInvestmentStockRepository dailyInvestmentStockRepository;
//     private final UserRepository userRepository;
//     private final AccountRepository accountRepository;
//     private final InvestmentMxService investmentMxService;

//     public InvestmentService(InvestmentRepository investmentRepository,
//                            DailyInvestmentStockRepository dailyInvestmentStockRepository,
//                            UserRepository userRepository,
//                            AccountRepository accountRepository,
//                            InvestmentMxService investmentMxService) {
//         this.investmentRepository = investmentRepository;
//         this.dailyInvestmentStockRepository = dailyInvestmentStockRepository;
//         this.userRepository = userRepository;
//         this.accountRepository = accountRepository;
//         this.investmentMxService = investmentMxService;
//     }
//   /**
//      * Get investments by user ID and return as DTOs
//      */
//     public List<InvestmentDto> getInvestmentsByUserId(Integer userId) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
        
//         List<Investment> investments = investmentRepository.findByUser_Id(userId);
//         log.info("Fetched {} investments for userId={}", investments.size(), userId);

//         return investments.stream()
//                 .map(this::convertToDto)
//                 .collect(Collectors.toList());
//     }

//     /**
//      * Get investments by account ID and return as DTOs
//      */
//     public List<InvestmentDto> getInvestmentsDtoByAccountId(Integer accountId) {
//         if (accountId == null || accountId <= 0) {
//             throw new IllegalArgumentException("Invalid account ID");
//         }
        
//         List<Investment> investments = investmentRepository.findByAccount_Id(accountId);
//         return investments.stream()
//                 .map(this::convertToDto)
//                 .collect(Collectors.toList());
//     }

//     /**
//      * Get investments by security type and return as DTOs
//      */
//     public List<InvestmentDto> getInvestmentsDtoBySecurityType(Integer userId, String securityType) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
//         if (securityType == null || securityType.trim().isEmpty()) {
//             throw new IllegalArgumentException("Security type cannot be null or empty");
//         }
        
//         List<Investment> investments = investmentRepository.findByUser_IdAndSecurityType(userId, securityType);
//         return investments.stream()
//                 .map(this::convertToDto)
//                 .collect(Collectors.toList());
//     }

//     /**
//      * Get single investment by ID and return as DTO
//      */
//     public Optional<InvestmentDto> getInvestmentDtoById(Integer id) {
//         if (id == null || id <= 0) {
//             throw new IllegalArgumentException("Invalid investment ID");
//         }
        
//         return investmentRepository.findById(id)
//                 .map(this::convertToDto);
//     }

//     /**
//      * Convert Investment entity to InvestmentDto with enhanced data
//      */
//     private InvestmentDto convertToDto(Investment investment) {
//         InvestmentDto dto = new InvestmentDto();

//         // Basic fields
//         dto.setId(investment.getId());
//         dto.setUserId(investment.getUser().getId());
        
//         // Handle account - check for null to avoid NullPointerException
//         if (investment.getAccount() != null) {
//             dto.setAccountId(investment.getAccount().getId());
//         } else {
//             dto.setAccountId(0); // or set to -1 or handle as needed
//             log.warn("Investment {} has no associated account", investment.getId());
//         }
        
//         dto.setSecurityName(investment.getSecurityName());
//         dto.setTicker(investment.getTicker());
//         dto.setSecurityType(investment.getSecurityType());
//         dto.setQuantity(investment.getQuantity());
//         dto.setValue(investment.getValue());
//         dto.setCostBasis(investment.getCostBasis());
//         dto.setCurrentPrice(investment.getCurrentPrice());
//         dto.setCurrencyCode(investment.getCurrencyCode());
//         dto.setLastUpdated(investment.getLastUpdated());

//         // Calculate total gain and percent
//         if (investment.getCostBasis() != null && investment.getValue() != null) {
//             double gain = investment.getValue() - investment.getCostBasis();
//             dto.setTotalGain(roundToTwoDecimals(gain));
            
//             if (investment.getCostBasis() != 0) {
//                 double gainPercent = (gain / investment.getCostBasis()) * 100;
//                 dto.setTotalGainPercent(roundToTwoDecimals(gainPercent));
//             } else {
//                 dto.setTotalGainPercent(0.0);
//             }
//         } else {
//             dto.setTotalGain(0.0);
//             dto.setTotalGainPercent(0.0);
//         }

//         // Get today's change from daily investment stock data
//         setTodayChangeData(dto, investment);

//         return dto;
//     }

//     /**
//      * Set today's change data from daily investment stock
//      */
//     private void setTodayChangeData(InvestmentDto dto, Investment investment) {
//         try {
//             LocalDate today = LocalDate.now();
            
//             // Get today's data
//             Optional<DailyInvestmentStock> todayData = dailyInvestmentStockRepository
//                     .findByInvestmentIdAndDate(investment.getId(), today);
            
//             if (todayData.isPresent()) {
//                 DailyInvestmentStock todayStock = todayData.get();
                
//                 // Set today's change data
//                 dto.setTodayChange(todayStock.getPriceChange());
//                 dto.setTodayChangePercent(todayStock.getPercentChange());
//             } else {
//                 // If no today's data, try to get the latest available data
//                 Optional<DailyInvestmentStock> latestData = dailyInvestmentStockRepository
//                         .findTopByInvestmentIdOrderByDateDescTimestampDesc(investment.getId());
                
//                 if (latestData.isPresent()) {
//                     DailyInvestmentStock latestStock = latestData.get();
//                     dto.setTodayChange(latestStock.getPriceChange());
//                     dto.setTodayChangePercent(latestStock.getPercentChange());
//                 } else {
//                     // No daily data available
//                     dto.setTodayChange(0.0);
//                     dto.setTodayChangePercent(0.0);
//                 }
//             }
//         } catch (Exception e) {
//             log.error("Error setting today's change data for investment {}: {}", 
//                     investment.getId(), e.getMessage());
//             dto.setTodayChange(0.0);
//             dto.setTodayChangePercent(0.0);
//         }
//     }

//     /**
//      * Helper method to round values to 2 decimal places
//      */
//     private Double roundToTwoDecimals(Double value) {
//         if (value == null) {
//             return 0.0;
//         }
//         return BigDecimal.valueOf(value)
//                 .setScale(2, RoundingMode.HALF_UP)
//                 .doubleValue();
//     }

//     /**
//      * Get portfolio summary as DTO list with aggregated data
//      */
//     public List<InvestmentDto> getPortfolioSummary(Integer userId) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
        
//         return getInvestmentsByUserId(userId);
//     }

//     /**
//      * Get investments grouped by security type
//      */
//     public List<InvestmentDto> getStocksOnly(Integer userId) {
//         return getInvestmentsDtoBySecurityType(userId, "stock");
//     }

//     public List<InvestmentDto> getMutualFundsOnly(Integer userId) {
//         return getInvestmentsDtoBySecurityType(userId, "mutual_fund");
//     }

//     public List<InvestmentDto> getBondsOnly(Integer userId) {
//         return getInvestmentsDtoBySecurityType(userId, "bond");
//     }

//     public List<InvestmentDto> getETFsOnly(Integer userId) {
//         return getInvestmentsDtoBySecurityType(userId, "etf");
//     }

//     /**
//      * Get total portfolio value (keeping the existing method)
//      */
//     public Double getTotalPortfolioValue(Integer userId) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
//         Double value = investmentRepository.getTotalValueByUserId(userId);
//         return value != null ? value : 0.0;
//     }

//     /**
//      * Get portfolio statistics
//      */
//     public PortfolioSummaryDto getPortfolioStatistics(Integer userId) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
        
//         List<InvestmentDto> investments = getInvestmentsByUserId(userId);
        
//         PortfolioSummaryDto summary = new PortfolioSummaryDto();
//         summary.setTotalInvestments(investments.size());
        
//         double totalValue = investments.stream()
//                 .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
//                 .sum();
//         summary.setTotalValue(roundToTwoDecimals(totalValue));
        
//         double totalCostBasis = investments.stream()
//                 .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
//                 .sum();
//         summary.setTotalCostBasis(roundToTwoDecimals(totalCostBasis));
        
//         double totalGain = totalValue - totalCostBasis;
//         summary.setTotalGain(roundToTwoDecimals(totalGain));
        
//         if (totalCostBasis > 0) {
//             double totalGainPercent = (totalGain / totalCostBasis) * 100;
//             summary.setTotalGainPercent(roundToTwoDecimals(totalGainPercent));
//         } else {
//             summary.setTotalGainPercent(0.0);
//         }
        
//         double todayChange = investments.stream()
//                 .mapToDouble(inv -> inv.getTodayChange() != null ? inv.getTodayChange() : 0.0)
//                 .sum();
//         summary.setTodayTotalChange(roundToTwoDecimals(todayChange));
        
//         return summary;
//     }

//     // Inner class for portfolio summary
//     public static class PortfolioSummaryDto {
//         private int totalInvestments;
//         private Double totalValue;
//         private Double totalCostBasis;
//         private Double totalGain;
//         private Double totalGainPercent;
//         private Double todayTotalChange;
        
//         // Getters and setters
//         public int getTotalInvestments() { return totalInvestments; }
//         public void setTotalInvestments(int totalInvestments) { this.totalInvestments = totalInvestments; }
        
//         public Double getTotalValue() { return totalValue; }
//         public void setTotalValue(Double totalValue) { this.totalValue = totalValue; }
        
//         public Double getTotalCostBasis() { return totalCostBasis; }
//         public void setTotalCostBasis(Double totalCostBasis) { this.totalCostBasis = totalCostBasis; }
        
//         public Double getTotalGain() { return totalGain; }
//         public void setTotalGain(Double totalGain) { this.totalGain = totalGain; }
        
//         public Double getTotalGainPercent() { return totalGainPercent; }
//         public void setTotalGainPercent(Double totalGainPercent) { this.totalGainPercent = totalGainPercent; }
        
//         public Double getTodayTotalChange() { return todayTotalChange; }
//         public void setTodayTotalChange(Double todayTotalChange) { this.todayTotalChange = todayTotalChange; }
//     }
//     @Transactional
//     public String syncAndSaveInvestmentData(Integer userId) {
//         try {
//             log.info("Starting investment data sync for user: {}", userId);
            
//             // Validate userId
//             if (userId == null || userId <= 0) {
//                 throw new IllegalArgumentException("Invalid user ID: " + userId);
//             }
            
//             // Verify user exists
//             Users user = userRepository.findById(userId)
//                 .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
            
//             // Get holdings from MX
//             String holdingsJson = investmentMxService.getAllHoldings(userId);
            
//             if (holdingsJson == null || holdingsJson.trim().isEmpty()) {
//                 log.warn("No holdings data received from MX for user: {}", userId);
//                 return "No investment data found to sync";
//             }
            
//             // Save investments to database
//             saveInvestmentsFromMxData(userId, holdingsJson);
            
//             // AUTO-UPDATE: Update investments table from latest daily data
//             updateInvestmentsFromDailyData(userId);
            
//             log.info("Successfully completed investment data sync with auto-update for user: {}", userId);
//             return "Investment data synchronized and saved successfully with auto-update";
            
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid argument for user {}: {}", userId, e.getMessage());
//             throw e;
//         } catch (RuntimeException e) {
//             log.error("Runtime error syncing investment data for user {}: {}", userId, e.getMessage(), e);
//             throw e;
//         } catch (Exception e) {
//             log.error("Unexpected error syncing investment data for user {}: {}", userId, e.getMessage(), e);
//             throw new RuntimeException("Failed to sync and save investment data", e);
//         }
//     }

//     @Transactional
//     public void saveInvestmentsFromMxData(Integer userId, String holdingsJson) {
//         try {
//             if (holdingsJson == null || holdingsJson.trim().isEmpty()) {
//                 throw new IllegalArgumentException("Holdings JSON data is null or empty");
//             }
            
//             JSONObject holdingsData = new JSONObject(holdingsJson);
            
//             if (!holdingsData.has("holdings")) {
//                 log.warn("No 'holdings' array found in JSON data for user: {}", userId);
//                 return;
//             }
            
//             JSONArray holdings = holdingsData.getJSONArray("holdings");
            
//             if (holdings.length() == 0) {
//                 log.info("No holdings found for user: {}", userId);
//                 return;
//             }
            
//             String syncId = UUID.randomUUID().toString();
//             LocalDateTime now = LocalDateTime.now();
            
//             int successCount = 0;
//             int errorCount = 0;
            
//             for (int i = 0; i < holdings.length(); i++) {
//                 try {
//                     JSONObject holding = holdings.getJSONObject(i);
//                     saveOrUpdateInvestment(userId, holding, syncId, now);
//                     successCount++;
//                 } catch (Exception e) {
//                     errorCount++;
//                     log.error("Error processing holding {} for user {}: {}", i, userId, e.getMessage(), e);
//                 }
//             }
            
//             log.info("Investment processing completed for user {}: {} successful, {} errors", 
//                 userId, successCount, errorCount);
            
//             if (errorCount > 0 && successCount == 0) {
//                 throw new RuntimeException("Failed to save any investments - all holdings had errors");
//             }
            
//         } catch (Exception e) {
//             log.error("Error saving investments from MX data for user {}: {}", userId, e.getMessage(), e);
//             throw new RuntimeException("Failed to save investments from MX data", e);
//         }
//     }

//     private void saveOrUpdateInvestment(Integer userId, JSONObject holding, String syncId, LocalDateTime timestamp) {
//         try {
//             String investmentId = holding.optString("guid");
//             String accountGuid = holding.optString("account_guid");
            
//             if (investmentId == null || investmentId.trim().isEmpty()) {
//                 throw new IllegalArgumentException("Investment ID (guid) is missing or empty");
//             }
            
//             // Find or create investment
//             Optional<Investment> existingInvestment = investmentRepository.findByInvestmentId(investmentId);
//             Investment investment;
            
//             if (existingInvestment.isPresent()) {
//                 investment = existingInvestment.get();
//                 log.debug("Updating existing investment: {}", investmentId);
//             } else {
//                 investment = new Investment();
//                 investment.setInvestmentId(investmentId);
//                 investment.setInsertDateTime(timestamp);
                
//                 // Set user
//                 Users user = userRepository.findById(userId)
//                     .orElseThrow(() -> new RuntimeException("User not found: " + userId));
//                 investment.setUser(user);
                
//                 // Set account if available
//                 if (accountGuid != null && !accountGuid.trim().isEmpty()) {
//                     Optional<Accounts> account = accountRepository.findByPlaidUniqueNo(accountGuid);
//                     if (account.isPresent()) {
//                         investment.setAccount(account.get());
//                     } else {
//                         log.warn("Account not found for GUID: {} (investment: {})", accountGuid, investmentId);
//                     }
//                 }
                
//                 log.debug("Creating new investment: {}", investmentId);
//             }
            
//             // Update investment data with null-safe operations
//             investment.setSecurityId(holding.optString("security_guid", null));
//             investment.setSecurityName(holding.optString("description", null));
//             investment.setTicker(holding.optString("symbol", null));
//             investment.setSecurityType(holding.optString("holding_type", null));
            
//             // Handle numeric fields with proper validation
//             Double quantity = holding.has("shares") && !holding.isNull("shares") ? 
//                 holding.optDouble("shares", 0.0) : null;
//             Double value = holding.has("market_value") && !holding.isNull("market_value") ? 
//                 holding.optDouble("market_value", 0.0) : null;
//             Double costBasis = holding.has("cost_basis") && !holding.isNull("cost_basis") ? 
//                 holding.optDouble("cost_basis", 0.0) : null;
//             Double currentPrice = holding.has("unit_price") && !holding.isNull("unit_price") ? 
//                 holding.optDouble("unit_price", 0.0) : null;
            
//             investment.setQuantity(quantity);
//             investment.setValue(value);
//             investment.setCostBasis(costBasis);
//             investment.setCurrentPrice(currentPrice);
//             investment.setCurrencyCode(holding.optString("currency_code", "USD"));
//             investment.setLastUpdated(timestamp);
//             investment.setSyncId(syncId);
            
//             // Calculate average purchase price
//             if (quantity != null && quantity > 0 && costBasis != null && costBasis > 0) {
//                 investment.setAveragePurchasePrice(costBasis / quantity);
//             }
            
//             // Save investment
//             investment = investmentRepository.save(investment);
            
//             // Save daily stock data
//             saveDailyInvestmentStock(investment, holding, timestamp);
            
//         } catch (Exception e) {
//             log.error("Error saving individual investment with ID {}: {}", 
//                 holding.optString("guid", "unknown"), e.getMessage(), e);
//             throw new RuntimeException("Failed to save investment", e);
//         }
//     }

//     /**
//      * Get daily stock data by security type (stock/mutual fund)
//      */
//     public List<DailyInvestmentStock> getDailyDataBySecurityType(Integer userId, String securityType, 
//                                                                LocalDate fromDate, LocalDate toDate) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
//         if (securityType == null || securityType.trim().isEmpty()) {
//             throw new IllegalArgumentException("Security type cannot be null or empty");
//         }
//         if (fromDate == null || toDate == null) {
//             throw new IllegalArgumentException("Date parameters cannot be null");
//         }
//         if (fromDate.isAfter(toDate)) {
//             throw new IllegalArgumentException("From date cannot be after to date");
//         }
        
//         return dailyInvestmentStockRepository.findByUserIdAndSecurityTypeAndDateBetween(
//             userId, securityType, fromDate, toDate);
//     }

//     /**
//      * Get stocks daily data only
//      */
//     public List<DailyInvestmentStock> getDailyStocksData(Integer userId, LocalDate fromDate, LocalDate toDate) {
//         return getDailyDataBySecurityType(userId, "stock", fromDate, toDate);
//     }

//     /**
//      * Get mutual funds daily data only
//      */
//     public List<DailyInvestmentStock> getDailyMutualFundsData(Integer userId, LocalDate fromDate, LocalDate toDate) {
//         return getDailyDataBySecurityType(userId, "mutual_fund", fromDate, toDate);
//     }
// public void saveDailyInvestmentStock(Investment investment, JSONObject holding, LocalDateTime timestamp) {
//     try {
//         LocalDate today = timestamp.toLocalDate();

//         // Check if daily record already exists for today
//         Optional<DailyInvestmentStock> existingRecord =
//                 dailyInvestmentStockRepository.findByInvestmentIdAndDate(investment.getId(), today);

//         DailyInvestmentStock dailyStock = existingRecord.orElseGet(DailyInvestmentStock::new);
//         if (existingRecord.isEmpty()) {
//             dailyStock.setInvestment(investment);
//             dailyStock.setDate(today);
//             log.debug("Creating new daily stock record for investment {} on {}", investment.getId(), today);
//         } else {
//             log.debug("Updating existing daily stock record for investment {} on {}", investment.getId(), today);
//         }

//         // Extract values from holding JSON
//         Double quantity = holding.has("shares") && !holding.isNull("shares")
//                 ? holding.optDouble("shares", 0.0) : null;
//         Double marketValue = holding.has("market_value") && !holding.isNull("market_value")
//                 ? holding.optDouble("market_value", 0.0) : null;

//         // Calculate price
//         Double currentPrice = null;
//         if (quantity != null && quantity > 0 && marketValue != null) {
//             currentPrice = BigDecimal.valueOf(marketValue)
//                     .divide(BigDecimal.valueOf(quantity), 4, RoundingMode.HALF_UP)
//                     .doubleValue();
//         }

//         // Set daily stock values
//         dailyStock.setPrice(currentPrice);
//         dailyStock.setQuantity(quantity);
//         dailyStock.setValue(marketValue);
//         dailyStock.setTimestamp(timestamp);
//         dailyStock.setSecurityType(holding.optString("holding_type", null));
//         dailyStock.setTicker(holding.optString("symbol", null));
//         dailyStock.setUserId(investment.getUser().getId());

//         // Calculate price change from last available day
//         if (currentPrice != null) {
//             Optional<DailyInvestmentStock> lastRecord =
//                     dailyInvestmentStockRepository.findTopByInvestmentIdAndDateBeforeOrderByDateDesc(
//                             investment.getId(), today);

//             if (lastRecord.isPresent() && lastRecord.get().getPrice() != null) {
//                 Double lastPrice = lastRecord.get().getPrice();
//                 Double priceChange = currentPrice - lastPrice;
//                 Double percentChange = (priceChange / lastPrice) * 100;

//                 // Round values
//                 dailyStock.setPriceChange(BigDecimal.valueOf(priceChange).setScale(2, RoundingMode.HALF_UP).doubleValue());
//                 dailyStock.setPercentChange(BigDecimal.valueOf(percentChange).setScale(2, RoundingMode.HALF_UP).doubleValue());
//             } else {
//                 dailyStock.setPriceChange(0.0);
//                 dailyStock.setPercentChange(0.0);
//             }
//         } else {
//             log.warn("Missing data to calculate price change for investment {}", investment.getId());
//             dailyStock.setPriceChange(0.0);
//             dailyStock.setPercentChange(0.0);
//         }

//         dailyInvestmentStockRepository.save(dailyStock);

//         // 🔄 Update investment table with latest values
//         boolean updated = false;

//         if (currentPrice != null && !currentPrice.equals(investment.getCurrentPrice())) {
//             investment.setCurrentPrice(currentPrice);
//             updated = true;
//         }

//         if (quantity != null && !quantity.equals(investment.getQuantity())) {
//             investment.setQuantity(quantity);
//             updated = true;
//         }

//         if (marketValue != null && !marketValue.equals(investment.getValue())) {
//             investment.setValue(marketValue);
//             updated = true;
//         }

//         if (updated) {
//             investment.setLastUpdated(LocalDateTime.now());
//             investmentRepository.save(investment);
//             log.debug("Updated investment {} from daily stock data", investment.getId());
//         }

//         log.debug("Saved daily stock for investment {} | price: {}, qty: {}, val: {}", 
//             investment.getId(), currentPrice, quantity, marketValue);

//     } catch (Exception e) {
//         log.error("Error saving daily stock for investment {}: {}", investment.getId(), e.getMessage(), e);
//         // DO NOT throw, to avoid rollback of parent transaction
//     }
// }

//     // public List<Investment> getInvestmentsByUserId(Integer userId) {
//     //     if (userId == null || userId <= 0) {
//     //         throw new IllegalArgumentException("Invalid user ID");
//     //     }
//     //     return investmentRepository.findByUser_Id(userId);
//     // }

//     public List<Investment> getInvestmentsByAccountId(Integer accountId) {
//         if (accountId == null || accountId <= 0) {
//             throw new IllegalArgumentException("Invalid account ID");
//         }
//         return investmentRepository.findByAccount_Id(accountId);
//     }

//     public List<DailyInvestmentStock> getDailyStockData(Integer investmentId, LocalDateTime fromDate, LocalDateTime toDate) {
//         if (investmentId == null || investmentId <= 0) {
//             throw new IllegalArgumentException("Invalid investment ID");
//         }
//         if (fromDate == null || toDate == null) {
//             throw new IllegalArgumentException("Date parameters cannot be null");
//         }
//         if (fromDate.isAfter(toDate)) {
//             throw new IllegalArgumentException("From date cannot be after to date");
//         }
//         return dailyInvestmentStockRepository.findByInvestmentIdAndTimestampBetween(investmentId, fromDate, toDate);
//     }

//     public List<DailyInvestmentStock> getUserDailyStockData(Integer userId, LocalDateTime fromDate, LocalDateTime toDate) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
//         if (fromDate == null || toDate == null) {
//             throw new IllegalArgumentException("Date parameters cannot be null");
//         }
//         if (fromDate.isAfter(toDate)) {
//             throw new IllegalArgumentException("From date cannot be after to date");
//         }
//         return dailyInvestmentStockRepository.findByUserIdAndTimestampBetween(userId, fromDate, toDate);
//     }

//     public Double getAccountTotalValue(Integer accountId) {
//         if (accountId == null || accountId <= 0) {
//             throw new IllegalArgumentException("Invalid account ID");
//         }
//         Double value = investmentRepository.getTotalValueByAccountId(accountId);
//         return value != null ? value : 0.0;
//     }

//     public Optional<Investment> getInvestmentById(Integer id) {
//         if (id == null || id <= 0) {
//             throw new IllegalArgumentException("Invalid investment ID");
//         }
//         return investmentRepository.findById(id);
//     }

//     public Optional<Investment> getInvestmentByInvestmentId(String investmentId) {
//         if (investmentId == null || investmentId.trim().isEmpty()) {
//             throw new IllegalArgumentException("Investment ID cannot be null or empty");
//         }
//         return investmentRepository.findByInvestmentId(investmentId);
//     }

//     public List<Investment> getInvestmentsByTicker(String ticker) {
//         if (ticker == null || ticker.trim().isEmpty()) {
//             throw new IllegalArgumentException("Ticker cannot be null or empty");
//         }
//         return investmentRepository.findByTicker(ticker);
//     }

//     public List<Investment> getInvestmentsBySecurityType(Integer userId, String securityType) {
//         if (userId == null || userId <= 0) {
//             throw new IllegalArgumentException("Invalid user ID");
//         }
//         if (securityType == null || securityType.trim().isEmpty()) {
//             throw new IllegalArgumentException("Security type cannot be null or empty");
//         }
//         return investmentRepository.findByUser_IdAndSecurityType(userId, securityType);
//     }

//     @Transactional
//     public void deleteInvestment(Integer investmentId) {
//         if (investmentId == null || investmentId <= 0) {
//             throw new IllegalArgumentException("Invalid investment ID");
//         }
        
//         try {
//             // Check if investment exists
//             if (!investmentRepository.existsById(investmentId)) {
//                 throw new RuntimeException("Investment not found with ID: " + investmentId);
//             }
            
//             // Delete associated daily stock data first
//             dailyInvestmentStockRepository.deleteByInvestmentId(investmentId);
            
//             // Delete investment
//             investmentRepository.deleteById(investmentId);
            
//             log.info("Successfully deleted investment and associated daily stock data: {}", investmentId);
            
//         } catch (Exception e) {
//             log.error("Error deleting investment {}: {}", investmentId, e.getMessage(), e);
//             throw new RuntimeException("Failed to delete investment", e);
//         }
//     }

//     /**
//      * Update investments table with latest data from daily_investment_stocks
//      * This method should be called after saving daily stock data
//      */
//     @Transactional
//     public void updateInvestmentsFromDailyData(Integer userId) {
//         try {
//             log.info("Updating investments table from daily stock data for user: {}", userId);
            
//             if (userId == null || userId <= 0) {
//                 throw new IllegalArgumentException("Invalid user ID");
//             }
            
//             // Get all investments for the user
//             List<Investment> investments = investmentRepository.findByUser_Id(userId);
            
//             int updatedCount = 0;
            
//             for (Investment investment : investments) {
//                 try {
//                     // Get the latest daily stock data for this investment
//                     Optional<DailyInvestmentStock> latestDailyData = dailyInvestmentStockRepository
//                         .findTopByInvestmentIdOrderByDateDescTimestampDesc(investment.getId());
                    
//                     if (latestDailyData.isPresent()) {
//                         DailyInvestmentStock dailyStock = latestDailyData.get();
                        
//                         // Update investment with latest daily data
//                         boolean updated = false;
                        
//                         if (dailyStock.getPrice() != null && 
//                             !dailyStock.getPrice().equals(investment.getCurrentPrice())) {
//                             investment.setCurrentPrice(dailyStock.getPrice());
//                             updated = true;
//                         }
                        
//                         if (dailyStock.getQuantity() != null && 
//                             !dailyStock.getQuantity().equals(investment.getQuantity())) {
//                             investment.setQuantity(dailyStock.getQuantity());
//                             updated = true;
//                         }
                        
//                         if (dailyStock.getValue() != null && 
//                             !dailyStock.getValue().equals(investment.getValue())) {
//                             investment.setValue(dailyStock.getValue());
//                             updated = true;
//                         }
                        
//                         if (dailyStock.getTicker() != null && 
//                             !dailyStock.getTicker().equals(investment.getTicker())) {
//                             investment.setTicker(dailyStock.getTicker());
//                             updated = true;
//                         }
                        
//                         if (dailyStock.getSecurityType() != null && 
//                             !dailyStock.getSecurityType().equals(investment.getSecurityType())) {
//                             investment.setSecurityType(dailyStock.getSecurityType());
//                             updated = true;
//                         }
                        
//                         if (updated) {
//                             investment.setLastUpdated(LocalDateTime.now());
//                             // Assuming you have a calculateDerivedValues method in Investment entity
//                             // investment.calculateDerivedValues();
//                             investmentRepository.save(investment);
//                             updatedCount++;
                            
//                             log.debug("Updated investment {} with latest daily data", investment.getId());
//                         }
//                     }
                    
//                 } catch (Exception e) {
//                     log.error("Error updating investment {} from daily data: {}", 
//                         investment.getId(), e.getMessage());
//                 }
//             }
            
//             log.info("Updated {} investments from daily stock data for user: {}", updatedCount, userId);
            
//         } catch (Exception e) {
//             log.error("Error updating investments from daily data for user {}: {}", userId, e.getMessage(), e);
//             throw new RuntimeException("Failed to update investments from daily data", e);
//         }
//     }

//     /**
//      * Update single investment from its latest daily data
//      */
//     @Transactional
//     public void updateInvestmentFromLatestDailyData(Investment investment) {
//         try {
//             // Get the latest daily stock data for this investment
//             Optional<DailyInvestmentStock> latestDailyData = dailyInvestmentStockRepository
//                 .findTopByInvestmentIdOrderByDateDescTimestampDesc(investment.getId());
            
//             if (latestDailyData.isPresent()) {
//                 DailyInvestmentStock dailyStock = latestDailyData.get();
//                 boolean updated = false;
                
//                 // Update current price
//                 if (dailyStock.getPrice() != null && 
//                     !dailyStock.getPrice().equals(investment.getCurrentPrice())) {
//                     investment.setCurrentPrice(dailyStock.getPrice());
//                     updated = true;
//                 }
                
//                 // Update quantity
//                 if (dailyStock.getQuantity() != null && 
//                     !dailyStock.getQuantity().equals(investment.getQuantity())) {
//                     investment.setQuantity(dailyStock.getQuantity());
//                     updated = true;
//                 }
                
//                 // Update value
//                 if (dailyStock.getValue() != null && 
//                     !dailyStock.getValue().equals(investment.getValue())) {
//                     investment.setValue(dailyStock.getValue());
//                     updated = true;
//                 }
                
//                 if (updated) {
//                     investment.setLastUpdated(LocalDateTime.now());
//                     // investment.calculateDerivedValues();
//                     investmentRepository.save(investment);
                    
//                     log.debug("Auto-updated investment {} from latest daily data", investment.getId());
//                 }
//             }
            
//         } catch (Exception e) {
//             log.error("Error auto-updating investment {} from daily data: {}", 
//                 investment.getId(), e.getMessage());
//         }
//     }

//     /**
//      * Scheduled method to periodically sync daily data back to investments table
//      * Add this if you want automatic periodic updates
//      */
//     @Transactional
//     public void scheduledUpdateInvestmentsFromDailyData() {
//         try {
//             log.info("Starting scheduled update of investments from daily data");
            
//             // Get all users who have investments
//             List<Integer> userIds = investmentRepository.findDistinctUserIds();
            
//             for (Integer userId : userIds) {
//                 try {
//                     updateInvestmentsFromDailyData(userId);
//                 } catch (Exception e) {
//                     log.error("Error in scheduled update for user {}: {}", userId, e.getMessage());
//                 }
//             }
            
//             log.info("Completed scheduled update of investments from daily data");
            
//         } catch (Exception e) {
//             log.error("Error in scheduled update of investments from daily data: {}", e.getMessage(), e);
//         }
//     }
// }