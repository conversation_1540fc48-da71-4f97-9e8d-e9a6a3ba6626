package com.pennypal.fintech.entity;   

import jakarta.persistence.*; 
import lombok.Data; 
import java.time.LocalDateTime;  

@Data 
@Entity 
@Table(name = "user_relationships") 
public class UserRelationship {     
    @Id     
    @GeneratedValue(strategy = GenerationType.IDENTITY)     
    private int id;          
    
    @Column(name = "primary_user_id")     
    private int primaryUserId;          
    
    @Column(name = "secondary_user_id")     
    private Integer secondaryUserId;          
    
    @Column(name = "secondary_user_email")     
    private String secondaryUserEmail;          
    
    @Column(name = "relationship_type")     
    private String relationshipType;          
    
    @Column(name = "invite_token")     
    private String inviteToken;          
    
    @Column(name = "token_expires_at")     
    private LocalDateTime tokenExpiresAt;          
    
    @Column(name = "created_at")     
    private LocalDateTime createdAt;          
    
    @Column(name = "updated_at")     
    private LocalDateTime updatedAt;          
    
    @Column(name = "status")     
    private String status; // PENDING, ACTIVE, REJECTED
    
    @Column(name = "permission_type")
    private String permissionType; // READ_ONLY, WRITE 
}