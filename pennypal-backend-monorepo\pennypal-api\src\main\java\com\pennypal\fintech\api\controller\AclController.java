package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.service.AclService;

@RestController
@RequestMapping("/api/v1/permissions")
public class AclController {
    private final AclService aclService;

    public AclController(AclService aclService) {
        this.aclService = aclService;
    }

    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getPermissions(@PathVariable Integer userId) {
        List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(userId);
        Map<String, Object> response = new HashMap<>();
        response.put("user_id", userId);
        response.put("permissions", permissions);
        return ResponseEntity.ok(response);
    }
}