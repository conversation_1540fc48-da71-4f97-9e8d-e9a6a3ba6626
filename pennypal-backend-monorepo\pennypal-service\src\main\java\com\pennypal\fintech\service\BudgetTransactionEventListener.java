// package com.pennypal.fintech.service;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.context.event.EventListener;
// import org.springframework.scheduling.annotation.Async;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.annotation.Transactional;

// import com.pennypal.fintech.dto.TransactionDto;
// import com.pennypal.fintech.entity.Budget;
// import com.pennypal.fintech.entity.Category;
// import com.pennypal.fintech.entity.SubCategory;
// import com.pennypal.fintech.entity.Users;
// import com.pennypal.fintech.repository.BudgetRepository;
// import com.pennypal.fintech.repository.CategoryRepository;
// import com.pennypal.fintech.repository.SubCategoryRepository;
// import com.pennypal.fintech.repository.UserRepository;

// import java.math.BigDecimal;
// import java.time.LocalDate;
// import java.util.List;
// import java.util.Optional;

// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;

// // Create the TransactionEvent class since it can't be resolved
// class TransactionEvent {
//     private final List<TransactionDto> transactions;
    
//     public TransactionEvent(Object source, List<TransactionDto> transactions) {
//         this.transactions = transactions;
//     }
    
//     public List<TransactionDto> getTransactions() {
//         return transactions;
//     }
// }

// @Service
// public class BudgetTransactionEventListener {
//     private static final Logger logger = LoggerFactory.getLogger(BudgetTransactionEventListener.class);

//     @Autowired
//     private BudgetRepository budgetRepository;
    
//     @Autowired
//     private CategoryRepository categoryRepository;
    
//     @Autowired
//     private SubCategoryRepository subCategoryRepository;
    
//     @Autowired
//     private UserRepository userRepository;

//     /**
//      * Event listener to update budgets when new transactions are processed
//      * This method will be called asynchronously when a TransactionEvent is published
//      */
//     @EventListener
//     @Async
//     @Transactional
//     public String updateBudgetFromTransaction(TransactionEvent event) {
//         logger.info("Processing transaction event for budget updates");
        
//         // Get the list of new transactions from the event
//         List<TransactionDto> transactions = event.getTransactions();
        
//         if (transactions == null || transactions.isEmpty()) {
//             logger.warn("No transactions were provided in the event");
//             return "No transactions were provided";
//         }
        
//         int processedCount = 0;
        
//         // Process each transaction
//         for (TransactionDto transaction : transactions) {
//             try {
//                 boolean processed = processTransactionForBudget(transaction);
//                 if (processed) {
//                     processedCount++;
//                 }
//             } catch (Exception e) {
//                 logger.error("Error processing transaction ID: {} for budget", transaction.getId(), e);
//             }
//         }
        
//         logger.info("Successfully processed {}/{} transactions for budget updates", 
//                 processedCount, transactions.size());
        
//         return "Processed " + processedCount + " transactions for budget updates";
//     }
    
//     /**
//      * Process a single transaction and update the corresponding budget
//      * @param transaction The transaction to process
//      * @return true if processed successfully, false otherwise
//      */
//     private boolean processTransactionForBudget(TransactionDto transaction) {
//         logger.debug("Processing transaction: {}", transaction);
        
//         // Get the category for this transaction
//         // You might need to implement logic to determine the category
//         // based on the transaction description, merchant, or other attributes
//         String categoryName = determineCategoryFromTransaction(transaction);
        
//         if (categoryName == null) {
//             logger.debug("No category determined for transaction: {}", transaction.getId());
//             return false;
//         }
        
//         // Find the category in the database
//         Optional<Category> categoryOpt = categoryRepository.findByCategory(categoryName);
//         if (categoryOpt.isEmpty()) {
//             logger.warn("Category not found: {}", categoryName);
//             return false;
//         }
        
//         Category category = categoryOpt.get();
        
//         // Get the user
//         Optional<Users> userOpt = userRepository.findById(transaction.getUserId());
//         if (userOpt.isEmpty()) {
//             logger.warn("User not found with ID: {}", transaction.getUserId());
//             return false;
//         }
        
//         Users user = userOpt.get();
        
//         // Get current month and year for budget period
//         LocalDate transactionDate = transaction.getTransactionDate().toLocalDate();
//         LocalDate budgetMonth = LocalDate.of(transactionDate.getYear(), transactionDate.getMonth(), 1);
        
//         // Find existing budget for this user, category, and month
//         // Using existing method signature that your repository likely supports
//         List<Budget> existingBudgets = budgetRepository.findByUserId(user.getId());
//         Optional<Budget> existingBudgetOpt = existingBudgets.stream()
//             .filter(b -> b.getCategory().getId() == category.getId() && 
//                     (b.getDate() != null && b.getDate().getYear() == budgetMonth.getYear() && 
//                      b.getDate().getMonth() == budgetMonth.getMonth()))
//             .findFirst();
        
//         // Convert transaction amount to BigDecimal
//         BigDecimal transAmount = BigDecimal.valueOf(transaction.getTransactionAmount());
        
//         if (existingBudgetOpt.isPresent()) {
//             // Update existing budget
//             Budget budget = existingBudgetOpt.get();
            
//             // Add transaction amount to actual spending
//             if (budget.getActual() == null) {
//                 budget.setActual(transAmount);
//             } else {
//                 budget.setActual(budget.getActual().add(transAmount));
//             }
            
//             // Recalculate remaining budget
//             if (budget.getAllocated() != null) {
//                 budget.setRemaining(budget.getAllocated().subtract(budget.getActual()));
//             }
            
//             budgetRepository.save(budget);
//             logger.info("Updated budget ID: {} with transaction amount: {}", 
//                     budget.getId(), transAmount);
//         } else {
//             // Create new budget entry if it doesn't exist
//             Budget newBudget = new Budget();
//             newBudget.setUser(user);
//             newBudget.setCategory(category);
            
//             // Determine if there's a subcategory
//             String subCategoryName = determineSubCategoryFromTransaction(transaction);
//             if (subCategoryName != null) {
//                 // Modified to use a method your repository likely supports
//                 List<SubCategory> subCategories = subCategoryRepository.findByCategoryId(category.getId());
//                 Optional<SubCategory> subCategoryOpt = subCategories.stream()
//                     .filter(sc -> sc.getSubCategory().equalsIgnoreCase(subCategoryName))
//                     .findFirst();
                
//                 if (subCategoryOpt.isPresent()) {
//                     newBudget.setSubCategory(subCategoryOpt.get());
//                 } else {
//                     // If subcategory doesn't exist but we identified one, store as custom
//                     newBudget.setCustomSubCategory(subCategoryName);
//                 }
//             }
            
//             // Set default allocated amount (this would normally come from user preferences)
//             newBudget.setAllocated(BigDecimal.valueOf(1000.00)); // Default budget amount
//             newBudget.setActual(transAmount);
//             newBudget.setRemaining(newBudget.getAllocated().subtract(newBudget.getActual()));
//             newBudget.setDate(budgetMonth);
            
//             // Default values
//             newBudget.setIsDynamic(false);
//             newBudget.setRollover(false);
//             newBudget.setExclude(false);
            
//             budgetRepository.save(newBudget);
//             logger.info("Created new budget for category: {}, month: {}, with transaction amount: {}", 
//                     categoryName, budgetMonth, transAmount);
//         }
        
//         return true;
//     }
    
//     /**
//      * Determine the category from a transaction.
//      * This is a simplified implementation - in a real app, you'd have more
//      * sophisticated logic to categorize transactions.
//      */
//     private String determineCategoryFromTransaction(TransactionDto transaction) {
//         // This would be more sophisticated in a real app, using merchant data,
//         // transaction descriptions, or even ML models
        
//         String description = transaction.getDescription().toLowerCase();
        
//         // Simple logic to categorize based on transaction description
//         if (description.contains("walmart") || 
//             description.contains("target") || 
//             description.contains("grocery")) {
//             return "Groceries";
//         } else if (description.contains("restaurant") || 
//                   description.contains("cafe") || 
//                   description.contains("doordash")) {
//             return "Dining";
//         } else if (description.contains("netflix") || 
//                   description.contains("hulu") || 
//                   description.contains("spotify")) {
//             return "Entertainment";
//         } else if (description.contains("uber") || 
//                   description.contains("lyft") || 
//                   description.contains("gas")) {
//             return "Transportation";
//         } else if (description.contains("gamestop") ||
//                   description.contains("game")) {
//             return "Entertainment";
//         }
        
//         // Default category for unrecognized transactions
//         return "Miscellaneous";
//     }
    
//     /**
//      * Determine the subcategory from a transaction.
//      * This is a simplified implementation.
//      */
//     private String determineSubCategoryFromTransaction(TransactionDto transaction) {
//         String description = transaction.getDescription().toLowerCase();
        
//         // Simple logic for subcategories
//         if (description.contains("walmart") || description.contains("target")) {
//             return "Supermarket";
//         } else if (description.contains("netflix")) {
//             return "Streaming";
//         } else if (description.contains("uber") || description.contains("lyft")) {
//             return "Rideshare";
//         } else if (description.contains("gamestop")) {
//             return "Gaming";
//         }
        
//         // Return null if no subcategory identified
//         return null;
//     }
// }