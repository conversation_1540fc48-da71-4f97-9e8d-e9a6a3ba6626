package com.pennypal.fintech.api.controller;



import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.http.HttpStatus;

import com.pennypal.fintech.dto.UserDto;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.dto.SignInRequestDto;
import com.pennypal.fintech.service.UserService;
import com.pennypal.fintech.dto.UserPermissionDto;

import jakarta.persistence.EntityNotFoundException;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Controller
@RequestMapping("/api/v1/user")
public class UserController {

   @Autowired
   private final UserService  userService;

   public UserController(UserService userService) {
    this.userService = userService;
   }

    @GetMapping("/token")
    public String getAccessToken() throws Exception {
        return userService.getAccessToken();
    }

    @GetMapping ("/update/{emailId}/{accessToken}")
    public ResponseEntity<?>  updateAccessToken(@PathVariable String emailId, @PathVariable String accessToken) {

       boolean res =  userService.updateAccessToken(accessToken,emailId);
       String msg = "Updated";
       if(!res){
            msg = "Failed";
       }
       return ResponseEntity.ok(msg);
    }

    @GetMapping("/{emailId}")
    public ResponseEntity<?> getUserByEmailId(@PathVariable String emailId) {
        return ResponseEntity.ok(userService.fetchUserByEmailId(emailId));
    }
  @GetMapping("/profile/{userId}")
    public ResponseEntity<?> getUserById(@PathVariable Integer userId) {
        try {
            UserDto user = userService.fetchUserById(userId);
            if (user != null) {
                return ResponseEntity.ok(user);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            return ResponseEntity.internalServerError().body("Error fetching user profile: " + e.getMessage());
        }
    }

   @PutMapping("/profile/{id}")
public ResponseEntity<?> updateUser(@PathVariable Integer id,
                                    @Valid @RequestBody UserDto userDto) {
    try {
        userDto.setId(id);                     // keep path & body in sync
        UserDto updated = userService.updateUser(userDto);
        return ResponseEntity.ok(updated);     // 200 OK with updated data
    } catch (EntityNotFoundException ex) {     // e.g. JPA repository .orElseThrow()
        return ResponseEntity
                .status(HttpStatus.NOT_FOUND)  // 404 if id not found
                .body("User not found: " + id);
    } catch (Exception ex) {
        return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR) // 500 for anything else
                .body("Error updating user: " + ex.getMessage());
    }
}

 
 
      @GetMapping("/{id}")
    public ResponseEntity<String> getUser(@PathVariable Integer id) {
        Optional<Users> user =  userService.findById(id);
        return ResponseEntity.ok("User ID: " + user.get().getEmailId());
    }

    @PostMapping("/save")
    public ResponseEntity<UserDto> saveUser(@RequestBody UserDto userDto) {
        UserDto savedUserDto = userService.addUser(userDto);
        return ResponseEntity.ok(savedUserDto);
    }
    
    @PostMapping("/signIn/{emaiIld}")
    public ResponseEntity<UserDto> signIn(@PathVariable String emailId) {
        UserDto savedUserDto = userService.fetchUserByEmailId(emailId);
        return ResponseEntity.ok(savedUserDto);
    }
	//   @PostMapping("/signin")
	//   public ResponseEntity<String> signIn(@RequestBody SignInRequestDto signInRequestDto   ) {
	//       boolean isAuthenticated = userService.signIn(signInRequestDto.getEmailId());
  
	//       if (isAuthenticated) {
	//           return ResponseEntity.ok("Sign In Successful!");
	//       } else {
	//           return ResponseEntity.status(404).body("User not found. Please sign up.");
	//       }
	//   } 
	@PostMapping("/signin")
public ResponseEntity<UserDto> signin(@RequestBody UserDto userDto, HttpServletResponse response) {
    UserDto responseDto = userService.signInUser(userDto, response);
    // Add debugging to verify ID is still correct
    System.out.println("Controller returning user with ID: " + responseDto.getId());
    return ResponseEntity.ok(responseDto);
}

    @GetMapping("/permissions/{userId}")
public ResponseEntity<?> getUserPermissions(@PathVariable Integer userId) {
    // Fetch user entity by ID
    Users user = userService.findById(userId)
        .orElseThrow(() -> new RuntimeException("User not found"));
    // Get permissions for user (primary or secondary)
    List<UserPermissionDto> permissions = userService.getPermissionsForUser(user);
    return ResponseEntity.ok(permissions);
}
}
