package com.pennypal.fintech.api.config;

import com.pennypal.fintech.service.AppConfigService;
import com.stripe.Stripe;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

@Configuration
@Slf4j
public class StripeConfig {

    // @Value("${stripe.api-key}")
    // private String apiKey;

    private final AppConfigService appConfigService;

    public StripeConfig(AppConfigService appConfigService) {
        this.appConfigService = appConfigService;
    }

    // @PostConstruct
    @EventListener(ContextRefreshedEvent.class)
    public void setup() {
        // Stripe.apiKey = apiKey;
        String apiKey = appConfigService.getConfig("stripe_api-key");
        if (apiKey != null && !apiKey.isEmpty()) {
            Stripe.apiKey = apiKey;
        } else {
            throw new IllegalStateException("Stripe API key not found in configuration");
        }
        log.info("Stripe configs loaded");
    }

    @EventListener(AppConfigService.ConfigurationRefreshedEvent.class)
    public void reloadStripeConfigs(AppConfigService.ConfigurationRefreshedEvent event) {
        setup();
    }
}