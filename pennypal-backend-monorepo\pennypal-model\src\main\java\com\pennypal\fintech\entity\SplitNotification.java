package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "split_notification")
public class SplitNotification {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ManyToOne
    @JoinColumn(name = "split_transaction_id")
    private SplitTransaction splitTransaction;

    private String recipientName;
    private String recipientEmail;
    private String recipientPhone;

    @Column(length = 1000)
    private String message; // Added to store the notification message

    @Enumerated(EnumType.STRING)
    private NotificationType notificationType; // SMS, EMAIL, or BOTH

    private boolean emailSent;
    private boolean smsSent;
    private LocalDateTime sentAt;

    @Enumerated(EnumType.STRING)
    private NotificationStatus status; // SUCCESS, FAILED, PENDING

    public enum NotificationType {
        SMS, EMAIL, BOTH
    }

    public enum NotificationStatus {
        SUCCESS, FAILED, PENDING
    }

    public SplitNotification() {
        this.sentAt = LocalDateTime.now();
        this.status = NotificationStatus.PENDING;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public SplitTransaction getSplitTransaction() {
        return splitTransaction;
    }

    public void setSplitTransaction(SplitTransaction splitTransaction) {
        this.splitTransaction = splitTransaction;
    }

    public String getRecipientName() {
        return recipientName;
    }

    public void setRecipientName(String recipientName) {
        this.recipientName = recipientName;
    }

    public String getRecipientEmail() {
        return recipientEmail;
    }

    public void setRecipientEmail(String recipientEmail) {
        this.recipientEmail = recipientEmail;
    }

    public String getRecipientPhone() {
        return recipientPhone;
    }

    public void setRecipientPhone(String recipientPhone) {
        this.recipientPhone = recipientPhone;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public NotificationType getNotificationType() {
        return notificationType;
    }

    public void setNotificationType(NotificationType notificationType) {
        this.notificationType = notificationType;
    }

    public boolean isEmailSent() {
        return emailSent;
    }

    public void setEmailSent(boolean emailSent) {
        this.emailSent = emailSent;
    }

    public boolean isSmsSent() {
        return smsSent;
    }

    public void setSmsSent(boolean smsSent) {
        this.smsSent = smsSent;
    }

    public LocalDateTime getSentAt() {
        return sentAt;
    }

    public void setSentAt(LocalDateTime sentAt) {
        this.sentAt = sentAt;
    }

    public NotificationStatus getStatus() {
        return status;
    }

    public void setStatus(NotificationStatus status) {
        this.status = status;
    }
}