package com.pennypal.fintech.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.pennypal.fintech.service.SplitTransactionService;

import com.pennypal.fintech.dto.SplitTransactionDto;
import com.pennypal.fintech.entity.SplitTransaction;

@RestController
@RequestMapping("/api/split-transactions")
public class SplitTransactionController {
    @Autowired
    private SplitTransactionService splitTransactionService;

 @PostMapping("/add")
public ResponseEntity<List<SplitTransactionDto>> createSplitTransactions(@RequestBody List<SplitTransactionDto> dtoList) {
    List<SplitTransaction> createdTransactions = splitTransactionService.createSplitTransactions(dtoList);

    List<SplitTransactionDto> response = createdTransactions.stream().map(entity -> {
        SplitTransactionDto dto = new SplitTransactionDto();
        dto.setId(entity.getId());
        dto.setAmount(entity.getAmount());
        dto.setDate(entity.getDate());
        dto.setNotesDesc(entity.getNotesDesc());
        dto.setUserId(entity.getUser() != null ? entity.getUser().getId() : null);
        dto.setTransactionId(entity.getTransaction() != null ? entity.getTransaction().getId() : null);
        dto.setUserContactId(entity.getUserContact() != null ? entity.getUserContact().getId() : null);
        return dto;
    }).toList();

    return ResponseEntity.ok(response);
}

     @GetMapping("/user/{userId}")
    public ResponseEntity<List<SplitTransactionDto>> getSplitTransactionsByUserId(@PathVariable Integer userId) {
        List<SplitTransactionDto> splitTransactions = splitTransactionService.getSplitTransactionsByUserId(userId);
        return ResponseEntity.ok(splitTransactions);
    }
}
