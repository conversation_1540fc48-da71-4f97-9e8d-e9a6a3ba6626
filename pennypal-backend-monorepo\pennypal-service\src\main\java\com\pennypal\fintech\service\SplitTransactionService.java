package com.pennypal.fintech.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import com.pennypal.fintech.repository.SplitNotificationRepository;
import com.pennypal.fintech.repository.SplitTransactionRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserContactRepository;
import com.pennypal.fintech.repository.UserRepository;

import jakarta.transaction.Transactional;

import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.entity.UserContact;

import com.pennypal.fintech.dto.SplitTransactionDto;
import com.pennypal.fintech.entity.SplitNotification;
import com.pennypal.fintech.entity.SplitTransaction;

@Slf4j
@Service
public class SplitTransactionService {
      @Autowired
    private SplitTransactionRepository splitTransactionRepository;

    @Autowired
    private UserRepository userRepository; // Repository for User entity

    @Autowired
    private TransactionRepository transactionRepository; // Repository for Transaction entity

    @Autowired
    private UserContactRepository userContactRepository;
    
    @Autowired
    private TwilioService twilioService;
    
    @Autowired
    private SplitNotificationRepository splitNotificationRepository;

    @Cacheable(value = "splitTransactionCache",
               key = "#root.methodName" + "_" + "#userId",
               unless = "#result == null")
    @Transactional
    public List<SplitTransactionDto> getSplitTransactionsByUserId(Integer userId) {
        List<SplitTransaction> transactions = splitTransactionRepository.findByUserId(userId);
        return transactions.stream().map(transaction -> {
            SplitTransactionDto dto = new SplitTransactionDto();
            dto.setId(transaction.getId());
            dto.setAmount(transaction.getAmount());
            dto.setDate(transaction.getDate());
            dto.setNotesDesc(transaction.getNotesDesc());
            // Make sure to also set the split method from the transaction
            // dto.setSplitMethod(transaction.getSplitMethod()); // Uncomment this if you have the field
            
            // Set the userContactId as well
            if (transaction.getUserContact() != null) {
                dto.setUserContactId(transaction.getUserContact().getId());
                dto.setUserContactName(transaction.getUserContact().getName());
            } else {
                dto.setUserContactName("Unknown Contact");
            }
            
            return dto;
        }).collect(Collectors.toList());
    }

    @CacheEvict(value = "splitTransactionCache",
                key = "getSplitTransactionsByUserId_" + "#dto.getUserId()")
  @Transactional
    public List<SplitTransaction> createSplitTransactions(List<SplitTransactionDto> dtos) {
        List<SplitTransaction> createdTransactions = new ArrayList<>();

        for (SplitTransactionDto dto : dtos) {
            SplitTransaction splitTransaction = new SplitTransaction();
            BigDecimal amount = dto.getAmount();
            if ("owedToMe".equals(dto.getSplitMethod())) {
                splitTransaction.setAmount(amount.abs());
            } else if ("owedToOther".equals(dto.getSplitMethod())) {
                splitTransaction.setAmount(amount.abs().negate());
            } else {
                splitTransaction.setAmount(amount);
            }
            splitTransaction.setDate(dto.getDate());
            splitTransaction.setNotesDesc(dto.getNotesDesc());

            Users user = userRepository.findById(dto.getUserId())
                    .orElseThrow(() -> new RuntimeException("User not found"));
            Transactions transaction = transactionRepository.findById(dto.getTransactionId())
                    .orElseThrow(() -> new RuntimeException("Transaction not found"));
            UserContact userContact = userContactRepository.findById(dto.getUserContactId())
                    .orElseThrow(() -> new RuntimeException("UserContact not found"));

            splitTransaction.setUser(user);
            splitTransaction.setTransaction(transaction);
            splitTransaction.setUserContact(userContact);

            SplitTransaction savedTransaction = splitTransactionRepository.save(splitTransaction);
            createdTransactions.add(savedTransaction);

            // Send SMS notification
            sendNotifications(savedTransaction, userContact);
        }

        return createdTransactions;
    }
    
   private void sendNotifications(SplitTransaction splitTransaction, UserContact userContact) {
        SplitNotification notification = new SplitNotification();
        notification.setSplitTransaction(splitTransaction);
        notification.setRecipientName(userContact.getName());
        notification.setRecipientPhone(userContact.getPhoneNumber());
        notification.setRecipientEmail(userContact.getEmailId());
        notification.setNotificationType(SplitNotification.NotificationType.SMS);

        try {
            // Construct the SMS message
            String message = String.format(
                "Hi %s, %s has requested a split payment of $%s for %s.",
                userContact.getName(),
                splitTransaction.getUser().getName(), // Assuming Users entity has a getName() method
                splitTransaction.getAmount().abs().toString(),
                splitTransaction.getNotesDesc()
            );
            notification.setMessage(message);

            // Check if the user contact has a phone number
            String phoneNumber = userContact.getPhoneNumber();
            if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
                // Send SMS using TwilioService
                twilioService.sendSms(phoneNumber, message);
                notification.setSmsSent(true);
                notification.setStatus(SplitNotification.NotificationStatus.SUCCESS);
                log.info("SMS sent to {} for split transaction ID {}", phoneNumber, splitTransaction.getId());
            } else {
                notification.setSmsSent(false);
                notification.setStatus(SplitNotification.NotificationStatus.FAILED);
                log.warn("No phone number available for user contact ID {}", userContact.getId());
            }
        } catch (Exception e) {
            notification.setSmsSent(false);
            notification.setStatus(SplitNotification.NotificationStatus.FAILED);
            notification.setMessage(notification.getMessage() + " | Error: " + e.getMessage());
            log.error("Failed to send SMS notification for split transaction ID {}: {}", 
                splitTransaction.getId(), e.getMessage());
        }

        // Save the notification record
        splitNotificationRepository.save(notification);
    }
}