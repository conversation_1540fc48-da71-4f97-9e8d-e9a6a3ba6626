package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Goal;
import com.pennypal.fintech.entity.RecurringContribution;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface RecurringContributionRepository extends JpaRepository<RecurringContribution, Integer> {

    List<RecurringContribution> findByGoal(Goal goal);
    
    @Query("SELECT rc FROM RecurringContribution rc WHERE rc.goal.user.id = ?1")
    List<RecurringContribution> findByUserId(Integer userId);
    
    List<RecurringContribution> findByStatusAndNextContributionDateLessThanEqual(
            RecurringContribution.ContributionStatus status, LocalDate date);
    
    @Query("SELECT COUNT(rc) FROM RecurringContribution rc WHERE rc.goal.user.id = ?1 AND rc.status = ?2")
    Integer countByUserIdAndStatus(Integer userId, RecurringContribution.ContributionStatus status);
    
    @Query("SELECT SUM(rc.amount) FROM RecurringContribution rc WHERE rc.goal.id = ?1 AND rc.status = ?2")
    Double sumMonthlyContributionsByGoalId(Integer goalId, RecurringContribution.ContributionStatus status);
    
    @Query("SELECT rc FROM RecurringContribution rc WHERE rc.account.id = ?1")
    List<RecurringContribution> findByAccountId(Integer accountId);
}