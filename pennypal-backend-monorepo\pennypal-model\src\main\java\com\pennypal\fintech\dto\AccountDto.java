package com.pennypal.fintech.dto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class AccountDto {
    private List<Map<String, Object>> aggregatedBalances = new ArrayList<>();
    private int id;  // If you want to keep it for internal tracking
     private boolean authenticationRequired; 
    private String accountName;
    private String accountId;
    
    private String accountType;
    private String accountCategory;
    private double balance;
    private String currencyType;
    private String accountMask;
    private LocalDateTime insertDatetime;
    private LocalDateTime updateDatetime;
    private String accountSubtype;
    private String institutionId;
    private Double availableBalance;
    private String financialInstName;
    private long syncId;
    private String accountNumber;  // Masked account number
    private LocalDateTime lastSyncTime;
    
    public LocalDateTime getLastSyncTime() {
        return lastSyncTime;
    }
    
    public void setLastSyncTime(LocalDateTime lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }
    
    // Add aggregated balances to the account
    public void setAggregatedBalances(List<Map<String, Object>> balances) {
        this.aggregatedBalances = balances != null ? balances : new ArrayList<>();
    }
    
    // Get aggregated balances
    public List<Map<String, Object>> getAggregatedBalances() {
        return this.aggregatedBalances;
    }
    
    // Add a single aggregated balance entry
    public void addAggregatedBalance(Map<String, Object> balance) {
        if (balance != null) {
            this.aggregatedBalances.add(balance);
        }
    }
    
    // Masking account number before setting it
    public void setAccountNumber(String plaidUniqueNo) {
        if (plaidUniqueNo != null && plaidUniqueNo.length() > 4) {
            String maskedAccountNumber = plaidUniqueNo.substring(0, 4)
                                        + "****"
                                        + plaidUniqueNo.substring(plaidUniqueNo.length() - 4);
            this.accountNumber = maskedAccountNumber;
        } else {
            this.accountNumber = plaidUniqueNo;  // If it's too short, no masking
        }
    }
    
    @Override
    public String toString() {
        return "AccountDto{" +
                "id=" + id +
             
                ", accountName='" + accountName + '\'' +
                ", accountType='" + accountType + '\'' +
                ", accountCategory='" + accountCategory + '\'' +
                ", balance=" + balance +
                ", currencyType='" + currencyType + '\'' +
                ", accountMask='" + accountMask + '\'' +
                ", insertDatetime=" + insertDatetime +
                ", updateDatetime=" + updateDatetime +
                ", accountSubtype='" + accountSubtype + '\'' +
                ", institutionId='" + institutionId + '\'' +
                ", availableBalance=" + availableBalance +
                ", financialInstName='" + financialInstName + '\'' +
                ", syncId=" + syncId +
                ", aggregatedBalances size=" + (aggregatedBalances != null ? aggregatedBalances.size() : 0) +
                '}';
    }
}
