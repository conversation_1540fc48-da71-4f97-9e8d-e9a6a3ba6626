package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Notification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notification, Integer> {
    // List<Notification> findByUserIdOrderByCreateTimestampDesc(Integer userId);
    @Query(value = """
        SELECT n.*, svg.svg_content FROM notifications n
        JOIN svgicons svg ON n.severity = svg.description
        WHERE user_id = :userId
        ORDER BY create_timestamp desc
    """, nativeQuery = true)
    List<Object[]> findByUserIdOrderByCreateTimestampDesc(Integer userId);
    Notification findByNotificationId(Integer notificationId);
    void deleteByUserId(Integer userId);
    void deleteByNotificationId(Integer notificationId);
}