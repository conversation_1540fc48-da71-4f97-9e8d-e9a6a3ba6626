package com.pennypal.fintech.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.StripeInvoice;

@Repository
public interface StripeInvoiceRepository extends JpaRepository<StripeInvoice, Integer> {
    List<StripeInvoice> findByUserId(Integer userId);
    List<StripeInvoice> findByCustomerId(String customerId);

    @Query(value = """
            SELECT * FROM stripe_invoice WHERE subscription_id in
            (SELECT subscription_id FROM stripe_subscription
             WHERE user_id = :userId AND status IN ('active', 'trialing'))
            """, nativeQuery = true)
    List<StripeInvoice> findByUserIdActiveSubscription(Integer userId);
}