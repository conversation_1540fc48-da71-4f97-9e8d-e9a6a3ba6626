package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.CustomChartDto;
import com.pennypal.fintech.entity.CustomChart;
import com.pennypal.fintech.service.CustomChartService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;

import jakarta.validation.Valid;
import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/charts")
@RequiredArgsConstructor
@Slf4j
public class CustomChartController {

    @Autowired
    private CustomChartService customChartService;

    @Operation(summary = "Create a new custom chart",
            description = "Creates a new custom chart with the provided data")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Chart created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid request data"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/create")
    public CustomChart createChart(
        @Parameter(description = "Custom chart data to be created", required = true)
        @Valid @RequestBody CustomChartDto chartDTO) {
        log.info("Inside createChart method of CustomChartController");
        log.info("Received CustomChartDto: " + chartDTO);
        try {
            return customChartService.create(chartDTO);
        } catch (Exception e) {
            throw new RuntimeException("Error creating chart: " + e.getMessage());
        }        
    }

    @Operation(summary = "Get custom charts by user",
            description = "Retrieves all custom charts for a specific user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved charts"),
            @ApiResponse(responseCode = "400", description = "Invalid user ID"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/user/{userId}")
    public List<CustomChart> getByUser(
        @Parameter(description = "ID of the user to get charts for", required = true)
        @PathVariable Integer userId) {
        log.info("Inside getByUser method of CustomChartController");
        log.info("User ID: " + userId);
        try {
            if (userId == null) {
                throw new IllegalArgumentException("User ID is required");
            }
            return customChartService.getByUserId(userId);
        } catch (Exception e) {
            throw new RuntimeException("Error fetching charts: " + e.getMessage());
        }
    }

    @Operation(summary = "Get custom charts by chat",
            description = "Retrieves all custom charts for a specific chat")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved charts"),
            @ApiResponse(responseCode = "400", description = "Invalid chat ID"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/chat/{chatId}")
    public List<CustomChart> getByChat(
        @Parameter(description = "ID of the chat to get charts for", required = true)
        @PathVariable Integer chatId) {
        log.info("Inside getByChat method of CustomChartController");
        log.info("Chat ID: " + chatId);
        try {
            if (chatId == null) {
                throw new IllegalArgumentException("Chat ID is required");
            }
            return customChartService.getByChatId(chatId);
        } catch (Exception e) {
            throw new RuntimeException("Error fetching charts: " + e.getMessage());
        }
    }

    @Operation(summary = "Get custom charts by user and chat",
            description = "Retrieves all custom charts for a specific user and chat")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved charts"),
            @ApiResponse(responseCode = "400", description = "Invalid user ID or chat ID"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/user/{userId}/chat/{chatId}")
    public List<CustomChart> getByUserAndChat(
        @Parameter(description = "ID of the user to get charts for", required = true)
        @RequestParam Integer userId,
        @Parameter(description = "ID of the chat to get charts for", required = true)
        @RequestParam Integer chatId) {
        log.info("Inside getByUserAndChat method of CustomChartController");
        log.info("User ID: " + userId);
        log.info("Chat ID: " + chatId);
        try {
            if (userId == null) {
                throw new IllegalArgumentException("User ID is required");
            }
            if (chatId == null) {
                throw new IllegalArgumentException("Chat ID is required");
            }
            return customChartService.getByUserAndChat(userId, chatId);
        } catch (Exception e) {
            throw new RuntimeException("Error fetching charts: " + e.getMessage());
        }
    }

    @Operation(summary = "Delete a custom chart",
            description = "Deletes a custom chart with the provided ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Chart deleted successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid chart ID"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @DeleteMapping("/delete/{id}")
    public void deleteChart(
        @Parameter(description = "ID of the chart to delete", required = true)
        @PathVariable Long id) {
        log.info("Inside deleteChart method of CustomChartController");
        log.info("Chart ID: " + id);
        try {
            if (id == null) {
                throw new IllegalArgumentException("Chart ID is required");
            }
            customChartService.deleteById(id);
        } catch (Exception e) {
            throw new RuntimeException("Error deleting chart: " + e.getMessage());
        }
    }

    @Operation(summary = "Update a custom chart",
            description = "Updates a custom chart with the provided ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Chart updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid chart ID"),
            @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PutMapping("/update/{id}")
    public CustomChart updateChart(
        @Parameter(description = "ID of the chart to update", required = true)
        @PathVariable Long id,
        @Parameter(description = "Updated chart data", required = true)
        @RequestBody CustomChartDto chartDTO) {
        log.info("Inside updateChart method of CustomChartController");
        log.info("Chart ID: " + id);
        log.info("Updated chart data: " + chartDTO);
        try {
            if (id == null) {
                throw new IllegalArgumentException("Chart ID is required");
            }
            return customChartService.update(id, chartDTO);
        } catch (Exception e) {
            throw new RuntimeException("Error updating chart: " + e.getMessage());
        }
    }
}