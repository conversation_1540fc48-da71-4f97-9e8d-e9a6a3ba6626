package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;

@Entity
@Data
@Table(name = "receipt_consolidate")
public class ReceiptConsolidate {
   
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "receipt_id")
    private int receiptId;

    @Column(name = "receipt_item")
    private String receiptItem;

    @Column(name = "price")
    private BigDecimal price;

    @Column(name = "user_id")
    private Long userId;
}
