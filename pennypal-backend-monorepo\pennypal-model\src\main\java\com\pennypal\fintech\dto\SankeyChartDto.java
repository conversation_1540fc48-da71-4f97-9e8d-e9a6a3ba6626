package com.pennypal.fintech.dto;

import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
public class SankeyChartDto {
    private List<Node> nodes;
    private List<Link> links;
    private Map<String, Integer> nodeLevels;

    @Data
    public static class Node {
        private String id;
        private String name;
        private Integer level;
		private String category;
    }

    @Data
    public static class Link {
        private String source;
        private String target;
        private Double value;
    }
}