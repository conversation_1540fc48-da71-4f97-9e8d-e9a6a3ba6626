package com.pennypal.fintech.dto;

public class TickerSummaryDto {
    private Double totalInvestedAmount;
    private Double totalCurrentValue;
    private Double overallGainLoss;
    private Double overallGainLossPercent;
    private String overallStatus;
    private Integer dataPoints;

    public TickerSummaryDto() {}

    public TickerSummaryDto(Double totalInvestedAmount, Double totalCurrentValue, Integer dataPoints) {
        this.totalInvestedAmount = totalInvestedAmount;
        this.totalCurrentValue = totalCurrentValue;
        this.dataPoints = dataPoints;
        calculateOverallGainLoss();
    }

    private void calculateOverallGainLoss() {
        this.overallGainLoss = totalCurrentValue - totalInvestedAmount;
        this.overallGainLossPercent = totalInvestedAmount > 0 ? (overallGainLoss / totalInvestedAmount) * 100 : 0.0;
        this.overallStatus = overallGainLoss >= 0 ? "GAIN" : "LOSS";
    }

    public Double getTotalInvestedAmount() { return totalInvestedAmount; }
    public void setTotalInvestedAmount(Double totalInvestedAmount) { this.totalInvestedAmount = totalInvestedAmount; }

    public Double getTotalCurrentValue() { return totalCurrentValue; }
    public void setTotalCurrentValue(Double totalCurrentValue) { this.totalCurrentValue = totalCurrentValue; }

    public Double getOverallGainLoss() { return overallGainLoss; }
    public void setOverallGainLoss(Double overallGainLoss) { this.overallGainLoss = overallGainLoss; }

    public Double getOverallGainLossPercent() { return overallGainLossPercent; }
    public void setOverallGainLossPercent(Double overallGainLossPercent) { this.overallGainLossPercent = overallGainLossPercent; }

    public String getOverallStatus() { return overallStatus; }
    public void setOverallStatus(String overallStatus) { this.overallStatus = overallStatus; }

    public Integer getDataPoints() { return dataPoints; }
    public void setDataPoints(Integer dataPoints) { this.dataPoints = dataPoints; }
}
