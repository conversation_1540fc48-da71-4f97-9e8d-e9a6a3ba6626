package com.pennypal.fintech.dto;


import lombok.Data;
import jakarta.validation.constraints.NotBlank;

@Data
public class DeleteAccountDto {
    private Integer userId;
    
    @NotBlank(message = "Password is required")
    private String password;
    
    private String twoFactorCode; // For 2FA verification
    private String reason; // Optional deletion reason
    private Boolean confirmDeletion;
}