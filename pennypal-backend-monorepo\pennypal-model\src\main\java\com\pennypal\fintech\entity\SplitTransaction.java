package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Column;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ForeignKey;
import com.pennypal.fintech.entity.Users; 

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonIgnore;

import org.hibernate.engine.jdbc.connections.internal.UserSuppliedConnectionProviderImpl;
import org.springframework.boot.autoconfigure.security.SecurityProperties.User;

@Entity
@Table(name = "split_transaction")
public class SplitTransaction {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @JsonIgnore // Prevents serialization of the User object
    private Users user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transaction_id")
    @JsonIgnore
    private Transactions transactions;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_contact_id")
    @JsonIgnore
    private UserContact userContact;

    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "date")
    private LocalDateTime date;

    @Column(name = "notes_desc")
    private String notesDesc;

    // Default constructor
    public SplitTransaction() {
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

     public Users getUser() {
        return user;
    }

    public void setUser(Users user) {
        this.user = user;
    }


    public Transactions getTransaction() {
        return transactions;
    }

    public void setTransaction(Transactions transaction) {
        this.transactions = transaction;
    }

    public UserContact getUserContact() {
        return userContact;
    }

    public void setUserContact(UserContact userContact) {
        this.userContact = userContact;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public String getNotesDesc() {
        return notesDesc;
    }

    public void setNotesDesc(String notesDesc) {
        this.notesDesc = notesDesc;
    }

    @Override
    public String toString() {
        return "SplitTransaction{" +
                "id=" + id +
            ", user=" + (user != null ? user.getId() : "null") +
                ", transaction=" + transactions.getId() + // Assuming Transaction has getId() method
                ", userContact=" + userContact.getId() + // Assuming UserContact has getId() method
                ", amount=" + amount +
                ", date=" + date +
                ", notesDesc='" + notesDesc + '\'' +
                '}';
    }
}

