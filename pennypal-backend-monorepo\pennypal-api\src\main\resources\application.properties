spring.application.name=pennypal-api

server.servlet.context-path=/pennypal



spring.datasource.url=*******************************************************************************
spring.datasource.username=dev
spring.datasource.password=@PennyPal25

#spring.datasource.url=***************************************
#spring.datasource.username=root
#spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.hibernate.ddl-auto=update  # Or 'create', 'validate', 'none' based on your requirement
spring.jpa.show-sql=true
plaid.client_id=67655adcb9d9be001afdfdbb
plaid.secret=8d9e072acc1e07480668af914ac95b
#spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
spring.jpa.properties.hibernate.format_sql=true
# Enable or disable scheduled refresh (true by default)
pennypal.scheduling.enabled=true

# Cron expression for refresh schedule (default is 2 AM and 2 PM daily)
# This can be modified based on requirements
pennypal.scheduling.cron=0 0 2,14 * * *

# Number of concurrent refresh tasks (default is 5)
pennypal.scheduling.pool-size=5


spring.jpa.hibernate.ddl-auto=update
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect

# Logging configuration
logging.level.root=INFO
logging.level.com.pennypal.fintech=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}

# Enable ANSI output
spring.output.ansi.enabled=ALWAYS

spring.jpa.properties.hibernate.use_sql_comments=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload-dir=C:/Users/<USER>/uploads

# Swagger UI custom path
springdoc.swagger-ui.path=/swagger-ui.html
# Include all endpoints in documentation
springdoc.api-docs.path=/v3/api-docs
springdoc.packages-to-scan=com.pennypal.fintech.api.controller
springdoc.swagger-ui.tryItOutEnabled=true


# Cache Configuration
cache.type=memory
spring.cache.type=caffeine
spring.cache.cache-names=transactionCache,userCache,aggregatedExpensesCache
spring.cache.caffeine.spec=maximumSize=1000,expireAfterWrite=3600s
# Switch ON when we need to use redis
# spring.profiles.active=redis

# Logging configuration
logging.level.root=INFO
logging.level.com.pennypal=DEBUG
logging.level.org.springframework.cache=TRACE

# Log pattern with timestamp
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n

# Log to file (optional)
logging.file.name=logs/pennypal.log

# Configure console output
spring.main.banner-mode=console
finicity.partnerId=*************
finicity.secret=WMz2za2dRYLBpI38BC2b
finicity.appKey=32c407c2682408f29b798be8eefbdff5
finicity.baseUrl=https://api.finicity.com

document.location.base.path=/Users/<USER>/AppDev/Offshore/pennypal/documents

# Stripe
stripe.api-key=sk_test_51RClAOQ8d4Ag6P7Hp64Q7TJbrAQ8D635pJswJYJFL9BMdNMroNKtteaEAJUvUHwAlKsidvOHRxBmh0Px5QBcbeKi00YKVGtw7v

# Stripe Webhook
stripe.webhook.secret=whsec_fd49ff6dd1ccb1e1c883e04eb63ac13f8fd7790b9cae2a4b34763ab5a93f3948

# Twilio
twilio.account.sid=**********************************
twilio.auth.token=d422365d8ac7401e9f73c4143d6e1ad3
twilio.phone.number=+***********
twilio.whatsapp.number=+***********

# Kafka Configuration
spring.kafka.bootstrap-servers=localhost:9092
spring.kafka.consumer.group-id=stripe-group
spring.kafka.consumer.auto-offset-reset=earliest
spring.kafka.consumer.key-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.consumer.value-deserializer=org.apache.kafka.common.serialization.StringDeserializer
spring.kafka.producer.key-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.producer.value-serializer=org.apache.kafka.common.serialization.StringSerializer
spring.kafka.enabled=false
# Mail Properties
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=orjqrmyfsukzcnxc
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.DEBUG=true

# JWT Secret Key
jwt.secret=MotfC4B6zullKDzWlQlkKK7HDO/xODEwvxUrH1epxs8=

# MX
mx.base-url=https://int-api.mx.com
mx.client-id=afe8f53c-7f88-4034-8992-5a18b380597e
mx.api-key=ae5b646762a6e2e8cc5226cf438434e8673d7419


logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.level.org.springframework.http.converter.json=DEBUG
logging.level.org.springframework.web=DEBUG
