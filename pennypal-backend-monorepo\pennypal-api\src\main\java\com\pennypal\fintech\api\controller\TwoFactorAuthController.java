package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ApiResponseDto;
import com.pennypal.fintech.dto.TwoFactorAuthDto;
import com.pennypal.fintech.service.TwoFactorAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/auth/2fa")
@CrossOrigin
public class TwoFactorAuthController {
    
    @Autowired
    private TwoFactorAuthService twoFactorAuthService;
    
    @PostMapping("/setup")
    public ResponseEntity<ApiResponseDto> setupTwoFactorAuth(@RequestParam Integer userId) {
        try {
            TwoFactorAuthDto result = twoFactorAuthService.setupTwoFactorAuth(userId);
            return ResponseEntity.ok(new ApiResponseDto(true, "2FA setup successful", result));
        } catch (Exception e) {
            log.error("Error setting up 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/enable")
    public ResponseEntity<ApiResponseDto> enableTwoFactorAuth(
            @RequestParam Integer userId,
            @RequestParam String verificationCode) {
        try {
            boolean result = twoFactorAuthService.enableTwoFactorAuth(userId, verificationCode);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "2FA enabled successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Invalid verification code"));
            }
        } catch (Exception e) {
            log.error("Error enabling 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/disable")
    public ResponseEntity<ApiResponseDto> disableTwoFactorAuth(
            @RequestParam Integer userId,
            @RequestParam String verificationCode) {
        try {
            boolean result = twoFactorAuthService.disableTwoFactorAuth(userId, verificationCode);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "2FA disabled successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Invalid verification code"));
            }
        } catch (Exception e) {
            log.error("Error disabling 2FA for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/verify")
    public ResponseEntity<ApiResponseDto> verifyTwoFactorCode(
            @RequestParam Integer userId,
            @RequestParam String code) {
        try {
            boolean result = twoFactorAuthService.verifyTwoFactorCode(userId, code);
            return ResponseEntity.ok(new ApiResponseDto(result, 
                    result ? "Code verified successfully" : "Invalid verification code"));
        } catch (Exception e) {
            log.error("Error verifying 2FA code for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<ApiResponseDto> getTwoFactorStatus(@RequestParam Integer userId) {
        try {
            TwoFactorAuthDto status = twoFactorAuthService.getTwoFactorStatus(userId);
            return ResponseEntity.ok(new ApiResponseDto(true, "Status retrieved successfully", status));
        } catch (Exception e) {
            log.error("Error getting 2FA status for user: {}", userId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
}