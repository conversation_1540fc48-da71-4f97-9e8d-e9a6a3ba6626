package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.FinicityService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/finicity")
public class FinicityController {
    private static final Logger logger = LoggerFactory.getLogger(FinicityController.class);

    private final FinicityService finicityService;

    public FinicityController(FinicityService finicityService) {
        this.finicityService = finicityService;
    }

    @PostMapping("/authenticate")
    public ResponseEntity<String> authenticate() {
        logger.info("Authentication endpoint called");
        System.out.println("Authentication endpoint called - Console Output");
        System.err.println("Authentication endpoint called - Error Output");
        
        try {
            System.out.println("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@");
            logger.info("Calling Finicity authentication service");
            String token = finicityService.authenticate();
            logger.info("Authentication successful");
            return ResponseEntity.ok(token);
        } catch (Exception e) {
            logger.error("Authentication failed: " + e.getMessage(), e);
            System.err.println("Authentication error: " + e.getMessage());
            throw e;
        }
    }

    @GetMapping("/accounts/{customerId}")
    public ResponseEntity<String> getCustomerAccounts(@PathVariable String customerId) {
        logger.info("Getting accounts for customer: {}", customerId);
        String accounts = finicityService.getCustomerAccounts(customerId);
        return ResponseEntity.ok(accounts);
    }

    @GetMapping("/transactions/{customerId}/{accountId}")
    public ResponseEntity<String> getCustomerTransactions(
            @PathVariable String customerId,
            @PathVariable String accountId,
            @RequestParam long fromDate,
            @RequestParam long toDate) {
        logger.info("Getting transactions for customer: {}, account: {}", customerId, accountId);
        String transactions = finicityService.getCustomerTransactions(customerId, accountId, fromDate, toDate);
        return ResponseEntity.ok(transactions);
    }

    // Test endpoint to verify logging
    @GetMapping("/test-logs")
    public ResponseEntity<String> testLogs() {
        System.out.println("Test endpoint - Console output");
        System.err.println("Test endpoint - Error output");
        logger.info("Test endpoint - Logger info");
        logger.error("Test endpoint - Logger error");
        return ResponseEntity.ok("Logging test completed - check your console and logs");
    }
}
