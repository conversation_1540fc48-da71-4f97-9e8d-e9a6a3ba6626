package com.pennypal.fintech.dto;


import java.time.LocalDate;


public class AggregatedStockDataDto  {
       private LocalDate groupEndDate;
    private Double avgPrice;
    private Double totalQuantity;
    private Double avgCostBasis;
    private Double totalCostBasis;
    
    // Constructor that handles different date types
    public AggregatedStockDataDto(Object groupEndDate, Double avgPrice, Double totalQuantity, 
                                  Double avgCostBasis, Double totalCostBasis) {
        this.groupEndDate = parseDate(groupEndDate);
        this.avgPrice = avgPrice;
        this.totalQuantity = totalQuantity;
        this.avgCostBasis = avgCostBasis;
        this.totalCostBasis = totalCostBasis;
    }

    private LocalDate parseDate(Object dateObj) {
        if (dateObj == null) return null;
        
        if (dateObj instanceof java.sql.Date) {
            return ((java.sql.Date) dateObj).toLocalDate();
        } else if (dateObj instanceof String) {
            return LocalDate.parse((String) dateObj);
        } else if (dateObj instanceof LocalDate) {
            return (LocalDate) dateObj;
        }
        
        throw new IllegalArgumentException("Unsupported date type: " + dateObj.getClass());
    }
    
    // Getters and setters
    public LocalDate getGroupEndDate() { return groupEndDate; }
    public void setGroupEndDate(LocalDate groupEndDate) { this.groupEndDate = groupEndDate; }
    
    public Double getAvgPrice() { return avgPrice; }
    public void setAvgPrice(Double avgPrice) { this.avgPrice = avgPrice; }
    
    public Double getTotalQuantity() { return totalQuantity; }
    public void setTotalQuantity(Double totalQuantity) { this.totalQuantity = totalQuantity; }
    
    public Double getAvgCostBasis() { return avgCostBasis; }
    public void setAvgCostBasis(Double avgCostBasis) { this.avgCostBasis = avgCostBasis; }
    
    public Double getTotalCostBasis() { return totalCostBasis; }
    public void setTotalCostBasis(Double totalCostBasis) { this.totalCostBasis = totalCostBasis; }
}
