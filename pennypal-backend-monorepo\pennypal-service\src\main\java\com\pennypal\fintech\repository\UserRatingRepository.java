package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.UserRating;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserRatingRepository extends JpaRepository<UserRating, Integer> {
    
    Optional<UserRating> findByUserId(Integer userId);
    
    @Query("SELECT AVG(ur.rating) FROM UserRating ur WHERE ur.userId = :userId")
    Double getAverageRatingByUserId(@Param("userId") Integer userId);
    
    @Query("SELECT COUNT(ur) FROM UserRating ur WHERE ur.userId = :userId")
    Integer getRatingCountByUserId(@Param("userId") Integer userId);
    
    boolean existsByUserId(Integer userId);
    @Modifying(clearAutomatically = true)
    @Query("DELETE FROM UserRating ur WHERE ur.userId = :userId")
    int deleteByUserId(@Param("userId") Integer userId);
    
    @Query("SELECT AVG(ur.rating) FROM UserRating ur")
    Double getAverageRating();
    
    // Get total count of all ratings
    @Query("SELECT COUNT(ur) FROM UserRating ur")
    Integer getTotalRatingsCount();
    
    // Get ratings by rating value (for statistics if needed)
    @Query("SELECT COUNT(ur) FROM UserRating ur WHERE ur.rating = :rating")
    Integer getCountByRating(@Param("rating") Integer rating);

}