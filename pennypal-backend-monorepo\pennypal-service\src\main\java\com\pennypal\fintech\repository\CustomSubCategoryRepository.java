package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.CustomSubCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomSubCategoryRepository extends JpaRepository<CustomSubCategory, Integer> {
    
    /**
     * Find all custom subcategories for a specific user
     * @param userId User ID
     * @return List of custom subcategories
     */
    List<CustomSubCategory> findByUserId(Integer userId);
    
    /**
     * Find custom subcategory by user ID and name (to avoid duplicates)
     * @param userId User ID
     * @param customSubCategoryName Custom subcategory name
     * @return Optional CustomSubCategory
     */
    Optional<CustomSubCategory> findByUserIdAndCustomSubCategoryName(Integer userId, String customSubCategoryName);
    
    /**
     * Check if custom subcategory exists for user with given name
     * @param userId User ID
     * @param customSubCategoryName Custom subcategory name
     * @return true if exists, false otherwise
     */
    boolean existsByUserIdAndCustomSubCategoryName(Integer userId, String customSubCategoryName);
    
    /**
     * Delete all custom subcategories for a user
     * @param userId User ID
     */
    void deleteByUserId(Integer userId);
    
    /**
     * Find custom subcategories by user ID ordered by name
     * @param userId User ID
     * @return List of custom subcategories ordered by name
     */
    @Query("SELECT c FROM CustomSubCategory c WHERE c.userId = :userId ORDER BY c.customSubCategoryName ASC")
    List<CustomSubCategory> findByUserIdOrderByName(@Param("userId") Integer userId);

    CustomSubCategory findByCustomSubCategoryId(Integer customSubCategoryId);
    boolean existsByCustomSubCategoryId(Integer customSubCategoryId);
}