package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.Users;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.Query;

@Repository
public interface UserRepository extends JpaRepository<Users , Integer> {

    List<Users> findByEmailId(String emailId);
	//Optional<Users> findByEmailId(String emailId);
    // Existing methods...
    List<Users> findAll();
    
   
    Boolean existsByEmailId(String emailId);

    List<Users> findByPhoneNumber(String phoneNumber);
}