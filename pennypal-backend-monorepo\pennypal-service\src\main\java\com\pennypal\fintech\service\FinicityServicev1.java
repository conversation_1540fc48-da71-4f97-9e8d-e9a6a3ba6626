package com.pennypal.fintech.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.pennypal.fintech.dto.FinicityCustomerRequestDto;
import com.pennypal.fintech.dto.FinicityTransaction;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.FinicityCustomers;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.FinicityCustomersRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FinicityServicev1 {
    @Value("${finicity.partnerId}") private String partnerId;
    @Value("${finicity.secret}") private String partnerSecret;
    @Value("${finicity.appKey}") private String appKey;
    @Value("${finicity.baseUrl}") private String baseUrl;

    @Autowired
    private AccountService accountService;

    @Autowired
    private TransactionService transactionService;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private FinicityCustomersRepository finicityCustomersRepository;

    private String partnerAccessToken;

    @PostConstruct
    public void init() throws Exception {
        try {
            partnerAccessToken = authenticatePartner();
            System.out.println("Successfully authenticated with Finicity: " + partnerAccessToken);
        } catch (Exception e) {
            System.err.println("Failed to authenticate with Finicity: " + e.getMessage());
            e.printStackTrace();
            partnerAccessToken = null;
        }
    }

    public String authenticatePartner() throws Exception {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("Finicity-App-Key", appKey);
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64)");

            Map<String, String> body = Map.of(
                "partnerId", partnerId,
                "partnerSecret", partnerSecret
            );

            HttpEntity<Map<String, String>> entity = new HttpEntity<>(body, headers);

            ResponseEntity<JsonNode> response = new RestTemplate().postForEntity(
                baseUrl + "/aggregation/v2/partners/authentication", entity, JsonNode.class);

            return response.getBody().get("token").asText();
        } catch (Exception e) {
            System.err.println("Error in authenticatePartner: " + e.getMessage());
            throw e;
        }
    }

    public String createCustomer(FinicityCustomerRequestDto request) {
        System.out.println("Creating customer: " + request);
    
        try {
            partnerAccessToken = authenticatePartner();
            System.out.println("Successfully authenticated with Finicity: " + partnerAccessToken);
        } catch (Exception e) {
            System.err.println("Failed to authenticate with Finicity: " + e.getMessage());
            e.printStackTrace();
            partnerAccessToken = null;
        }
        
        System.out.println("Partner access token: " + partnerAccessToken);
        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", partnerAccessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<FinicityCustomerRequestDto> entity = new HttpEntity<>(request, headers);
        
        try {
            ResponseEntity<JsonNode> response = new RestTemplate().postForEntity(
                baseUrl + "/aggregation/v2/customers/testing", entity, JsonNode.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                return response.getBody().get("id").asText();
            } else {
                throw new RuntimeException("API call failed with status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            System.err.println("Error creating customer: " + e.getMessage());
            throw new RuntimeException("Failed to create customer", e);
        }
    }

    public String generateConnectLink(Integer userId) {
        log.info("Generating connect link for userId: " + userId);

        try {
            partnerAccessToken = authenticatePartner();
            log.info("Successfully authenticated with Finicity");
        } catch (Exception e) {
            log.error("Failed to authenticate with Finicity: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to authenticate with Finicity", e);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", partnerAccessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Fetch or create customer ID
        String customerId = getOrCreateFinicityCustomer(userId);

        Map<String, Object> body = Map.of(
            "partnerId", partnerId,
            "customerId", customerId
        );

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<String> response = new RestTemplate().postForEntity(
                baseUrl + "/connect/v2/generate", entity, String.class);
            
            System.out.println("Response status: " + response.getStatusCode());
            
            if (response.getStatusCode().is2xxSuccessful()) {
                ObjectMapper mapper = new ObjectMapper();
                JsonNode jsonNode = mapper.readTree(response.getBody());
                return jsonNode.get("link").asText();
            } else {
                throw new RuntimeException("API call failed with status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error calling Finicity API: " + e.getMessage());
            throw new RuntimeException("Failed to generate connect link", e);
        }
    }

    public ResponseEntity<?> getCustomerAccounts(Integer userId) {
        log.info("Inside getCustomerAccounts in FinicityServicev1");
        log.info("Getting/refreshing accounts for userId: " + userId);

        try {
            partnerAccessToken = authenticatePartner();
            log.info("Successfully authenticated with Finicity");
        } catch (Exception e) {
            log.error("Failed to authenticate with Finicity: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("Failed to authenticate with Finicity", e);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", partnerAccessToken);
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        String customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();

        try {
            ResponseEntity<JsonNode> response = new RestTemplate().exchange(
                baseUrl + "/aggregation/v2/customers/" + customerId + "/accounts",
                HttpMethod.GET, entity, JsonNode.class);
            
            System.out.println("Response status: " + response.getStatusCode());
            
            if (response.getStatusCode().is2xxSuccessful()) {
                // Save accounts to database
                List<Accounts> savedAccounts = accountService.saveFinicityAccounts(response.getBody(), userId);
                log.info("Successfully saved " + savedAccounts.size() + " accounts for user ID: " + userId);
                return ResponseEntity.ok("Accounts synced successfully.");
            } else {
                throw new RuntimeException("API call failed with status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error calling Finicity API: " + e.getMessage());
            throw new RuntimeException("Failed to get customer accounts", e);
        }
    }

    // Deprecated - use syncTransactions
    public ResponseEntity<?> getTransactions(Integer userId) {
        System.out.println("Getting transactions for customer: " + userId);

        try {
            partnerAccessToken = authenticatePartner();
            System.out.println("Successfully authenticated with Finicity: " + partnerAccessToken);
        } catch (Exception e) {
            System.err.println("Failed to authenticate with Finicity: " + e.getMessage());
            throw new RuntimeException("Failed to authenticate with Finicity", e);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", partnerAccessToken);
        headers.set("Accept", "application/json");
        headers.setContentType(MediaType.APPLICATION_JSON);

        String customerId = finicityCustomersRepository.findByUserId(userId)
            .getFinicityCustomerId();

        long fromDate = Instant.now().minus(1800, ChronoUnit.DAYS).getEpochSecond();
        System.out.println("From date: " + fromDate);
        long toDate = Instant.now().minus(1, ChronoUnit.DAYS).getEpochSecond();
        System.out.println("To date: " + toDate );

        String url = String.format("%s/aggregation/v3/customers/%s/transactions" +
            "?fromDate=%d&toDate=%d",
            baseUrl, customerId, fromDate, toDate);
        System.out.println("url: " + url);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            System.out.println("Request URL: " + url);
            headers.forEach((key, value) -> System.out.println("Header: " + key + " = " + value));
            ResponseEntity<JsonNode> response = new RestTemplate().exchange(
                url, HttpMethod.GET, entity, JsonNode.class);
            
            System.out.println("Response status: " + response.getStatusCode());
            System.out.println("Response body: " + response.getBody());

            JsonNode transactionsNode = response.getBody().get("transactions");

            ObjectMapper objectMapper = new ObjectMapper();
            List<FinicityTransaction> transactions = objectMapper
                    .readerForListOf(FinicityTransaction.class)
                    .readValue(transactionsNode);

            if (transactions.isEmpty()) {
                return ResponseEntity.ok("No Finicity transactions to sync.");
            }

            transactionService.syncFinicityTransactions(transactions, userId);

            return ResponseEntity.ok("Finicity transactions synced successfully.");
        } catch (Exception e) {
            System.err.println("Error calling Finicity API: " + e.getMessage());
            throw new RuntimeException("Failed to get transactions", e);
        }
    }

    public String getOrCreateFinicityCustomer(Integer userId) {
        FinicityCustomers finicityCustomer = finicityCustomersRepository.findByUserId(userId);
        if (finicityCustomer == null) {
            // Create a new Finicity customer
            FinicityCustomerRequestDto finicityCustomerRequestDto = new FinicityCustomerRequestDto();
            finicityCustomerRequestDto.setUsername("test_user_" + userId);
            String finicityCustomerId = createCustomer(finicityCustomerRequestDto);

            // Save in database
            finicityCustomer = new FinicityCustomers();
            finicityCustomer.setUserId(userId);
            finicityCustomer.setFinicityCustomerId(finicityCustomerId);
            finicityCustomersRepository.save(finicityCustomer);
        }
        return finicityCustomer.getFinicityCustomerId();
    }

    public ResponseEntity<?> syncTransactions(Integer userId) {
        log.info("Inside syncTransactions in FinicityServicev1");
        // Get list of finicity accounts non-investment
        List<Accounts> accounts = accountRepository.findByUserIdAndAuthPartner(
            userId, "finicity");
        log.info("Number of finicity accounts: " + accounts.size());

        // Sync transactions for each account
        for (Accounts account : accounts) {
            Integer numOfTxns = syncTransactionsForAccount(account.getId());
            log.info("Number of transactions synced for account ID: " + account.getId() + " is: " + numOfTxns);
        }
        return ResponseEntity.ok("Transactions synced successfully.");
    }

    public Integer syncTransactionsForAccount(Integer accountId) {
        log.info("Syncing transactions for account ID: {}", accountId);
        Accounts account = accountRepository.findById(accountId)
            .orElseThrow(() -> {
                log.error("Account not found with ID: {}", accountId);
                return new RuntimeException("Account not found with ID: " + accountId);
            });

        try {
            partnerAccessToken = authenticatePartner();
            log.info("Successfully authenticated with Finicity");
        } catch (Exception e) {
            log.error("Failed to authenticate with Finicity: " + e.getMessage());
            throw new RuntimeException("Failed to authenticate with Finicity", e);
        }

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", partnerAccessToken);
        headers.set("Accept", "application/json");
        headers.setContentType(MediaType.APPLICATION_JSON);

        String customerId = finicityCustomersRepository.findByUserId(account.getUser().getId())
            .getFinicityCustomerId();

        long fromDate = account.getLastTxnDate() != null 
                ? account.getLastTxnDate() + 1 
                : account.getOldestTxnDate();
        long toDate = Instant.now().getEpochSecond();
        log.info("Fetching transactions from {} to {} for account ID: {}", 
                fromDate, toDate, accountId);

        String url = String.format("%s/aggregation/v4/customers/%s/accounts/%s/transactions" +
            "?fromDate=%d&toDate=%d",
            baseUrl, customerId, account.getPlaidUniqueNo(), fromDate, toDate);
        log.info("url: " + url);

        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<JsonNode> response = new RestTemplate().exchange(
                url, HttpMethod.GET, entity, JsonNode.class);
            
            log.debug("Response status: {}", response.getStatusCode());

            if (response.getBody() == null) {
                log.warn("Received null response body from Finicity API");
                return 0;
            }
            
            JsonNode transactionsNode = response.getBody().get("transactions");
            if (transactionsNode == null) {
                log.warn("No transactions node in response: {}", response.getBody());
                return 0;
            }

            ObjectMapper objectMapper = new ObjectMapper();
            List<FinicityTransaction> transactions = objectMapper
                    .readerForListOf(FinicityTransaction.class)
                    .readValue(transactionsNode);

            if (transactions.isEmpty()) {
                log.info("No new transactions found for account ID: {}", accountId);
                return 0;
            }

            transactionService.syncFinicityTransactions(transactions, account.getUser().getId());

            // Stream from transactions to get latest_txn_date
            long latestTxnDate = transactions.stream()
                .mapToLong(txn -> txn.getTransactionDate())
                .max()
                .orElse(toDate);
            account.setLastTxnDate(latestTxnDate);
            accountRepository.save(account);

            log.info("Successfully synced {} transactions for account ID: {}", 
                    transactions.size(), accountId);
            return transactions.size();
        } catch (Exception e) {
            System.err.println("Error calling Finicity API: " + e.getMessage());
            throw new RuntimeException("Failed to get transactions", e);
        }
    }
}