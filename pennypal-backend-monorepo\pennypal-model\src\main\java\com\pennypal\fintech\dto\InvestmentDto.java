package com.pennypal.fintech.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class InvestmentDto {
    
    @JsonProperty("id")
    private Integer id;
    
    @JsonProperty("user_id")
 
    private Integer userId;
    
    @JsonProperty("account_id")
 
    private Integer accountId;
    
    @JsonProperty("investment_guid")
    private String investmentGuid;
    
    @JsonProperty("security_name")
    private String securityName;
    
    @JsonProperty("ticker")
    private String ticker;
    
    @JsonProperty("security_type")
    private String securityType;
    
    @JsonProperty("quantity")
 
    private Double quantity;
    
    @JsonProperty("value")
  
    private Double value;
    
    @JsonProperty("cost_basis")
 
    private Double costBasis;
    
    @JsonProperty("current_price")
   
    private Double currentPrice;
    
    @JsonProperty("currency_code")
    private String currencyCode;
    
    @JsonProperty("last_updated")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdated;
    
    @JsonProperty("today_change")
    private Double todayChange;
    
    @JsonProperty("today_change_percent")
    private Double todayChangePercent;
    
    @JsonProperty("total_gain")
    private Double totalGain;
    
    @JsonProperty("total_gain_percent")
    private Double totalGainPercent;

    // Constructor with essential fields
    public InvestmentDto(Integer id, Integer userId, String securityName, String ticker, 
                        Double quantity, Double value, Double currentPrice) {
        this.id = id;
        this.userId = userId;
        this.securityName = securityName;
        this.ticker = ticker;
        this.quantity = quantity;
        this.value = value;
        this.currentPrice = currentPrice;
    }

    // Custom setter for ticker to ensure uppercase
    public void setTicker(String ticker) {
        this.ticker = ticker != null ? ticker.toUpperCase() : null;
    }

    // Custom setter for currency code with default
    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode != null ? currencyCode.toUpperCase() : "USD";
    }

    // Utility methods
    public boolean isPositiveGain() {
        return totalGain != null && totalGain > 0;
    }

    public boolean isPositiveTodayChange() {
        return todayChange != null && todayChange > 0;
    }

    public Double getMarketValue() {
        if (quantity != null && currentPrice != null) {
            return quantity * currentPrice;
        }
        return value;
    }

    public String getFormattedTicker() {
        return ticker != null ? ticker.toUpperCase() : "";
    }

    public String getSecurityTypeDisplayName() {
        if (securityType == null) return "Unknown";
        
        switch (securityType.toLowerCase()) {
            case "stock":
                return "Stock";
            case "mutual_fund":
                return "Mutual Fund";
            case "etf":
                return "ETF";
            case "bond":
                return "Bond";
            case "option":
                return "Option";
            case "crypto":
                return "Cryptocurrency";
            default:
                return securityType;
        }
    }

    // Calculate unrealized gain/loss
    public Double getUnrealizedGainLoss() {
        if (getMarketValue() != null && costBasis != null) {
            return getMarketValue() - costBasis;
        }
        return null;
    }

    // Calculate unrealized gain/loss percentage
    public Double getUnrealizedGainLossPercent() {
        if (costBasis != null && costBasis != 0 && getUnrealizedGainLoss() != null) {
            return (getUnrealizedGainLoss() / costBasis) * 100;
        }
        return null;
    }
}