package com.pennypal.fintech.service;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;

import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.repository.ReconcileRepository;
import com.pennypal.fintech.repository.TransactionRepository;

public class ReconcileService {

    @Autowired
    private TransactionRepository transactionRepository;

    @Autowired
    private ReconcileRepository reconcileRepository;

   
}
