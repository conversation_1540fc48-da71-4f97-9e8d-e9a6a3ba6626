package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ProfilePictureUploadDto;
import com.pennypal.fintech.dto.ProfilePictureResponseDto;
import com.pennypal.fintech.service.ProfilePictureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/users")

public class ProfilePictureController {
    
    @Autowired
    private ProfilePictureService profilePictureService;
    
    /**
     * Upload profile picture using multipart form data
     */
    @PostMapping("/{userId}/profile-picture/upload")
    public ResponseEntity<?> uploadProfilePicture(
            @PathVariable Integer userId,
            @RequestParam("file") MultipartFile file) {
        try {
            ProfilePictureResponseDto response = profilePictureService.uploadProfilePicture(userId, file);
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("success", true);
            responseMap.put("message", "Profile picture uploaded successfully");
            responseMap.put("data", response);
            
            return ResponseEntity.ok(responseMap);
            
        } catch (IOException e) {
            log.error("IO error while uploading profile picture for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("success", false, "message", "Failed to process file: " + e.getMessage()));
                    
        } catch (RuntimeException e) {
            log.error("Error uploading profile picture for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("success", false, "message", e.getMessage()));
        }
    }
    
    /**
     * Upload profile picture using JSON data (base64 encoded)
     */
    @PostMapping("/{userId}/profile-picture/upload-data")
    public ResponseEntity<?> uploadProfilePictureFromData(
            @PathVariable Integer userId,
            @RequestBody ProfilePictureUploadDto uploadDto) {
        try {
            uploadDto.setUserId(userId);
            ProfilePictureResponseDto response = profilePictureService.uploadProfilePictureFromDto(uploadDto);
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("success", true);
            responseMap.put("message", "Profile picture uploaded successfully");
            responseMap.put("data", response);
            
            return ResponseEntity.ok(responseMap);
            
        } catch (RuntimeException e) {
            log.error("Error uploading profile picture data for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("success", false, "message", e.getMessage()));
        }
    }
    
    /**
     * Get profile picture as image
     */
    @GetMapping("/{userId}/profile-picture")
    public ResponseEntity<?> getProfilePicture(@PathVariable Integer userId) {
        try {
            byte[] imageData = profilePictureService.getProfilePictureData(userId);
            String contentType = profilePictureService.getContentType(userId);
            
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(contentType));
            headers.setContentLength(imageData.length);
            headers.setCacheControl("max-age=3600"); // Cache for 1 hour
            
            return ResponseEntity.ok()
                    .headers(headers)
                    .body(imageData);
                    
        } catch (RuntimeException e) {
            log.error("Error retrieving profile picture for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("success", false, "message", e.getMessage()));
        }
    }
    
    /**
     * Get profile picture info (metadata)
     */
    @GetMapping("/{userId}/profile-picture/info")
    public ResponseEntity<?> getProfilePictureInfo(@PathVariable Integer userId) {
        try {
            ProfilePictureResponseDto response = profilePictureService.getProfilePictureInfo(userId);
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("success", true);
            responseMap.put("data", response);
            
            return ResponseEntity.ok(responseMap);
            
        } catch (RuntimeException e) {
            log.error("Error retrieving profile picture info for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(Map.of("success", false, "message", e.getMessage()));
        }
    }
    
    /**
     * Delete profile picture
     */
    @DeleteMapping("/{userId}/profile-picture")
    public ResponseEntity<?> deleteProfilePicture(@PathVariable Integer userId) {
        try {
            boolean deleted = profilePictureService.deleteProfilePicture(userId);
            
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("success", true);
            responseMap.put("message", "Profile picture deleted successfully");
            responseMap.put("deleted", deleted);
            
            return ResponseEntity.ok(responseMap);
            
        } catch (RuntimeException e) {
            log.error("Error deleting profile picture for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(Map.of("success", false, "message", e.getMessage()));
        }
    }
}