package com.pennypal.fintech.service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.service.NotificationRuleService;
import com.pennypal.fintech.service.RecurringTransactionService;
import com.pennypal.fintech.service.PlaidService;
import com.pennypal.fintech.service.InvestmentMxService;
@Service
@Slf4j
public class TaskSchedulerService {

    private final UserRepository userRepository;
    private final RecurringTransactionService recurringTransactionService;
    private final NotificationRuleService notificationRuleService;
      private final PlaidService plaidService; 
      private final InvestmentMxService investmentMxService;
    public TaskSchedulerService(UserRepository userRepository,
                               RecurringTransactionService recurringTransactionService,
                               NotificationRuleService notificationRuleService,
                               PlaidService plaidService,
                               InvestmentMxService investmentMxService) {
        this.userRepository = userRepository;
        this.recurringTransactionService = recurringTransactionService;
        this.notificationRuleService = notificationRuleService;
         this.plaidService = plaidService;
           this.investmentMxService = investmentMxService;
    }

    @Scheduled(cron = "0 0 0 * * 6")
    public void analyzeAndStoreRecurringTransactions() {
        List<Integer> userIds = userRepository.findAll().stream().map(Users::getId).collect(Collectors.toList());
        System.out.println("User IDs: " + userIds);
        for (Integer userId : userIds) {
            log.info("Calling scheduled analyzeAndStoreRecurringTransactions for user: {}", userId);
            recurringTransactionService.analyzeAndStoreRecurringTransactions(userId);
        }
    }

    @Scheduled(cron = "0 0 0 * * *") 
    public void checkRecurringTransactionRules() {
        List<Integer> userIds = userRepository.findAll().stream().map(Users::getId).collect(Collectors.toList());
        for (Integer userId : userIds) {
            log.info("Calling scheduled checkRecurringTransactionRules for user: {}", userId);
            notificationRuleService.checkRecurringTransactionRules(userId);
        }
    }

    @Scheduled(cron = "0 0 0 * * *") 
    public void checkBudgetRules() {
        List<Integer> userIds = userRepository.findAll().stream().map(Users::getId).collect(Collectors.toList());
        for (Integer userId : userIds) {
            log.info("Calling scheduled checkBudgetRules for user: {}", userId);
            notificationRuleService.checkBudgetRules(userId);
        }
    }

//       // New scheduled method to sync all accounts for all users
//     @Scheduled(cron = "0 0 1 * * *") // Run daily at 1 AM
//     public void syncAllAccountsForAllUsers() {
//         List<Integer> userIds = userRepository.findAll().stream()
//             .map(Users::getId)
//             .collect(Collectors.toList());
        
//         log.info("Starting scheduled account sync for {} users", userIds.size());
        
//         for (Integer userId : userIds) {
//             try {
//                 log.info("Starting account sync for user: {}", userId);
//                 CompletableFuture<Integer> syncResult = plaidService.asyncSyncAllAccountsForUser(userId);
                
//                 // Optional: Handle the result asynchronously
//                 syncResult.thenAccept(successCount -> {
//                     log.info("Account sync completed for user {}: {} accounts successfully synced", 
//                         userId, successCount);
//                 }).exceptionally(throwable -> {
//                     log.error("Account sync failed for user {}: {}", userId, throwable.getMessage(), throwable);
//                     return null;
//                 });
                
//             } catch (Exception e) {
//                 log.error("Error initiating account sync for user {}: {}", userId, e.getMessage(), e);
//             }
//         }
//     }

//     // Alternative: If you want to run account sync in batches to avoid overwhelming the system
//     @Async
//     // @Scheduled(cron = "0 0 1 * * *") // Run daily at 1 AM
//     @Scheduled(cron = "0 25 11 * * *")
//     public void asyncSyncAllAccountsForAllUsers() {
//         List<Integer> userIds = userRepository.findAll().stream()
//             .map(Users::getId)
//             .collect(Collectors.toList());
        
//         log.info("Starting async scheduled account sync for {} users", userIds.size());
        
//         // Process users in batches to avoid overwhelming the system
//         int batchSize = 10; // Adjust based on your system capacity
//         for (int i = 0; i < userIds.size(); i += batchSize) {
//             final int batchStart = i; // Make effectively final copy
//             final int end = Math.min(i + batchSize, userIds.size());
//             List<Integer> batch = userIds.subList(batchStart, end);
            
//             log.info("Processing batch {}-{} of {} users", batchStart + 1, end, userIds.size());
            
//             List<CompletableFuture<Integer>> futures = batch.stream()
//                 .map(userId -> {
//                     try {
//                         return plaidService.asyncSyncAllAccountsForUser(userId);
//                     } catch (Exception e) {
//                         log.error("Error initiating account sync for user {}: {}", userId, e.getMessage(), e);
//                         return CompletableFuture.completedFuture(0);
//                     }
//                 })
//                 .collect(Collectors.toList());
            
//             // Wait for all futures in this batch to complete
//             CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
//                 .thenRun(() -> {
//                     log.info("Batch {}-{} completed", batchStart + 1, end);
//                 })
//                 .exceptionally(throwable -> {
//                     log.error("Error in batch {}-{}: {}", batchStart + 1, end, throwable.getMessage(), throwable);
//                     return null;
//                 });
            
//             // Optional: Add delay between batches to prevent system overload
//             try {
//                 Thread.sleep(5000); // 5 second delay between batches
//             } catch (InterruptedException e) {
//                 Thread.currentThread().interrupt();
//                 log.warn("Batch processing interrupted");
//                 break;
//             }
//         }
//     }
   
   
//     /**
//  * Sync investment data from MX - runs 4 times daily
//  * Scheduled at: 9:30 AM, 2:30 PM, 4:30 PM, 6:00 PM
//  * This fetches latest holdings and saves to daily_investment_stocks table
//  */
// @Scheduled(cron = "0 30 9,14,16,18 * * *")
// public void syncInvestmentData() {
//     List<Integer> userIds = userRepository.findAll().stream()
//             .map(Users::getId)
//             .collect(Collectors.toList());
    
//     log.info("Starting scheduled investment data sync for {} users at {}", 
//              userIds.size(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
    
//     for (Integer userId : userIds) {
//         try {
//             log.info("Calling scheduled syncInvestmentData for user: {}", userId);
//             String result = investmentMxService.syncInvestmentData(userId);
//             log.info("Investment sync result for user {}: {}", userId, result);
//         } catch (Exception e) {
//             log.error("Error syncing investment data for user: {}", userId, e);
//         }
//     }
    
//     log.info("Completed scheduled investment data sync at {}", 
//              LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm")));
// }

}