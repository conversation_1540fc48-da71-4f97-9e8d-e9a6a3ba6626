package com.pennypal.fintech.repository;

import com.pennypal.fintech.dto.CategoryMonthlySummaryDto;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import jakarta.persistence.Query;
import java.time.LocalDate;
import java.util.List;

public class TransactionRepositoryImpl implements TransactionCustomRepository {
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<CategoryMonthlySummaryDto> getCategoryMonthlySummary(
            Integer categoryId, Integer subCategoryId, Integer userId, LocalDate startDate, int months
    ) {
        // Generate all months in the range using a recursive CTE
        String queryStr = "WITH RECURSIVE months AS (" +
                "  SELECT DATE_FORMAT(:startDate, '%Y-%m') AS month " +
                "  UNION ALL " +
                "  SELECT DATE_FORMAT(DATE_ADD(CAST(CONCAT(month, '-01') AS DATE), INTERVAL 1 MONTH), '%Y-%m') " +
                "  FROM months " +
                "  WHERE DATE_ADD(CAST(CONCAT(month, '-01') AS DATE), INTERVAL 1 MONTH) < " +
                "        DATE_ADD(:startDate, INTERVAL :months MONTH)" +
                ") " +
                "SELECT " +
                "    m.month AS month, " +
                "    COALESCE(SUM(COALESCE(b.actual, 0)), 0) AS total " +
                "FROM months m " +
                "LEFT JOIN budget b " +
                "    ON DATE_FORMAT(b.date, '%Y-%m') = m.month " +
                "    JOIN sub_category sc ON b.sub_category_id = sc.id " +
                "    WHERE sc.category_id = :categoryId " +
                (subCategoryId != null ? "AND b.sub_category_id = :subCategoryId " : "") +
                "    AND b.user_id = :userId " +
                "    AND (b.date IS NULL OR " +
                "         (b.date >= :startDate AND b.date < DATE_ADD(:startDate, INTERVAL :months MONTH))) " +
                "GROUP BY m.month " +
                "ORDER BY m.month";

        Query query = entityManager.createNativeQuery(queryStr, "CategoryMonthlySummaryMapping");
        query.setParameter("categoryId", categoryId);
        if (subCategoryId != null) {
            query.setParameter("subCategoryId", subCategoryId);
        }
        query.setParameter("userId", userId);
        query.setParameter("startDate", startDate);
        query.setParameter("months", months);
        return query.getResultList();
    }
}