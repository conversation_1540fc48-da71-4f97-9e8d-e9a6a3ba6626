package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ApiResponseDto;
import com.pennypal.fintech.dto.ChangePasswordDto;
import com.pennypal.fintech.dto.DeleteAccountDto;
import com.pennypal.fintech.service.AccountManagementService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/v1/accounts")
@CrossOrigin
public class AccountManagementController {
    
    @Autowired
    private AccountManagementService accountManagementService;
    
    @PostMapping("/change-password")
    public ResponseEntity<ApiResponseDto> changePassword(@Valid @RequestBody ChangePasswordDto changePasswordDto) {
        try {
            boolean result = accountManagementService.changePassword(changePasswordDto);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "Password changed successfully"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Failed to change password"));
            }
        } catch (Exception e) {
            log.error("Error changing password for user: {}", changePasswordDto.getUserId(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @PostMapping("/delete-permanently")
    public ResponseEntity<ApiResponseDto> deleteAccountPermanently(@Valid @RequestBody DeleteAccountDto deleteAccountDto) {
        try {
            boolean result = accountManagementService.deleteAccountPermanently(deleteAccountDto);
            if (result) {
                return ResponseEntity.ok(new ApiResponseDto(true, "Account deleted permanently"));
            } else {
                return ResponseEntity.badRequest()
                        .body(new ApiResponseDto(false, "Failed to delete account"));
            }
        } catch (Exception e) {
            log.error("Error deleting account for user: {}", deleteAccountDto.getUserId(), e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
    
    @GetMapping("/deletion-status")
    public ResponseEntity<ApiResponseDto> checkDeletionStatus(@RequestParam String emailId) {
        try {
            boolean isDeleted = accountManagementService.isAccountDeleted(emailId);
            return ResponseEntity.ok(new ApiResponseDto(true, "Status retrieved successfully", isDeleted));
        } catch (Exception e) {
            log.error("Error checking deletion status for email: {}", emailId, e);
            return ResponseEntity.badRequest()
                    .body(new ApiResponseDto(false, e.getMessage()));
        }
    }
}