package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.CustomChart;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CustomChartRepository extends JpaRepository<CustomChart, Long> {
    boolean existsByUserId(Integer userId);
    boolean existsByChatId(Integer chatId);
    boolean existsByUserIdAndChatId(Integer userId, Integer chatId);
    List<CustomChart> findByUserId(Integer userId);
    List<CustomChart> findByChatId(Integer chatId);
    List<CustomChart> findByUserIdAndChatId(Integer userId, Integer chatId);
}