package com.pennypal.fintech.dto;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class TwoFactorAuthDto {
    private Integer userId;
    private String secret;
    private String qrCodeUrl;
    private String verificationCode;
    private Boolean isEnabled;
    private String backupCodes;
    private LocalDateTime enabledAt;
    private String authMethod; // "TOTP", "SMS", "EMAIL"
}