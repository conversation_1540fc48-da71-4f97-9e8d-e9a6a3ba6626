package com.pennypal.fintech.repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.Accounts;

@Repository
public interface AccountRepository extends JpaRepository<Accounts, Integer> {
    List<Accounts> findByUser_Id(int userId);
    List<Accounts> findByAccountCategory(String accountCategory);
    List<Accounts> findByInstitutionId(String institutionId);
    List<Accounts> findByAccountType(String accountType);
    Optional<Accounts> findByPlaidUniqueNo(String accountId);
    List<Accounts> findByUser_IdAndAccountType(int userId, String accountType);
    List<Accounts> findByUser_Id(Integer userId);
    Optional<Accounts> findByPlaidUniqueNoAndUser_Id(String plaidUniqueNo, Integer userId);
List<Accounts> findByUserIdAndAccountCategory(int userId, String accountCategory);
    @Query("SELECT DISTINCT a.user.id FROM Accounts a")
    List<Integer> findDistinctUserIds();

    @Query(value = """
        SELECT DISTINCT id AS account_id, CONCAT_WS(' - ****', account_name, account_mask) AS account_name
        FROM accounts
        WHERE user_id = :userId
        ORDER BY id
        """, nativeQuery = true)
    List<Map<String, Object>> findUniqueAccountIds(int userId);

    List<Accounts> findByUserId(int userId);

    @Query(value = """
        SELECT * FROM accounts
        WHERE plaid_unique_no = :finicityAcctId
        """, nativeQuery = true)
    Accounts findByFinictyAcctId(Long finicityAcctId);

    @Query(value = """
        SELECT * FROM accounts
        WHERE user_id = :userId AND
              auth_partner = :authPartner AND
              account_type != 'investment'
        """, nativeQuery = true)
    List<Accounts> findByUserIdAndAuthPartner(Integer userId, String authPartner);

    @Query(value = """
        SELECT * FROM accounts
        WHERE plaid_unique_no IN :finicityAcctIds
        """, nativeQuery = true)
    List<Accounts> findByFinictyAcctIdIn(Set<Long> finicityAcctIds);

    // Add this missing method for investment accounts
    @Query(value = """
        SELECT * FROM accounts
        WHERE user_id = :userId AND
              auth_partner = :authPartner AND
              account_type = :accountType
        """, nativeQuery = true)
    List<Accounts> findByUserIdAndAuthPartnerAndAccountType(Integer userId, String authPartner, String accountType);
}