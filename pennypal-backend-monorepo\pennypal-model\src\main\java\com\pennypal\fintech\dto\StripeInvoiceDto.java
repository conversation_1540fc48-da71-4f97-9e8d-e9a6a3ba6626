package com.pennypal.fintech.dto;

import java.math.BigDecimal;

import lombok.Data;

@Data
public class StripeInvoiceDto {
    Integer userId;
    String customerId;
    String subscriptionId;
    String invoiceId;
    BigDecimal amountPaid;
    String currency;
    String billingReason;
    String collectionMethod;
    String customerEmail;
    String customerName;
    String customerPhone;
    String invoiceUrl;
    String invoicePdf;
    String createdAt;

    public StripeInvoiceDto(Integer userId, String customerId, String subscriptionId, String invoiceId, BigDecimal amountPaid,
            String currency, String billingReason, String collectionMethod, String customerEmail, String customerName,
            String customerPhone, String invoiceUrl, String invoicePdf, String createdAt) {
        this.userId = userId;
        this.customerId = customerId;
        this.subscriptionId = subscriptionId;
        this.invoiceId = invoiceId;
        this.amountPaid = amountPaid;
        this.currency = currency;
        this.billingReason = billingReason;
        this.collectionMethod = collectionMethod;
        this.customerEmail = customerEmail;
        this.customerName = customerName;
        this.customerPhone = customerPhone;
        this.invoiceUrl = invoiceUrl;
        this.invoicePdf = invoicePdf;
        this.createdAt = createdAt;
    }
}
