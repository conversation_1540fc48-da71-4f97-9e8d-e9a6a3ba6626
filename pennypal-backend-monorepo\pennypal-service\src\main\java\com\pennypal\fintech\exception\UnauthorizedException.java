package com.pennypal.fintech.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.UNAUTHORIZED)
public class UnauthorizedException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private String errorCode;
    private Object[] args;
    
    /**
     * Default constructor
     */
    public UnauthorizedException() {
        super("Access denied");
    }
    
    /**
     * Constructor with message
     */
    public UnauthorizedException(String message) {
        super(message);
    }
    
    /**
     * Constructor with message and cause
     */
    public UnauthorizedException(String message, Throwable cause) {
        super(message, cause);
    }
    
    /**
     * Constructor with cause
     */
    public UnauthorizedException(Throwable cause) {
        super("Access denied", cause);
    }
    
    /**
     * Constructor with error code and message
     */
    public UnauthorizedException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
    
    /**
     * Constructor with error code, message and arguments
     */
    public UnauthorizedException(String errorCode, String message, Object... args) {
        super(message);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    /**
     * Constructor with error code, message, cause and arguments
     */
    public UnauthorizedException(String errorCode, String message, Throwable cause, Object... args) {
        super(message, cause);
        this.errorCode = errorCode;
        this.args = args;
    }
    
    // Getters
    public String getErrorCode() {
        return errorCode;
    }
    
    public Object[] getArgs() {
        return args;
    }
    
    // Setters
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }
    
    public void setArgs(Object[] args) {
        this.args = args;
    }
    
    // Common static factory methods for frequently used scenarios
    public static UnauthorizedException accessDenied() {
        return new UnauthorizedException("ACCESS_DENIED", "Access denied");
    }
    
    public static UnauthorizedException invalidToken() {
        return new UnauthorizedException("INVALID_TOKEN", "Invalid or expired token");
    }
    
    public static UnauthorizedException insufficientPermissions() {
        return new UnauthorizedException("INSUFFICIENT_PERMISSIONS", "Insufficient permissions to perform this action");
    }
    
    public static UnauthorizedException secondaryUserAccessRevoked() {
        return new UnauthorizedException("SECONDARY_ACCESS_REVOKED", "Secondary user access has been revoked");
    }
    
    public static UnauthorizedException invalidRelationship() {
        return new UnauthorizedException("INVALID_RELATIONSHIP", "Invalid user relationship");
    }
    
    @Override
    public String toString() {
        if (errorCode != null) {
            return String.format("UnauthorizedException{errorCode='%s', message='%s'}", errorCode, getMessage());
        }
        return super.toString();
    }
}