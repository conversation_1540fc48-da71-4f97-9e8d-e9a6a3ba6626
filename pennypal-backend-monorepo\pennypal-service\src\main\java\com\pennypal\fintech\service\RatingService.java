package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.UserRatingDto;
import com.pennypal.fintech.dto.RatingResponseDto;
import com.pennypal.fintech.dto.RatingStatsDto;
import com.pennypal.fintech.entity.UserRating;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.UserRatingRepository;
import com.pennypal.fintech.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Slf4j
@Service
public class RatingService {
    
    @Autowired
    private UserRatingRepository userRatingRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Transactional
    public RatingResponseDto submitRating(UserRatingDto ratingDto) {
        log.info("Submitting rating for user: {}", ratingDto.getUserId());
        
        // Validate input
        validateRatingInput(ratingDto);
        
        // Validate user exists
        Users user = userRepository.findById(ratingDto.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + ratingDto.getUserId()));
        
        // Check if user already has a rating
        Optional<UserRating> existingRating = userRatingRepository.findByUserId(ratingDto.getUserId());
        
        UserRating userRating;
        if (existingRating.isPresent()) {
            // Update existing rating
            userRating = existingRating.get();
            userRating.setRating(ratingDto.getRating());
            userRating.setFeedback(ratingDto.getFeedback());
            userRating.setUpdatedAt(LocalDateTime.now());
            log.info("Updating existing rating for user: {}", ratingDto.getUserId());
        } else {
            // Create new rating
            userRating = new UserRating();
            userRating.setUserId(ratingDto.getUserId());
            userRating.setRating(ratingDto.getRating());
            userRating.setFeedback(ratingDto.getFeedback());
            userRating.setCreatedAt(LocalDateTime.now());
            userRating.setUpdatedAt(LocalDateTime.now());
            log.info("Creating new rating for user: {}", ratingDto.getUserId());
        }
        
        UserRating savedRating = userRatingRepository.save(userRating);
        
        log.info("Rating submitted successfully for user: {}", ratingDto.getUserId());
        return convertToResponseDto(savedRating);
    }
    
    public RatingStatsDto getUserRatingStats(Integer userId) {
        log.info("Getting rating stats for user: {}", userId);
        
        // Validate user exists
        userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        RatingStatsDto statsDto = new RatingStatsDto();
        statsDto.setUserId(userId);
        
        Optional<UserRating> userRating = userRatingRepository.findByUserId(userId);
        if (userRating.isPresent()) {
            statsDto.setCurrentRating(userRating.get().getRating());
            statsDto.setHasRated(true);
            statsDto.setLastRatedAt(userRating.get().getUpdatedAt() != null ? 
                userRating.get().getUpdatedAt().format(formatter) : 
                userRating.get().getCreatedAt().format(formatter));
        } else {
            statsDto.setCurrentRating(0);
            statsDto.setHasRated(false);
            statsDto.setLastRatedAt(null);
        }
        
        // Get overall app statistics (if needed)
        try {
            Double averageRating = userRatingRepository.getAverageRating();
            Integer totalRatings = userRatingRepository.getTotalRatingsCount();
            
            statsDto.setOverallAverageRating(averageRating != null ? averageRating : 0.0);
            statsDto.setTotalAppRatings(totalRatings != null ? totalRatings.intValue() : 0);
        } catch (Exception e) {
            log.warn("Could not fetch overall rating statistics: {}", e.getMessage());
            statsDto.setOverallAverageRating(0.0);
            statsDto.setTotalAppRatings(0);
        }
        
        log.info("Rating stats retrieved successfully for user: {}", userId);
        return statsDto;
    }
    
    public RatingResponseDto getUserRating(Integer userId) {
        log.info("Getting rating for user: {}", userId);
        
        // Validate user exists
        userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        Optional<UserRating> userRating = userRatingRepository.findByUserId(userId);
        if (userRating.isEmpty()) {
            throw new RuntimeException("No rating found for user with ID: " + userId);
        }
        
        log.info("Rating retrieved successfully for user: {}", userId);
        return convertToResponseDto(userRating.get());
    }
    
    @Transactional
    public boolean deleteUserRating(Integer userId) {
        log.info("Deleting rating for user: {}", userId);
        
        // Validate user exists
        userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));
        
        if (!userRatingRepository.existsByUserId(userId)) {
            throw new RuntimeException("No rating found for user with ID: " + userId);
        }
        
        int deletedCount = userRatingRepository.deleteByUserId(userId);
        log.info("Rating deleted successfully for user: {}, deleted count: {}", userId, deletedCount);
        return deletedCount > 0;
    }
    
    // Helper method to validate rating input
    private void validateRatingInput(UserRatingDto ratingDto) {
        if (ratingDto == null) {
            throw new RuntimeException("Rating data cannot be null");
        }
        
        if (ratingDto.getUserId() == null) {
            throw new RuntimeException("User ID cannot be null");
        }
        
        if (ratingDto.getRating() == null) {
            throw new RuntimeException("Rating cannot be null");
        }
        
        if (ratingDto.getRating() < 1 || ratingDto.getRating() > 5) {
            throw new RuntimeException("Rating must be between 1 and 5 stars");
        }
        
        // Optional: Validate feedback length
        if (ratingDto.getFeedback() != null && ratingDto.getFeedback().length() > 1000) {
            throw new RuntimeException("Feedback cannot exceed 1000 characters");
        }
    }
    
    // Helper method to convert entity to response DTO
    private RatingResponseDto convertToResponseDto(UserRating rating) {
        RatingResponseDto responseDto = new RatingResponseDto();
        responseDto.setId(rating.getId());
        responseDto.setUserId(rating.getUserId());
        responseDto.setRating(rating.getRating());
        responseDto.setFeedback(rating.getFeedback());
        
        if (rating.getCreatedAt() != null) {
            responseDto.setCreatedAt(rating.getCreatedAt().format(formatter));
        }
        
        if (rating.getUpdatedAt() != null) {
            responseDto.setUpdatedAt(rating.getUpdatedAt().format(formatter));
        }
        
        return responseDto;
    }
}