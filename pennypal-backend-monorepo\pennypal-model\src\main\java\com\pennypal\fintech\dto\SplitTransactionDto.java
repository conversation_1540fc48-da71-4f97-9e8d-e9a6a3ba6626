package com.pennypal.fintech.dto;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class SplitTransactionDto {
     private Integer id;
    private Integer userId;
    private Integer transactionId;
    private Integer userContactId;
    private BigDecimal amount;
    private LocalDateTime date;
    private String notesDesc;
    private String splitMethod; // Add this field
    private String userContactName; // Add this field


    // Default constructor
    public SplitTransactionDto() {
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(Integer transactionId) {
        this.transactionId = transactionId;
    }

    public Integer getUserContactId() {
        return userContactId;
    }

    public void setUserContactId(Integer userContactId) {
        this.userContactId = userContactId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public LocalDateTime getDate() {
        return date;
    }

    public void setDate(LocalDateTime date) {
        this.date = date;
    }

    public String getNotesDesc() {
        return notesDesc;
    }

    public void setNotesDesc(String notesDesc) {
        this.notesDesc = notesDesc;
    }

    public String getSplitMethod(){
        return splitMethod;

    }
    public void setSplitMethod(String splitMethod){
        this.splitMethod = splitMethod;
    }
    public String getUserContactName(){
        return userContactName;

    }
    public void setUserContactName(String userContactName){
        this.userContactName = userContactName;
    }

    @Override
    public String toString() {
        return "SplitTransactionDto{" +
                "id=" + id +
                ", userId=" + userId +
                ", transactionId=" + transactionId +
                ", userContactId=" + userContactId +
                ", amount=" + amount +
                ", date=" + date +
                ", notesDesc='" + notesDesc + '\'' +
                '}';
    }
    
}
