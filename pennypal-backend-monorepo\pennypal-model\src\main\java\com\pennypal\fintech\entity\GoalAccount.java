package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

@Entity
@Table(name = "goal_accounts")
@Data
public class GoalAccount {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "goal_id", nullable = false)
    private Goal goal;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Accounts account;

    @Column(name = "allocation_percentage", nullable = false)
    private Double allocationPercentage;

    @Column(name = "allocated_amount")
    private Double allocatedAmount = 0.0;

    @Column(name = "current_contribution")
    private Double currentContribution = 0.0;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Calculate progress percentage
    public Double getProgressPercentage() {
        if (allocatedAmount <= 0) {
            return 0.0;
        }
        return (currentContribution / allocatedAmount) * 100;
    }
   // NEW: Virtual balance for this goal account - tracks allocated funds for this specific goal
    @Column(name = "goal_account_balance", nullable = false)
    private Double goalAccountBalance = 0.0;

    // NEW: Track initial allocation when goal account is created
    @Column(name = "initial_allocated_balance")
    private Double initialAllocatedBalance = 0.0;

    // Calculate remaining balance available for contributions
    public Double getRemainingBalance() {
        return goalAccountBalance;
    }

    // Check if sufficient balance is available for contribution
    public boolean hasSufficientBalance(Double amount) {
        return goalAccountBalance >= amount;
    }

    // Deduct amount from goal account balance
    public void deductFromBalance(Double amount) {
        if (amount > goalAccountBalance) {
            throw new IllegalArgumentException("Insufficient balance in goal account");
        }
        this.goalAccountBalance -= amount;
    }

    // Add amount to goal account balance (for allocations/top-ups)
    public void addToBalance(Double amount) {
        this.goalAccountBalance += amount;
    }



}