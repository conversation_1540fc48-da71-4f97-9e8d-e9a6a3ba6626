// AccountBalance.java
package com.pennypal.fintech.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;


import java.time.LocalDateTime;

@Entity
@Data
@Table(name = "account_balances")
public class AccountBalance {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
   
    /*  these are already mapped.
    @Column(name = "user_id", nullable = false)
    private int userId;
    
    @Column(name = "account_id", nullable = false, insertable = false, updatable = false)
    private int accountId;

    */
    @Column(name = "currency_code", length = 3)
    private String currencyCode;
   

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id")  // No need for foreignKey annotation here
    private Accounts account;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")  // No need for foreignKey annotation here
    private Users user;

    @Column(name = "balance", nullable = false)
    private Double balance;

    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;

     // Add getter methods if you need direct access to IDs
     public int getUserId() {
        return user != null ? user.getId() : 0;
    }
    
    public int getAccountId() {
        return account != null ? account.getId() : 0;
    }

}


