package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.RecurringContributionDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Goal;
import com.pennypal.fintech.entity.GoalAccount;
import com.pennypal.fintech.entity.RecurringContribution;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.exception.InsufficientFundsException;
import com.pennypal.fintech.exception.InvalidRequestException;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.GoalAccountRepository;
import com.pennypal.fintech.repository.GoalRepository;
import com.pennypal.fintech.repository.RecurringContributionRepository;
import com.pennypal.fintech.repository.UserRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class RecurringContributionService {
    private static final Logger logger = LoggerFactory.getLogger(RecurringContributionService.class);

    private final RecurringContributionRepository recurringContributionRepository;
    private final GoalRepository goalRepository;
    private final GoalAccountRepository goalAccountRepository;
    private final UserRepository userRepository;
    private final AccountRepository accountRepository;
    private final GoalService goalService;

    @Autowired
    public RecurringContributionService(
            RecurringContributionRepository recurringContributionRepository,
            GoalRepository goalRepository,
            GoalAccountRepository goalAccountRepository,
            UserRepository userRepository,
            AccountRepository accountRepository,
            GoalService goalService) {
        this.recurringContributionRepository = recurringContributionRepository;
        this.goalRepository = goalRepository;
        this.goalAccountRepository = goalAccountRepository;
        this.userRepository = userRepository;
        this.accountRepository = accountRepository;
        this.goalService = goalService;
    }

    @Transactional
    public RecurringContributionDto.RecurringContributionResponse setupRecurringContribution(
            int userId, RecurringContributionDto.CreateRecurringContributionRequest request) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate goal
        Goal goal = goalRepository.findById(request.getGoalId())
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + request.getGoalId()));

        // Check if goal belongs to user
        if (goal.getUser().getId() != userId) {
            throw new ResourceNotFoundException("Goal not found with id: " + request.getGoalId());
        }

        // Validate account
        Accounts account = accountRepository.findById(request.getAccountId())
                .orElseThrow(() -> new ResourceNotFoundException("Account not found with id: " + request.getAccountId()));

        // Check if account belongs to user
        if (account.getUser().getId() != userId) {
            throw new InvalidRequestException("Account does not belong to user");
        }

        // Validate amount
        if (request.getAmount() <= 0) {
            throw new InvalidRequestException("Contribution amount must be positive");
        }

        // Check if account is associated with goal
        GoalAccount goalAccount = goalAccountRepository.findByGoalAndAccount(goal, account);
        if (goalAccount == null) {
            throw new InvalidRequestException("Account is not associated with this goal");
        }

        // Create recurring contribution
        RecurringContribution contribution = new RecurringContribution();
        contribution.setGoal(goal);
        contribution.setAccount(account);
        contribution.setAmount(request.getAmount());
        contribution.setFrequency(request.getFrequency());
        contribution.setStartDate(request.getStartDate());
        contribution.setEndDate(request.getEndDate());
        contribution.setStatus(RecurringContribution.ContributionStatus.ACTIVE);
        contribution.setLastContributionDate(null);
        contribution.setNextContributionDate(calculateNextContributionDate(request.getStartDate(), request.getFrequency()));

        RecurringContribution savedContribution = recurringContributionRepository.save(contribution);

        return mapToRecurringContributionResponse(savedContribution);
    }

    @Transactional(readOnly = true)
    public List<RecurringContributionDto.RecurringContributionResponse> getRecurringContributionsForGoal(
            int userId, int goalId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Validate goal
        Goal goal = goalRepository.findById(goalId)
                .orElseThrow(() -> new ResourceNotFoundException("Goal not found with id: " + goalId));

        // Check if goal belongs to user
        if (goal.getUser().getId() != userId) {
            throw new ResourceNotFoundException("Goal not found with id: " + goalId);
        }

        // Get recurring contributions for goal
        List<RecurringContribution> contributions = recurringContributionRepository.findByGoal(goal);

        return contributions.stream()
                .map(this::mapToRecurringContributionResponse)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecurringContributionDto.RecurringContributionResponse> getAllRecurringContributions(int userId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get all recurring contributions for user
        List<RecurringContribution> contributions = recurringContributionRepository.findByUserId(userId);

        return contributions.stream()
                .map(this::mapToRecurringContributionResponse)
                .collect(Collectors.toList());
    }

    @Transactional
    public RecurringContributionDto.RecurringContributionResponse updateRecurringContribution(
            int userId, int contributionId, RecurringContributionDto.UpdateRecurringContributionRequest request) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get contribution
        RecurringContribution contribution = recurringContributionRepository.findById(contributionId)
                .orElseThrow(() -> new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId));

        // Check if contribution belongs to user's goal
        if (contribution.getGoal().getUser().getId() != userId) {
            throw new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId);
        }

        // Update fields if provided
        if (request.getAmount() != null && request.getAmount() > 0) {
            contribution.setAmount(request.getAmount());
        }

        if (request.getFrequency() != null) {
            contribution.setFrequency(request.getFrequency());
            // Recalculate next contribution date based on new frequency
            contribution.setNextContributionDate(
                    calculateNextContributionDate(
                            contribution.getLastContributionDate() != null ? contribution.getLastContributionDate() : LocalDate.now(),
                            request.getFrequency()));
        }

        if (request.getEndDate() != null) {
            contribution.setEndDate(request.getEndDate());
        }

        if (request.getStatus() != null) {
            contribution.setStatus(request.getStatus());
        }

        RecurringContribution updatedContribution = recurringContributionRepository.save(contribution);

        return mapToRecurringContributionResponse(updatedContribution);
    }

    @Transactional
    public void deleteRecurringContribution(int userId, int contributionId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get contribution
        RecurringContribution contribution = recurringContributionRepository.findById(contributionId)
                .orElseThrow(() -> new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId));

        // Check if contribution belongs to user's goal
        if (contribution.getGoal().getUser().getId() != userId) {
            throw new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId);
        }

        // Delete contribution
        recurringContributionRepository.delete(contribution);
    }

    // Scheduled task to process recurring contributions (runs daily at midnight)
    @Scheduled(cron = "0 0 0 * * ?")
    @Transactional
    public void processRecurringContributions() {
        logger.info("Processing recurring contributions...");
        LocalDate today = LocalDate.now();

        // Get all active recurring contributions due today or earlier
        List<RecurringContribution> dueContributions = recurringContributionRepository
                .findByStatusAndNextContributionDateLessThanEqual(
                        RecurringContribution.ContributionStatus.ACTIVE, today);

        for (RecurringContribution contribution : dueContributions) {
            try {
                processContribution(contribution);
            } catch (Exception e) {
                logger.error("Error processing recurring contribution {}: {}", 
                        contribution.getId(), e.getMessage());
                // Mark as failed if there's an error
                contribution.setStatus(RecurringContribution.ContributionStatus.FAILED);
                recurringContributionRepository.save(contribution);
            }
        }

        logger.info("Recurring contributions processing completed.");
    }

  
    @Transactional
public void processContribution(RecurringContribution contribution) {
    // Check if account has sufficient balance
    Accounts account = contribution.getAccount();
    double balance = account.getBalance();

    if (balance < contribution.getAmount()) {
        // Not enough funds - mark as failed
        contribution.setStatus(RecurringContribution.ContributionStatus.FAILED);
        contribution.setLastAttemptDate(LocalDate.now());
        recurringContributionRepository.save(contribution);
        
        logger.warn("Insufficient funds for recurring contribution ID {}: Account {} has balance {} but requires {}",
                contribution.getId(), account.getId(), balance, contribution.getAmount());
                
        throw new InsufficientFundsException("Insufficient funds in account for contribution");
    }

    // Make contribution to goal
    Goal goal = contribution.getGoal();
    
    // Update account balance
    account.setBalance(balance - contribution.getAmount());
    accountRepository.save(account);
    
    // Create contribution request for goal service
    // Use GoalDto.ContributeToGoalRequest instead of RecurringContributionDto.ContributeToGoalRequest
    com.pennypal.fintech.dto.GoalDto.ContributeToGoalRequest contributionRequest = 
        new com.pennypal.fintech.dto.GoalDto.ContributeToGoalRequest(
            goal.getId(),
            account.getId(),
            contribution.getAmount()
        );
    
    // Use goal service to make contribution
    goalService.contributeToGoal(goal.getUser().getId(), contributionRequest);
    
    // Update recurring contribution status
    contribution.setLastContributionDate(LocalDate.now());
    contribution.setLastAttemptDate(LocalDate.now());
    contribution.setNextContributionDate(calculateNextContributionDate(LocalDate.now(), contribution.getFrequency()));
    
    // Check if this was the last contribution (end date reached)
    if (contribution.getEndDate() != null && !LocalDate.now().isBefore(contribution.getEndDate())) {
        contribution.setStatus(RecurringContribution.ContributionStatus.COMPLETED);
    }
    
    recurringContributionRepository.save(contribution);
    
    logger.info("Successfully processed recurring contribution ID {}: ${} from account {} to goal {}",
            contribution.getId(), contribution.getAmount(), account.getId(), goal.getId());
}
    @Transactional
    public RecurringContributionDto.RecurringContributionResponse manuallyTriggerContribution(
            int userId, int contributionId) {
        // Validate user
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));

        // Get contribution
        RecurringContribution contribution = recurringContributionRepository.findById(contributionId)
                .orElseThrow(() -> new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId));

        // Check if contribution belongs to user's goal
        if (contribution.getGoal().getUser().getId() != userId) {
            throw new ResourceNotFoundException("Recurring contribution not found with id: " + contributionId);
        }

        // Process the contribution
        processContribution(contribution);

        return mapToRecurringContributionResponse(contribution);
    }

    // Helper methods
    private LocalDate calculateNextContributionDate(LocalDate fromDate, RecurringContribution.ContributionFrequency frequency) {
        switch (frequency) {
            case DAILY:
                return fromDate.plusDays(1);
            case WEEKLY:
                return fromDate.plusWeeks(1);
            case BIWEEKLY:
                return fromDate.plusWeeks(2);
            case MONTHLY:
                return fromDate.plusMonths(1);
            case QUARTERLY:
                return fromDate.plusMonths(3);
            case YEARLY:
                return fromDate.plusYears(1);
            default:
                return fromDate.plusMonths(1); // Default to monthly
        }
    }

    private RecurringContributionDto.RecurringContributionResponse mapToRecurringContributionResponse(
            RecurringContribution contribution) {
        return RecurringContributionDto.RecurringContributionResponse.builder()
                .id(contribution.getId())
                .goalId(contribution.getGoal().getId())
                .goalName(contribution.getGoal().getGoalName())
                .accountId(contribution.getAccount().getId())
                .accountName(contribution.getAccount().getAccountName())
                .amount(contribution.getAmount())
                .frequency(contribution.getFrequency())
                .startDate(contribution.getStartDate())
                .endDate(contribution.getEndDate())
                .status(contribution.getStatus())
                .lastContributionDate(contribution.getLastContributionDate())
                .nextContributionDate(contribution.getNextContributionDate())
                .lastAttemptDate(contribution.getLastAttemptDate())
                .build();
    }
}