package com.pennypal.fintech.service;

import com.azure.ai.formrecognizer.FormRecognizerClient;
import com.azure.ai.formrecognizer.FormRecognizerClientBuilder;
import com.azure.ai.formrecognizer.models.*;
import com.azure.core.credential.AzureKeyCredential;

import com.azure.core.util.polling.SyncPoller;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.azure.core.util.polling.PollResponse;
import com.azure.core.util.polling.LongRunningOperationStatus;
import com.pennypal.fintech.entity.Receipts;
import com.pennypal.fintech.entity.ReceiptItems;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.repository.ReceiptRepository;
import com.pennypal.fintech.repository.ReceiptItemRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.azure.core.util.BinaryData;
import com.pennypal.fintech.entity.ReceiptConsolidate;
import com.pennypal.fintech.repository.ReceiptConsolidateRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ReceiptService {
    private final String endpoint = "https://pennypalreceipt.cognitiveservices.azure.com/";
    private final String key = "6f0xv47U8VXNqFdZqUcl2KiDV9Locs2hSHX0LBZQ0COq9H4apf9pJQQJ99BBACYeBjFXJ3w3AAALACOGFP5j";
    private final String API_VERSION = "2023-07-31";
    private final HttpClient httpClient = HttpClient.newHttpClient();


    @Autowired
    private ReceiptRepository receiptRepository;

    @Autowired
    private ReceiptItemRepository receiptItemRepository;

    @Autowired
    private TransactionRepository transactionRepository; // Add the Transaction repository

     @Autowired
    private ReceiptConsolidateRepository receiptConsolidateRepository;
    
    public Map<String, Object> processReceipt(MultipartFile file) throws Exception {
        FormRecognizerClient client = new FormRecognizerClientBuilder()
                .credential(new AzureKeyCredential(key))
                .endpoint(endpoint)
                .buildClient();

        SyncPoller<FormRecognizerOperationResult, List<RecognizedForm>> poller =
                client.beginRecognizeReceipts(file.getInputStream(), file.getSize());

        List<RecognizedForm> receipts = poller.getFinalResult();
        if (receipts.isEmpty()) {
            throw new IllegalArgumentException("No receipt data recognized");
        }

        RecognizedForm form = receipts.get(0);
System.out.println(form.toString());
        Map<String, Object> extractedData = form.getFields().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getValueData() != null ? entry.getValue().getValueData().getText() : "N/A"
                ));
                extractedData.forEach((key, value) -> {
                    System.out.println("Key: " + key + ", Value: " + value);
                });
                

    String transactionDateString = (String) extractedData.get("TransactionDate");
    String transactionTotalString = (String) extractedData.get("Total");

    // Parse date and amount
    LocalDate transactionDate = parseDate(transactionDateString);
    BigDecimal transactionAmount = parseBigDecimal(transactionTotalString);

    // Find the matching transaction
    LocalDateTime transactionDateTime = (transactionDate != null) ? transactionDate.atStartOfDay() : null;

    // Find the matching transaction (if it exists)
    List<Transactions> matchingTransactions = new ArrayList<>();
    if (transactionDateTime != null && transactionAmount != null) {
        matchingTransactions = transactionRepository
                .findByTransactionDateAndTransactionAmount(transactionDateTime, transactionAmount);
    }
    
    
    // If found, add the transaction ID to the response data
   // If found, add the transaction ID and details to the response data
   if (!matchingTransactions.isEmpty()) {
    List<Map<String, Object>> transactionsList = matchingTransactions.stream()
            .map(transaction -> {
                Map<String, Object> transactionDetails = new HashMap<>();
                transactionDetails.put("transactionId", transaction.getId());
                transactionDetails.put("transactionDate", transaction.getTransactionDate());
                transactionDetails.put("transactionAmount", transaction.getTransactionAmount());
                transactionDetails.put("description", transaction.getDescription());
                transactionDetails.put("category", transaction.getCategory());
                return transactionDetails;
            })
            .collect(Collectors.toList());

    extractedData.put("matchingTransactions", transactionsList);
} else {
    extractedData.put("matchingTransactions", "No matching transactions found");
}

        FormField itemsField = form.getFields().get("Items");
        if (itemsField != null && FieldValueType.LIST.equals(itemsField.getValue().getValueType())) {
            List<FormField> items = itemsField.getValue().asList();
            List<Map<String, Object>> itemList = items.stream().map(item -> {
                Map<String, Object> itemMap = item.getValue().asMap().entrySet().stream()
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().getValueData() != null ? entry.getValue().getValueData().getText() : "N/A"
                        ));
                return itemMap;
            }).collect(Collectors.toList());
            extractedData.put("Items", itemList);
        } else {
            extractedData.put("Items", "N/A");
        }

      //  saveReceiptToDatabase(extractedData);
        return extractedData;
    }
    
     public Map<String, Object> processIdDocument(MultipartFile file) throws Exception {
        // Step 1: Send POST request with binary file
      InputStream inputStream = file.getInputStream(); // IOException must be caught or declared
HttpRequest postRequest = HttpRequest.newBuilder()
        .uri(URI.create(endpoint + "/formrecognizer/documentModels/prebuilt-idDocument:analyze?api-version=" + API_VERSION))
        .header("Content-Type", "application/octet-stream")
        .header("Ocp-Apim-Subscription-Key", key)
        .POST(HttpRequest.BodyPublishers.ofInputStream(() -> inputStream))
        .build();

        HttpResponse<Void> postResponse = httpClient.send(postRequest, HttpResponse.BodyHandlers.discarding());

        if (postResponse.statusCode() != 202) {
            throw new RuntimeException("Failed to start ID document analysis: " + postResponse.statusCode());
        }

        // Step 2: Extract operation-location URL
        String operationLocation = postResponse.headers().firstValue("operation-location")
                .orElseThrow(() -> new RuntimeException("Missing operation-location header"));

        // Step 3: Poll for final result
        // Get the result
    Map<String, Object> result = pollForResult(operationLocation);
    String rawJson = (String) result.get("raw_json");
   ObjectMapper mapper = new ObjectMapper();
    JsonNode root = mapper.readTree(rawJson);
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");

    // Extract basic ID fields
    Map<String, Object> fullAnalysis = new HashMap<>();
    // FirstName: Check GivenNames or FirstName
    fullAnalysis.put("FirstName", 
        fields.path("GivenNames").path("valueString").asText(
            fields.path("FirstName").path("valueString").asText("N/A")
        )
    );
    // LastName: Check Surname or LastName
    fullAnalysis.put("LastName", 
        fields.path("Surname").path("valueString").asText(
            fields.path("LastName").path("valueString").asText("N/A")
        )
    );
    // Nationality: Check Nationality or CountryRegion
    fullAnalysis.put("Nationality", 
        fields.path("Nationality").path("valueString").asText(
            fields.path("CountryRegion").path("valueString").asText("N/A")
        )
    );
    // Sex
    fullAnalysis.put("Sex", fields.path("Sex").path("valueString").asText("N/A"));
    // DateOfBirth: Check DateOfBirth or BirthDate
    fullAnalysis.put("DateOfBirth", 
        fields.path("DateOfBirth").path("valueDate").asText(
            fields.path("BirthDate").path("valueDate").asText("N/A")
        )
    );
    // DateOfExpiration: Check DateOfExpiration or ExpirationDate
    fullAnalysis.put("DateOfExpiration", 
        fields.path("DateOfExpiration").path("valueDate").asText(
            fields.path("ExpirationDate").path("valueDate").asText("N/A")
        )
    );

    String prettyJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(fullAnalysis);
  
    Map<String, Object> response = new HashMap<>();
    response.put("status", "success");
    response.put("message", "ID document saved");
    response.put("docType", "id");
response.put("fullAnalysis", fullAnalysis);
    response.put("prettyJson", prettyJson);
    response.put("rawJson", rawJson); 
    response.put("content", fields.toString()); // Keep raw content for fallback
    return response;
}

    private Map<String, Object> pollForResult(String operationLocation) throws Exception {
        HttpRequest getRequest = HttpRequest.newBuilder()
                .uri(URI.create(operationLocation))
                .header("Ocp-Apim-Subscription-Key", key)
                .GET()
                .build();
    
        int maxAttempts = 10;
        int attempt = 0;
    
        while (attempt < maxAttempts) {
            HttpResponse<String> getResponse = httpClient.send(getRequest, HttpResponse.BodyHandlers.ofString());
            String responseBody = getResponse.body();
    
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(responseBody);
            String status = root.path("status").asText();
    
            if ("succeeded".equalsIgnoreCase(status)) {
                // You can still extract fields if needed (optional)
                Map<String, Object> result = new HashMap<>();
                result.put("raw_json", responseBody); // Raw string you can save as BLOB
                return result;
            }
    
            if ("failed".equalsIgnoreCase(status)) {
                throw new RuntimeException("ID document analysis failed");
            }
    
            Thread.sleep(2000);
            attempt++;
        }
    
        throw new RuntimeException("Timed out waiting for ID document analysis result");
    }
    public Map<String, Object> processInvoiceDocument(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
    
        HttpRequest postRequest = HttpRequest.newBuilder()
            .uri(URI.create(endpoint + "/formrecognizer/documentModels/prebuilt-invoice:analyze?api-version=" + API_VERSION))
            .header("Content-Type", "application/octet-stream")
            .header("Ocp-Apim-Subscription-Key", key)
            .POST(HttpRequest.BodyPublishers.ofInputStream(() -> inputStream))
            .build();
    
        HttpResponse<Void> postResponse = httpClient.send(postRequest, HttpResponse.BodyHandlers.discarding());
    
        if (postResponse.statusCode() != 202) {
            throw new RuntimeException("Failed to start invoice analysis: " + postResponse.statusCode());
        }
    
        String operationLocation = postResponse.headers().firstValue("operation-location")
            .orElseThrow(() -> new RuntimeException("Missing operation-location header"));
    
        Map<String, Object> result = pollForResult(operationLocation);
        String rawJson = (String) result.get("raw_json");
    
        ObjectMapper mapper = new ObjectMapper();
    JsonNode root = mapper.readTree(rawJson);
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");

    // Extract specific invoice fields
    Map<String, Object> fullAnalysis = new HashMap<>();
    fullAnalysis.put("InvoiceNumber", fields.path("InvoiceId").path("valueString").asText("N/A"));
    fullAnalysis.put("VendorName", fields.path("VendorName").path("valueString").asText("N/A"));
    fullAnalysis.put("VendorAddress", fields.path("VendorAddress").path("valueString").asText("N/A"));
    fullAnalysis.put("InvoiceDate", fields.path("InvoiceDate").path("valueDate").asText("N/A"));
    fullAnalysis.put("InvoiceTotal", fields.path("InvoiceTotal").path("valueCurrency").path("amount").asText("N/A"));
    fullAnalysis.put("Tax", fields.path("Tax").path("valueCurrency").path("amount").asText("N/A"));

    String prettyJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(fullAnalysis);
    
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Invoice document processed");
        response.put("docType", "invoice");
response.put("fullAnalysis", fullAnalysis);
        response.put("prettyJson", prettyJson);
        response.put("rawJson", rawJson);
        
        // Print to console
        System.out.println("Invoice Analysis Result:\n" + prettyJson);
        
        return response;
    }
    
    public Map<String, Object> processTaxDocument(MultipartFile file) throws Exception {
        InputStream inputStream = file.getInputStream();
    
        // Step 1: Send the analysis request
        HttpRequest postRequest = HttpRequest.newBuilder()
            .uri(URI.create(endpoint + "/formrecognizer/documentModels/prebuilt-tax.us.w2:analyze?api-version=" + API_VERSION))
            .header("Content-Type", "application/octet-stream")
            .header("Ocp-Apim-Subscription-Key", key)
            .POST(HttpRequest.BodyPublishers.ofInputStream(() -> inputStream))
            .build();
    
        HttpResponse<Void> postResponse = httpClient.send(postRequest, HttpResponse.BodyHandlers.discarding());
    
        if (postResponse.statusCode() != 202) {
            throw new RuntimeException("Failed to start tax document analysis: " + postResponse.statusCode());
        }
    
        // Step 2: Poll for result
        String operationLocation = postResponse.headers().firstValue("operation-location")
            .orElseThrow(() -> new RuntimeException("Missing operation-location header"));
    
        Map<String, Object> result = pollForResult(operationLocation);
        String rawJson = (String) result.get("raw_json");

        Map<String, Object> extractedFields = extractTaxDocumentFields(rawJson);
    
        // Step 3: Parse JSON
       ObjectMapper mapper = new ObjectMapper();
    JsonNode root = mapper.readTree(rawJson);
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");

    // Extract specific tax document fields
    Map<String, Object> fullAnalysis = new HashMap<>();
    fullAnalysis.put("TaxYear", fields.path("TaxYear").path("valueString").asText("N/A"));
    fullAnalysis.put("EmployeeName", fields.path("Employee").path("valueObject").path("Name").path("valueString").asText("N/A"));
    fullAnalysis.put("EmployerName", fields.path("Employer").path("valueObject").path("Name").path("valueString").asText("N/A"));
    fullAnalysis.put("Wages", fields.path("Wages").path("valueCurrency").path("amount").asText("N/A"));
    fullAnalysis.put("FederalTaxWithheld", fields.path("FederalIncomeTaxWithheld").path("valueCurrency").path("amount").asText("N/A"));
    fullAnalysis.put("SocialSecurityNumber", fields.path("Employee").path("valueObject").path("SocialSecurityNumber").path("valueString").asText("N/A"));

    String prettyJson = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(fullAnalysis);
    
        Map<String, Object> response = new HashMap<>();
        response.put("status", "success");
        response.put("message", "Tax document processed");
        response.put("docType", "tax");
        response.put("extractedFields", extractedFields); // Add extractedFields
response.put("fullAnalysis", fullAnalysis);
        response.put("prettyJson", prettyJson);
        response.put("rawJson", rawJson);
    
        System.out.println("Tax Document Analysis Result:\n" + prettyJson);
    
        return response;
    }
    
public Map<String, Object> processContractDocument(MultipartFile file) throws Exception {
    InputStream inputStream = file.getInputStream();

    HttpRequest postRequest = HttpRequest.newBuilder()
        .uri(URI.create(endpoint + "/formrecognizer/documentModels/prebuilt-contract:analyze?api-version=" + API_VERSION))
        .header("Content-Type", "application/octet-stream")
        .header("Ocp-Apim-Subscription-Key", key)
        .POST(HttpRequest.BodyPublishers.ofInputStream(() -> inputStream))
        .build();

    HttpResponse<Void> postResponse = httpClient.send(postRequest, HttpResponse.BodyHandlers.discarding());

    if (postResponse.statusCode() != 202) {
        throw new RuntimeException("Failed to start contract analysis: " + postResponse.statusCode());
    }

    String operationLocation = postResponse.headers().firstValue("operation-location")
        .orElseThrow(() -> new RuntimeException("Missing operation-location header"));

    Map<String, Object> result = pollForResult(operationLocation);
    String rawJson = (String) result.get("raw_json");

    ObjectMapper mapper = new ObjectMapper();
    JsonNode root = mapper.readTree(rawJson);
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");

    // Extract structured fields
    Map<String, Object> fullAnalysis = new HashMap<>();
    fullAnalysis.put("ContractTitle", fields.path("ContractTitle").path("valueString").asText("N/A"));

    JsonNode parties = fields.path("Parties").path("valueArray");
    if (parties.isArray()) {
        List<String> partiesList = new ArrayList<>();
        for (JsonNode party : parties) {
            partiesList.add(party.path("valueString").asText("N/A"));
        }
        fullAnalysis.put("PartiesInvolved", String.join(", ", partiesList));
    } else {
        fullAnalysis.put("PartiesInvolved", fields.path("Parties").path("valueString").asText("N/A"));
    }

    fullAnalysis.put("EffectiveDate", fields.path("EffectiveDate").path("valueDate").asText("N/A"));
    fullAnalysis.put("ContractType", fields.path("ContractType").path("valueString").asText("N/A"));

    // NEW: Extract plain content
    String contractContent = root.path("analyzeResult").path("content").asText();

    // Prepare response
    Map<String, Object> response = new HashMap<>();
    response.put("status", "success");
    response.put("message", "Contract document processed");
    response.put("docType", "contract");
    response.put("extractedFields", extractContractDocumentFields(rawJson));
    response.put("fullAnalysis", fullAnalysis);
    response.put("prettyJson", mapper.writerWithDefaultPrettyPrinter().writeValueAsString(fullAnalysis));
    response.put("rawJson", rawJson);
    response.put("contractContent", contractContent); // ✅ Add this line

    return response;
}

public List<Map<String, Object>> getCategorizedItems(List<String> itemNames, Long userId) throws IOException, InterruptedException {
    if (itemNames.isEmpty()) return Collections.emptyList();

    ObjectMapper mapper = new ObjectMapper();

    // Prepare the JSON payload
    Map<String, Object> requestPayload = new HashMap<>();
    List<Map<String, String>> items = itemNames.stream()
            .map(name -> Map.of("name", name))
            .collect(Collectors.toList());

    requestPayload.put("items", items);
    requestPayload.put("user_id", userId);

    String requestBody = mapper.writeValueAsString(requestPayload);

    HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create("http://localhost:5000/categorize-receipt-items")) // replace with real URL
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(requestBody))
            .build();

    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

    if (response.statusCode() == 200) {
        Map<String, Object> responseMap = mapper.readValue(response.body(), new TypeReference<>() {});
        return (List<Map<String, Object>>) responseMap.get("items");
    } else {
        System.out.println("Failed to categorize items: " + response.body());
        return Collections.emptyList();
    }
}

    
 public Receipts saveReceiptToDatabase(Map<String, Object> receiptData) {
    System.out.println("Processing receipt data: " + receiptData);
    
    Receipts receipt = new Receipts();

    // Set common document properties
    receipt.setUserId(Long.valueOf(receiptData.get("userId").toString()));

    receipt.setDocType((String) receiptData.get("docType"));
     receipt.setDocName((String) receiptData.get("docName")); // Add this line
// Set receipt-level category
receipt.setCategory(
    receiptData.get("category") != null ? receiptData.get("category").toString() : "Uncategorized"
);
    receipt.setQrData((String) receiptData.get("qrData"));
    receipt.setUploadDate(LocalDate.now());
    receipt.setSavedFilePath((String) receiptData.get("savedFilePath"));
    receipt.setScannedCopyPath((String) receiptData.get("scannedCopyPath"));

    // Handle size conversion
    Object sizeObj = receiptData.get("size");
    long sizeInBytes = 0;
    
    if (sizeObj != null) {
        if (sizeObj instanceof String) {
            sizeInBytes = Long.parseLong((String) sizeObj);
        } else if (sizeObj instanceof Number) {
            sizeInBytes = ((Number) sizeObj).longValue();
        }
    }
    receipt.setSize(sizeInBytes);
    
  Object transactionIdObj = receiptData.get("transactionId");

if (transactionIdObj != null && !transactionIdObj.toString().equalsIgnoreCase("null")) {
    try {
        Integer transId = Integer.parseInt(transactionIdObj.toString());
        Optional<Transactions> transactionOpt = transactionRepository.findById(transId);
        if (transactionOpt.isPresent()) {
            receipt.setTransaction(transactionOpt.get());
            System.out.println("Associated receipt with transaction ID: " + transId);
        } else {
            System.out.println("Transaction with ID " + transId + " not found");
        }
    } catch (NumberFormatException e) {
        System.out.println("Invalid transaction ID format: " + transactionIdObj);
    }
} else {
    System.out.println("No transaction associated with the receipt.");
}


    // Only process receipt-specific fields if this is a receipt
    if ("receipt".equalsIgnoreCase(receipt.getDocType())) {
        // Extract receipt fields from Azure response
        receipt.setMerchantName((String) receiptData.getOrDefault("MerchantName", "Unknown"));
        receipt.setMerchantAddress((String) receiptData.getOrDefault("MerchantAddress", "Unknown"));
        receipt.setMerchantPhno((String) receiptData.getOrDefault("MerchantPhoneNumber", "Unknown"));
        
        // Parse transaction date
        String transactionDateString = (String) receiptData.get("TransactionDate");
        String transactionTimeString = (String) receiptData.get("TransactionTime");
        if (transactionDateString != null) {
            try {
                LocalDate date = parseDate(transactionDateString);
                if (date != null) {
                    LocalTime time = transactionTimeString != null ? 
                        LocalTime.parse(transactionTimeString) : LocalTime.NOON;
                    receipt.setTransactionDate(LocalDateTime.of(date, time));
                }
            } catch (Exception e) {
                System.out.println("Error parsing date: " + e.getMessage());
                receipt.setTransactionDate(LocalDateTime.now());
            }
        } else {
            receipt.setTransactionDate(LocalDateTime.now());
        }
        
        // Parse financial data
        receipt.setTransactionSubtotal(parseBigDecimal((String) receiptData.get("Subtotal")));
        receipt.setTransTax(parseBigDecimal((String) receiptData.get("Tax")));
        receipt.setTransTotal(parseBigDecimal((String) receiptData.get("Total")));
        receipt.setTransReturnby((String) receiptData.getOrDefault("ReturnByDate", "N/A"));
        
        // Save receipt items if available
        Receipts savedReceipt = receiptRepository.save(receipt);

        if (receiptData.containsKey("Items") && receiptData.get("Items") instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> items = (List<Map<String, Object>>) receiptData.get("Items");

            // Extract item names to send to Flask for categorization
            List<String> itemNames = items.stream()
                    .map(item -> (String) item.getOrDefault("Name", "Unknown"))
                    .collect(Collectors.toList());

            // Call your categorization service and get back categorized items
            List<Map<String, Object>> categorizedItems;
            try {
                categorizedItems = getCategorizedItems(itemNames, savedReceipt.getUserId());
            } catch (Exception e) {
                System.out.println("Failed to categorize items: " + e.getMessage());
                // fallback: assign Uncategorized for all
                categorizedItems = itemNames.stream()
                    .map(name -> Map.<String, Object>of("name", name, "category", "Uncategorized"))
                    .collect(Collectors.toList());
            }

            // Save each item with its category
            for (Map<String, Object> catItem : categorizedItems) {
                String itemName = (String) catItem.get("name");
                String itemCategory = (String) catItem.get("category");
System.out.println("Item: " + itemName + ", Category: " + itemCategory);

                // Find the original item data for price etc.
                Optional<Map<String, Object>> originalItemOpt = items.stream()
                    .filter(i -> itemName.equalsIgnoreCase((String) i.getOrDefault("Name", "")))
                    .findFirst();

                if (originalItemOpt.isPresent()) {
                    Map<String, Object> itemData = originalItemOpt.get();

                    ReceiptItems item = new ReceiptItems();
                    item.setReceiptId(savedReceipt.getId());
                    item.setItem(itemName);
                    item.setCategory(itemCategory); // <== set category here
                    item.setPrice(parseBigDecimal((String) itemData.get("TotalPrice")));
                    item.setQuantity(1); // default quantity

                    receiptItemRepository.save(item);

                    // Update consolidate table
                    Optional<ReceiptConsolidate> existingItem = receiptConsolidateRepository
                        .findByUserIdAndReceiptItem(savedReceipt.getUserId(), item.getItem());

                    if (existingItem.isPresent()) {
                        ReceiptConsolidate consolidate = existingItem.get();
                        BigDecimal updatedPrice = consolidate.getPrice().add(item.getPrice());
                        consolidate.setPrice(updatedPrice);
                        receiptConsolidateRepository.save(consolidate);
                    } else {
                        ReceiptConsolidate newConsolidate = new ReceiptConsolidate();
                        newConsolidate.setReceiptId(savedReceipt.getId());
                        newConsolidate.setReceiptItem(item.getItem());
                        newConsolidate.setPrice(item.getPrice());
                        newConsolidate.setUserId(savedReceipt.getUserId());
                        receiptConsolidateRepository.save(newConsolidate);
                    }
                }
            }
            return savedReceipt;
        }

        // If no items present, just return saved receipt
        return savedReceipt;
    }
    else if ("id".equalsIgnoreCase(receipt.getDocType()) || "invoice".equalsIgnoreCase(receipt.getDocType()) || "tax".equalsIgnoreCase(receipt.getDocType()) || "contract".equalsIgnoreCase(receipt.getDocType())) {
        // Save the raw JSON analysis for ID documents and invoices
        String rawJson = (String) receiptData.get("rawJson");
        if (rawJson != null) {
            receipt.setRawBlob(rawJson.getBytes(StandardCharsets.UTF_8));
        }
    }
    
    // For non-receipt documents or receipts without items
    return receiptRepository.save(receipt);
}
    private BigDecimal parseBigDecimal(String value) {
        if (value == null || value.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        try {
            // Log the original value
            System.out.println("Parsing decimal value: '" + value + "'");
            
            // Check if the value is enclosed in parentheses (indicating a negative amount)
            if (value.startsWith("(") && value.endsWith(")")) {
                // Remove parentheses and parse as a negative value
                value = value.substring(1, value.length() - 1);  // Remove parentheses
                BigDecimal parsedValue = new BigDecimal(value.replaceAll("[^0-9.]", ""));
                return parsedValue.negate(); // Return the negative value
            }
            
            // Otherwise, remove dollar signs, commas, or any other non-numeric characters and parse
            value = value.replaceAll("[^0-9.]", "");
            
            // Check if the value is still valid after cleaning
            if (value.isEmpty()) {
                return BigDecimal.ZERO;
            }
            
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            System.out.println("Error parsing BigDecimal: " + e.getMessage() + " for value: " + value);
            return BigDecimal.ZERO; // Default to 0 if parsing fails
        }
    }
    // Helper method to try parsing multiple date formats
    private LocalDate parseDate(String dateString) {
        if (dateString == null || dateString.isEmpty()) {
            return null; // Return null if the date string is null or empty
        }
        List<DateTimeFormatter> formatters = new ArrayList<>();
        formatters.add(DateTimeFormatter.ofPattern("dd/MM/yyyy"));
        formatters.add(DateTimeFormatter.ofPattern("MM/dd/yyyy"));
        formatters.add(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
        formatters.add(DateTimeFormatter.ofPattern("MM/dd/yy"));
        formatters.add(DateTimeFormatter.ofPattern("dd/MM/yy"));
        formatters.add(DateTimeFormatter.ofPattern("yy/MM/dd"));



        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(dateString, formatter);
            } catch (DateTimeParseException e) {
                // Ignore and try the next format
            }
        }
        return null; // Return null if no format was successful
    }
      public Map<String, Object> getReceiptDetailsByTransactionId(int transactionId) {
        Map<String, Object> receiptDetails = new HashMap<>();

        // Fetch the receipt by transaction_id
        Optional<Receipts> receiptOptional = receiptRepository.findByTransactionId(transactionId);
        if (receiptOptional.isEmpty()) {
            throw new IllegalArgumentException("No receipt found for transaction ID: " + transactionId);
        }

        Receipts receipt = receiptOptional.get();

        // Add receipt details to the response
        receiptDetails.put("id", receipt.getId());
        receiptDetails.put("receiptName", receipt.getSavedFilePath());
        receiptDetails.put("insertTime", receipt.getInsertTime());
        receiptDetails.put("merchantName", receipt.getMerchantName());
        receiptDetails.put("merchantAddress", receipt.getMerchantAddress());
        receiptDetails.put("merchantPhno", receipt.getMerchantPhno());
        receiptDetails.put("transactionDate", receipt.getTransactionDate());
        receiptDetails.put("transactionSubtotal", receipt.getTransactionSubtotal());
        receiptDetails.put("transTax", receipt.getTransTax());
        receiptDetails.put("transTotal", receipt.getTransTotal());
        receiptDetails.put("transReturnby", receipt.getTransReturnby());
        receiptDetails.put("savedFilePath", receipt.getSavedFilePath()); 
        // Fetch the associated items for the receipt
        List<ReceiptItems> items = receiptItemRepository.findByReceiptId(receipt.getId());
        List<Map<String, Object>> itemList = items.stream().map(item -> {
            Map<String, Object> itemMap = new HashMap<>();
            itemMap.put("item", item.getItem());
            itemMap.put("itemType", item.getItemType());
            itemMap.put("quantity", item.getQuantity());
            itemMap.put("weight", item.getWeight());
            itemMap.put("price", item.getPrice());
            return itemMap;
        }).collect(Collectors.toList());

        receiptDetails.put("items", itemList);

        return receiptDetails;
    }

    public List<Integer> getReceiptTransactionIds() {
        return receiptRepository.findAllTransactionIds();
    } 

    public Map<String, Object> getDocumentDetails(int documentId) {
        Optional<Receipts> receiptOptional = receiptRepository.findById(documentId);
        if (receiptOptional.isEmpty()) {
            throw new IllegalArgumentException("No document found with ID: " + documentId);
        }
    
        Receipts document = receiptOptional.get();
        Map<String, Object> documentDetails = new HashMap<>();
    
        // Common fields
        documentDetails.put("id", document.getId());
        documentDetails.put("docType", document.getDocType());
         documentDetails.put("docName", document.getDocName());
        documentDetails.put("category", document.getCategory());
        documentDetails.put("uploadDate", document.getUploadDate());
        documentDetails.put("savedFilePath", document.getSavedFilePath());
        documentDetails.put("scannedCopyPath", document.getScannedCopyPath());
    
        // Handle different document types
        if ("receipt".equalsIgnoreCase(document.getDocType())) {
            // Receipt specific fields
            documentDetails.put("merchantName", document.getMerchantName());
            documentDetails.put("merchantAddress", document.getMerchantAddress());
            documentDetails.put("merchantPhno", document.getMerchantPhno());
            documentDetails.put("transactionDate", document.getTransactionDate());
            documentDetails.put("transactionSubtotal", document.getTransactionSubtotal());
            documentDetails.put("transTax", document.getTransTax());
            documentDetails.put("transTotal", document.getTransTotal());
            documentDetails.put("transReturnby", document.getTransReturnby());
    
            // Fetch receipt items
            List<ReceiptItems> items = receiptItemRepository.findByReceiptId(document.getId());
            List<Map<String, Object>> itemList = items.stream().map(item -> {
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("item", item.getItem());
                itemMap.put("price", item.getPrice());
                return itemMap;
            }).collect(Collectors.toList());
            documentDetails.put("items", itemList);
        } else if ("id".equalsIgnoreCase(document.getDocType())|| "invoice".equalsIgnoreCase(document.getDocType()) || "tax".equalsIgnoreCase(document.getDocType()) || "contract".equalsIgnoreCase(document.getDocType())) {
            // ID document - parse the raw JSON blob
            if (document.getRawBlob() != null) {
                try {
                    String rawJson = new String(document.getRawBlob(), StandardCharsets.UTF_8);
                    if ("id".equalsIgnoreCase(document.getDocType())) {
                        // For ID documents, extract structured fields
                        Map<String, Object> extractedFields = extractIdDocumentFields(rawJson);
                        documentDetails.put("extractedFields", extractedFields);
                    } else if ("invoice".equalsIgnoreCase(document.getDocType())) {
                        // For invoices, extract structured fields
                        Map<String, Object> extractedFields = extractInvoiceFields(rawJson);
                        documentDetails.put("extractedFields", extractedFields);
                       }   else if ("tax".equalsIgnoreCase(document.getDocType())) {
                            // For invoices, extract structured fields
                            Map<String, Object> extractedFields = extractTaxDocumentFields(rawJson);
                            documentDetails.put("extractedFields", extractedFields);
                    }  else if ("contract".equalsIgnoreCase(document.getDocType())) {
                            // For invoices, extract structured fields
                            Map<String, Object> extractedFields = extractContractDocumentFields(rawJson);
                            documentDetails.put("extractedFields", extractedFields);
                    }
                    
                    // Still include raw analysis if needed
                    ObjectMapper mapper = new ObjectMapper();
                    Map<String, Object> jsonData = mapper.readValue(rawJson, Map.class);
                    documentDetails.put("rawAnalysis", jsonData);
                } catch (Exception e) {
                    documentDetails.put("analysisError", "Failed to parse document data");
                }
            }
        }
    
        return documentDetails;
    }

    private String getFieldValue(JsonNode node, String... fieldNames) {
        for (String fieldName : fieldNames) {
            JsonNode field = node.path(fieldName);
            if (!field.isMissingNode()) {
                JsonNode valueNode = field.path("valueString");
                if (!valueNode.isMissingNode()) return valueNode.asText("N/A");

                valueNode = field.path("valueDate");
                if (!valueNode.isMissingNode()) return valueNode.asText("N/A");

                valueNode = field.path("valueCurrency");
                if (!valueNode.isMissingNode()) return valueNode.path("amount").asText("N/A");

                valueNode = field.path("valueNumber");
                if (!valueNode.isMissingNode()) return valueNode.asText("N/A");

                valueNode = field.path("valueArray");
                if (!valueNode.isMissingNode() && valueNode.isArray()) {
                    List<String> values = new ArrayList<>();
                    for (JsonNode item : valueNode) {
                        values.add(item.path("valueString").asText("N/A"));
                    }
                    return String.join(", ", values);
                }

                valueNode = field.path("valueObject");
                if (!valueNode.isMissingNode()) {
                    JsonNode nested = valueNode.path("Name");
                    if (!nested.isMissingNode()) return nested.path("valueString").asText("N/A");
                }

                valueNode = field.path("content");
                if (!valueNode.isMissingNode()) return valueNode.asText("N/A");

                valueNode = field.path("text");
                if (!valueNode.isMissingNode()) return valueNode.asText("N/A");
            }
        }
        return "N/A";
    }

  private Map<String, Object> extractIdDocumentFields(String rawJson) throws Exception {
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> result = new HashMap<>();
    
    JsonNode root = mapper.readTree(rawJson);
    
    // Try different paths where fields might be located
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");
    if (fields.isMissingNode()) {
        fields = root.path("fields");
    }
    
    // Extract ID document fields
    result.put("documentType", getFieldValue(fields, "DocumentType", "Type", "documentType"));
    result.put("country", getFieldValue(fields, "Country", "CountryRegion", "Nationality"));
    result.put("firstName", getFieldValue(fields, "FirstName", "GivenNames", "givenName"));
    result.put("lastName", getFieldValue(fields, "LastName", "Surname", "familyName"));
    result.put("dateOfBirth", getFieldValue(fields, "DateOfBirth", "DateOfBirth", "birthDate"));
    result.put("sex", getFieldValue(fields, "Sex", "Gender", "sex"));
    result.put("expiryDate", getFieldValue(fields, "ExpirationDate", "DateOfExpiration", "expiryDate"));
    result.put("documentNumber", getFieldValue(fields, "DocumentNumber", "PassportNo", "documentNumber"));
    result.put("placeOfBirth", getFieldValue(fields, "PlaceOfBirth", "PlaceOfBirth", "birthPlace"));
    
    return result;
}
    // Updated getFieldValue to try multiple field names
   private Map<String, Object> extractInvoiceFields(String rawJson) throws Exception {
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> result = new HashMap<>();
    
    JsonNode root = mapper.readTree(rawJson);
    
    // Try different paths where fields might be located
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");
    if (fields.isMissingNode()) {
        fields = root.path("fields");
    }
    
    // Extract invoice fields
    result.put("invoiceNumber", getFieldValue(fields, "InvoiceNumber", "Invoice#", "InvoiceNo"));
    result.put("invoiceDate", getFieldValue(fields, "InvoiceDate", "Date"));
    result.put("dueDate", getFieldValue(fields, "DueDate", "PaymentDueDate"));
    result.put("vendorName", getFieldValue(fields, "VendorName", "CompanyName", "YourCompany"));
    result.put("vendorAddress", getFieldValue(fields, "VendorAddress", "CompanyAddress"));
    result.put("customerName", getFieldValue(fields, "CustomerName", "ClientName"));
    result.put("customerAddress", getFieldValue(fields, "CustomerAddress", "ClientAddress"));
    result.put("subtotal", getFieldValue(fields, "Subtotal", "AmountDue"));
    result.put("totalTax", getFieldValue(fields, "TotalTax", "Tax"));
    result.put("discount", getFieldValue(fields, "Discount", "DiscountAmount"));
    result.put("total", getFieldValue(fields, "Total", "AmountDue", "TotalAmount"));
    result.put("paymentTerms", getFieldValue(fields, "PaymentTerms", "Terms"));
    
    // Extract line items if available
    JsonNode itemsNode = fields.path("Items");
    if (itemsNode.isMissingNode()) {
        itemsNode = fields.path("LineItems");
    }
    
    if (!itemsNode.isMissingNode() && itemsNode.isArray()) {
        List<Map<String, String>> lineItems = new ArrayList<>();
        for (JsonNode item : itemsNode) {
            JsonNode itemFields = item.path("valueObject");
            if (itemFields.isMissingNode()) {
                itemFields = item; // Fallback to item node itself
            }
            Map<String, String> lineItem = new HashMap<>();
            lineItem.put("description", getFieldValue(itemFields, "Description", "Product"));
            lineItem.put("quantity", getFieldValue(itemFields, "Quantity"));
            lineItem.put("unitPrice", getFieldValue(itemFields, "UnitPrice", "UnitCost"));
            lineItem.put("amount", getFieldValue(itemFields, "Amount", "LineTotal"));
            lineItems.add(lineItem);
        }
        result.put("items", lineItems);
    }
    
    return result;
}

  private Map<String, Object> extractTaxDocumentFields(String rawJson) throws Exception {
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> result = new HashMap<>();
    
    JsonNode root = mapper.readTree(rawJson);
    
    // Try different paths where fields might be located
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");
    if (fields.isMissingNode()) {
        fields = root.path("fields");
    }
    
    // Extract basic tax document fields
    result.put("taxYear", getFieldValue(fields, "TaxYear", "Year"));
    result.put("employeeName", getFieldValue(fields, "EmployeeName", "e_EmployeeName", "TaxpayerName"));
    result.put("employerName", getFieldValue(fields, "EmployerName", "c_EmployerName", "PayerName"));
    result.put("wagesTips", getFieldValue(fields, "Box1_Wages", "1_WagesTips", "WagesTipsOtherCompensation", "Compensation"));
    result.put("federalIncomeTax", getFieldValue(fields, "Box2_FederalTax", "2_FederalIncomeTax", "FederalIncomeTaxWithheld"));
    
    return result;
}

private Map<String, Object> extractContractDocumentFields(String rawJson) throws Exception {
    ObjectMapper mapper = new ObjectMapper();
    Map<String, Object> result = new HashMap<>();
    
    JsonNode root = mapper.readTree(rawJson);
    
    // Try different paths where fields might be located
    JsonNode fields = root.path("analyzeResult").path("documents").get(0).path("fields");
    if (fields.isMissingNode()) {
        fields = root.path("fields");
    }
    
    // Extract basic contract document fields
    result.put("contractTitle", getFieldValue(fields, "ContractTitle", "Title"));
    result.put("partiesInvolved", getFieldValue(fields, "Parties", "PartiesInvolved"));
    result.put("effectiveDate", getFieldValue(fields, "EffectiveDate", "StartDate"));
    result.put("contractType", getFieldValue(fields, "ContractType", "Type"));
    
    return result;
}
public boolean deleteReceiptById(Integer id) {
    Optional<Receipts> receiptOpt = receiptRepository.findById(id);
    if (receiptOpt.isPresent()) {
        receiptRepository.deleteById(id);
        return true;
    }
    return false;
}


}
 