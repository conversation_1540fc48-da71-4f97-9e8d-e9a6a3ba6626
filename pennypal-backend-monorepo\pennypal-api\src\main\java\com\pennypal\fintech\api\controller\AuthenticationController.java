package com.pennypal.fintech.api.controller;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.dto.OtpUserDto;
import com.pennypal.fintech.dto.PennypalErrorResponse;
import com.pennypal.fintech.dto.UserDto;
import com.pennypal.fintech.dto.UserPermissionDto;

import com.pennypal.fintech.service.AclService;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.service.UserService;
import com.pennypal.fintech.service.JWTService;
import com.pennypal.fintech.service.OtpService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/v1/auth")
@Slf4j
@Validated
@Tag(name = "Authentication", description = "Authentication and Authorization APIs")
public class AuthenticationController {

    @Autowired
    private UserService userService;

    @Autowired
    private JWTService jwtService;

    @Autowired
    private OtpService otpService;

    @Autowired
    private AclService aclService;

    // ================== REGISTRATION ENDPOINTS ==================

    @Operation(summary = "Register user via email verification")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Verification email sent successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "User already exists"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/register")
    public ResponseEntity<?> registerUser(
        @Parameter(description = "User registration details", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            log.info("User registration initiated for email: {}", userDto.getEmailId());
            
            ResponseEntity<?> result = userService.registerUser(userDto);
            
            log.info("Registration verification email sent successfully for: {}", userDto.getEmailId());
            return result;
            
        } catch (RuntimeException e) {
            log.error("Registration failed for email: {} - Error: {}", userDto.getEmailId(), e.getMessage());
            
            HttpStatus status = determineHttpStatus(e.getMessage());
            PennypalErrorResponse errorResponse = new PennypalErrorResponse(
                status.value(), e.getMessage(), Instant.now().toEpochMilli());
            
            return new ResponseEntity<>(errorResponse, status);
        }
    }

    // Sent from setPassword page; to set password and register user
    @Operation(summary = "Verify user via email for registration")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid verification token"),
        @ApiResponse(responseCode = "409", description = "User already registered"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/verify")
    public ResponseEntity<?> verifyRegistration(
        @Parameter(description = "User verification details with token", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            log.info("User verification initiated for encoded email");
            
            UserDto verifiedUser = userService.verifyUser(userDto);
            
            log.info("User registered successfully with ID: {} and isPrimary: {}", 
                verifiedUser.getId(), verifiedUser.getIsPrimary());
            
            return ResponseEntity.ok(verifiedUser);
            
        } catch (RuntimeException e) {
            log.error("User verification failed - Error: {}", e.getMessage());
            
            HttpStatus status = determineHttpStatus(e.getMessage());
            PennypalErrorResponse errorResponse = new PennypalErrorResponse(
                status.value(), e.getMessage(), Instant.now().toEpochMilli());
            
            return new ResponseEntity<>(errorResponse, status);
        }
    }

    // Registration via email (OTP)
    @Operation(summary = "Register user via email (OTP)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP sent to email"),
        @ApiResponse(responseCode = "400", description = "Invalid request"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/register/email")
    public ResponseEntity<?> registerViaEmailOtp(
        @Parameter(description = "User details for email OTP registration", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            log.info("Email OTP registration initiated for: {}", userDto.getEmailId());
            
            otpService.sendEmailOtp(userDto.getEmailId());
            
            return ResponseEntity.ok(Map.of(
                "message", "OTP sent to email successfully",
                "email", userDto.getEmailId()
            ));
            
        } catch (Exception e) {
            log.error("Email OTP registration failed for: {} - Error: {}", userDto.getEmailId(), e.getMessage());
            return handleException(e);
        }
    }

    // Verification of OTP via email
    @Operation(summary = "Verify OTP via email")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid OTP"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/verify/email")
    public ResponseEntity<?> verifyEmailOtpRegistration(
        @Parameter(description = "OTP verification details for email registration", required = true)
        @Valid @RequestBody OtpUserDto otpRequest) {
        try {
            log.info("Email OTP verification initiated for: {}", otpRequest.getEmail());
            
            boolean isOtpValid = otpService.verifyEmailOtp(otpRequest.getEmail(), otpRequest.getOtp());
            
            if (!isOtpValid) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "Invalid or expired OTP"));
            }
            
            UserDto registeredUser = userService.verifyUserOAuth(otpRequest.getUserDto());
            
            log.info("User registered via email OTP successfully with ID: {} and isPrimary: {}", 
                registeredUser.getId(), registeredUser.getIsPrimary());
            
            return ResponseEntity.ok(registeredUser);
            
        } catch (Exception e) {
            log.error("Email OTP verification failed for: {} - Error: {}", otpRequest.getEmail(), e.getMessage());
            return handleException(e);
        }
    }

    // Registration via phone (OTP)
    @Operation(summary = "Register user via phone (OTP)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP sent to phone"),
        @ApiResponse(responseCode = "400", description = "Invalid request"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/register/phone")
    public ResponseEntity<?> registerViaPhoneOtp(
        @Parameter(description = "User details for phone OTP registration", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            log.info("Phone OTP registration initiated for: {}", userDto.getPhoneNumber());
            
            otpService.sendPhoneOtp(userDto.getPhoneNumber());
            
            return ResponseEntity.ok(Map.of(
                "message", "OTP sent to phone successfully",
                "phone", userDto.getPhoneNumber()
            ));
            
        } catch (Exception e) {
            log.error("Phone OTP registration failed for: {} - Error: {}", userDto.getPhoneNumber(), e.getMessage());
            return handleException(e);
        }
    }

    // Verification of OTP via phone
    @Operation(summary = "Verify OTP via phone")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "403", description = "Invalid OTP"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/verify/phone")
    public ResponseEntity<?> verifyPhoneOtpRegistration(
        @Parameter(description = "OTP verification details for phone registration", required = true)
        @Valid @RequestBody OtpUserDto otpRequest) {
        try {
            log.info("Phone OTP verification initiated for: {}", otpRequest.getPhoneNumber());
            
            boolean isOtpValid = otpService.verifyPhoneOtp(otpRequest.getPhoneNumber(), otpRequest.getOtp());
            
            if (!isOtpValid) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "Invalid or expired OTP"));
            }
            
            UserDto registeredUser = userService.verifyUserOAuth(otpRequest.getUserDto());
            
            log.info("User registered via phone OTP successfully with ID: {} and isPrimary: {}", 
                registeredUser.getId(), registeredUser.getIsPrimary());
            
            return ResponseEntity.ok(registeredUser);
            
        } catch (Exception e) {
            log.error("Phone OTP verification failed for: {} - Error: {}", otpRequest.getPhoneNumber(), e.getMessage());
            return handleException(e);
        }
    }

    // Google OAuth registration
    @Operation(summary = "Register user via Google OAuth")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid Google token"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/register/google")
    public ResponseEntity<?> registerViaGoogle(
        @Parameter(description = "Google OAuth token", required = true)
        @RequestBody Map<String, String> body) {
        try {
            log.info("Register Via Google Controller called");
            String idToken = body.get("token");
            if (idToken == null || idToken.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Google token is required"));
            }
            
            log.info("Google OAuth registration initiated");
            
            String googleToken = idToken.replace("\"", "").trim();
            UserDto registeredUser = userService.registerGoogleUser(googleToken);
            
            log.info("User registered via Google OAuth successfully with ID: {} and isPrimary: {}", 
                registeredUser.getId(), registeredUser.getIsPrimary());
            
            return ResponseEntity.ok(registeredUser);
            
        } catch (RuntimeException e) {
            log.error("Google OAuth registration failed - Error: {}", e.getMessage());
            return handleException(e);
        }
    }

    // Apple OAuth registration
    @Operation(summary = "Register user via Apple OAuth")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User registered successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid Apple token"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/register/apple")
    public ResponseEntity<?> registerViaApple(@RequestParam("code") String code) {
        try {
            if (code == null || code.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Apple authorization code is required"));
            }
            
            log.info("Apple OAuth registration initiated");
            
            UserDto registeredUser = userService.registerAppleUser(code);
            
            log.info("User registered via Apple OAuth successfully with ID: {} and isPrimary: {}", 
                registeredUser.getId(), registeredUser.getIsPrimary());
            
            return ResponseEntity.ok(registeredUser);
            
        } catch (Exception e) {
            log.error("Apple OAuth registration failed - Error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Apple registration failed: " + e.getMessage()));
        }
    }

    // ================== SIGN-IN ENDPOINTS ==================

    @Operation(summary = "Sign in user with email and password")
    @PostMapping("/signin")
    public ResponseEntity<?> signInUser(
        @Parameter(description = "User sign-in credentials", required = true)
        @Valid @RequestBody UserDto userDto, 
        HttpServletResponse response) {
        try {
            log.info("User sign-in initiated for email: {}", userDto.getEmailId());
            
            UserDto authenticatedUser = userService.signInUser(userDto, response);
            
            log.info("User signed in successfully with ID: {} and isPrimary: {}", 
                authenticatedUser.getId(), authenticatedUser.getIsPrimary());
            
            return ResponseEntity.ok(authenticatedUser);
            
        } catch (RuntimeException e) {
            log.error("Sign-in failed for email: {} - Error: {}", userDto.getEmailId(), e.getMessage());
            return handleException(e);
        }
    }

    @Operation(summary = "Generate OTP for sign-in")
    @PostMapping("/signin/otp/generate")
    public ResponseEntity<?> generateSignInOtp(
        @Parameter(description = "User details for OTP generation", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            if (userDto.getEmailId() != null) {
                log.info("Generating sign-in OTP for email: {}", userDto.getEmailId());
                otpService.sendEmailOtp(userDto.getEmailId());
                return ResponseEntity.ok(Map.of(
                    "message", "OTP sent to email successfully",
                    "email", userDto.getEmailId()
                ));
            } else if (userDto.getPhoneNumber() != null) {
                log.info("Generating sign-in OTP for phone: {}", userDto.getPhoneNumber());
                otpService.sendPhoneOtp(userDto.getPhoneNumber());
                return ResponseEntity.ok(Map.of(
                    "message", "OTP sent to phone successfully",
                    "phone", userDto.getPhoneNumber()
                ));
            } else {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Email or phone number is required"));
            }
        } catch (Exception e) {
            log.error("OTP generation failed - Error: {}", e.getMessage());
            return handleException(e);
        }
        // return ResponseEntity.ok("OTP sent to email");
    }

    @Operation(summary = "Verify OTP for sign-in")
    @PostMapping("/signin/otp/verify")
    public ResponseEntity<?> verifySignInOtp(
        @Parameter(description = "OTP verification details for sign-in", required = true)
        @Valid @RequestBody UserDto userDto, 
        HttpServletResponse response) {
        try {
            boolean isOtpValid = false;
            
            if (userDto.getEmailId() != null) {
                log.info("Verifying sign-in OTP for email: {}", userDto.getEmailId());
                isOtpValid = otpService.verifyEmailOtp(userDto.getEmailId(), userDto.getOtp());
            } else if (userDto.getPhoneNumber() != null) {
                log.info("Verifying sign-in OTP for phone: {}", userDto.getPhoneNumber());
                isOtpValid = otpService.verifyPhoneOtp(userDto.getPhoneNumber(), userDto.getOtp());
            } else {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Email or phone number is required"));
            }
            
            if (!isOtpValid) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN)
                    .body(Map.of("error", "Invalid or expired OTP"));
            }
            
            UserDto authenticatedUser = userService.signInUserViaOtp(userDto, response);
            
            log.info("User signed in via OTP successfully with ID: {} and isPrimary: {}", 
                authenticatedUser.getId(), authenticatedUser.getIsPrimary());
            
            return ResponseEntity.ok(authenticatedUser);
            
        } catch (RuntimeException e) {
            log.error("OTP sign-in verification failed - Error: {}", e.getMessage());
            return handleException(e);
        }
    }

    // Google OAuth sign-in
    @Operation(summary = "Sign in user via Google OAuth")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User signed in successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid Google token"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/signin/google")
    public ResponseEntity<?> signInViaGoogle(
        @Parameter(description = "Google OAuth token for sign-in", required = true)
        @RequestBody Map<String, String> body) {
        try {
            log.info("Signin Via Google Controller called");
            String idToken = body.get("token");
            if (idToken == null || idToken.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Google token is required"));
            }
            
            log.info("Google OAuth sign-in initiated");
            
            String googleToken = idToken.replace("\"", "").trim();
            UserDto authenticatedUser = userService.signInGoogleUser(googleToken);
            
            log.info("User signed in via Google OAuth successfully with ID: {} and isPrimary: {}", 
                authenticatedUser.getId(), authenticatedUser.getIsPrimary());
            
            return ResponseEntity.ok(authenticatedUser);
            
        } catch (RuntimeException e) {
            log.error("Google OAuth sign-in failed - Error: {}", e.getMessage());
            return handleException(e);
        }
    }

    // Apple OAuth sign-in
    @Operation(summary = "Sign in user via Apple OAuth")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User signed in successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid Apple token"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @PostMapping("/signin/apple")
    public ResponseEntity<?> signInViaApple(
        @Parameter(description = "Apple OAuth token for sign-in", required = true)
        HttpServletRequest request) {
        log.info("Signin Via Apple Controller called");
        try {
            String authHeader = request.getHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Authorization header with Bearer token is required"));
            }
            
            log.info("Apple OAuth sign-in initiated");
            
            String appleToken = authHeader.replace("Bearer ", "").trim();
            UserDto authenticatedUser = userService.signInAppleUser(appleToken);
            
            log.info("User signed in via Apple OAuth successfully with ID: {} and isPrimary: {}", 
                authenticatedUser.getId(), authenticatedUser.getIsPrimary());
            
            return ResponseEntity.ok(authenticatedUser);
            
        } catch (RuntimeException e) {
            log.error("Apple OAuth sign-in failed - Error: {}", e.getMessage());
            return handleException(e);
        }
    }

    // ================== TOKEN MANAGEMENT ENDPOINTS ==================

    @Operation(summary = "Refresh access token using refresh token")
    @PostMapping("/refresh-token")
    public ResponseEntity<?> refreshToken(
        @Parameter(description = "Refresh token details", required = true)
        @RequestBody Map<String, String> requestBody) {
        try {
            String refreshToken = requestBody.get("token");
            
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "Refresh token is required"));
            }
            
            log.info("Token refresh initiated");
            
            if (!jwtService.isValidRefreshToken(refreshToken)) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "Invalid or expired refresh token"));
            }
            
            String username = jwtService.extractUserName(refreshToken);
            Integer userId = jwtService.extractUserId(refreshToken);
            
            // Get user permissions
            List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(userId);

            String newAccessToken = jwtService.generateToken(username, userId, permissions);
            
            log.info("Token refreshed successfully for user ID: {}", userId);
            
            return ResponseEntity.ok(Map.of(
                "token", newAccessToken,
                "userId", userId,
                "email", username
            ));
            
        } catch (Exception e) {
            log.error("Token refresh failed - Error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                .body(Map.of("error", "Token refresh failed"));
        }
    }

    @Operation(summary = "Logout user and clear tokens")
    @PostMapping("/logout-respo")
    public ResponseEntity<?> logout(HttpServletResponse response) {
        try {
            log.info("User logout initiated");
            
            // Clear the refresh token cookie if using cookies
            ResponseCookie cookie = ResponseCookie.from("refreshToken", "")
                .path("/")
                .httpOnly(true)
                .secure(false) // Set to true in production with HTTPS
                .maxAge(0)
                .sameSite("Strict")
                .build();
            
            response.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
            
            log.info("User logged out successfully");
            
            return ResponseEntity.ok(Map.of(
                "message", "Logged out successfully",
                "timestamp", Instant.now().getEpochSecond()
            ));
            
        } catch (Exception e) {
            log.error("Logout failed - Error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Logout failed"));
        }
    }

    // ================== PASSWORD MANAGEMENT ENDPOINTS ==================

    @Operation(summary = "Reset user password")
    @PutMapping("/reset-password")
    public ResponseEntity<?> resetPassword(
        @Parameter(description = "Password reset details", required = true)
        @Valid @RequestBody UserDto userDto) {
        try {
            log.info("Password reset initiated for user");
            
            userService.resetPassword(userDto);
            
            log.info("Password reset completed successfully");
            
            return ResponseEntity.ok(Map.of(
                "message", "Password reset successfully",
                "timestamp", Instant.now().getEpochSecond()
            ));
            
        } catch (RuntimeException e) {
            log.error("Password reset failed - Error: {}", e.getMessage());
            return handleException(e);
        }
    }

    // ================== UTILITY ENDPOINTS ==================

    @Operation(summary = "Generate JWT secret key (Admin only)")
    @PostMapping("/generate-secret-key")
    public ResponseEntity<?> generateSecretKey() {
        try {
            log.warn("JWT secret key generation requested");
            
            String secretKey = jwtService.generateSecretKey();
            
            return ResponseEntity.ok(Map.of(
                "secretKey", secretKey,
                "warning", "Store this key securely and never expose it"
            ));
            
        } catch (Exception e) {
            log.error("Secret key generation failed - Error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "Failed to generate secret key"));
        }
    }

    // ================== HELPER METHODS ==================

    private ResponseEntity<?> handleException(Exception e) {
        HttpStatus status = determineHttpStatus(e.getMessage());
        PennypalErrorResponse errorResponse = new PennypalErrorResponse(
            status.value(), e.getMessage(), Instant.now().toEpochMilli());
        
        return new ResponseEntity<>(errorResponse, status);
    }

    private HttpStatus determineHttpStatus(String errorMessage) {
        if (errorMessage == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        
        if (lowerMessage.contains("already exist") || lowerMessage.contains("already registered")) {
            return HttpStatus.CONFLICT;
        } else if (lowerMessage.contains("not found") || lowerMessage.contains("invalid") || 
                   lowerMessage.contains("expired")) {
            return HttpStatus.BAD_REQUEST;
        } else if (lowerMessage.contains("unauthorized") || lowerMessage.contains("authentication")) {
            return HttpStatus.UNAUTHORIZED;
        } else if (lowerMessage.contains("forbidden") || lowerMessage.contains("access denied")) {
            return HttpStatus.FORBIDDEN;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
      @PostMapping("/logout")
    public String logout(HttpServletRequest request) {
        HttpSession session = request.getSession(false); // Get session if exists
        if (session != null) {
            session.invalidate(); // Invalidate session
        }
        return "Logged out successfully.";
    }
}
