package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.SubCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.Optional;
import java.util.List;

@Repository
public interface SubCategoryRepository extends JpaRepository<SubCategory, Integer> {
    Optional<SubCategory> findBySubCategory(String subCategory);
    List<SubCategory> findByCategoryId(int categoryId);

      @Query("SELECT s FROM SubCategory s LEFT JOIN FETCH s.category WHERE s.iconBlob IS NOT NULL")
    List<SubCategory> findAllWithIcons();
    
    // Add this for debugging
    @Query("SELECT COUNT(s) FROM SubCategory s WHERE s.iconBlob IS NOT NULL")
    long countWithIcons();

    @Query(value="""
            SELECT sc.id, sc.sub_category, sc.category_id, c.category
            FROM sub_category sc
            JOIN category c
            ON sc.category_id = c.id
            WHERE sc.id IN (SELECT DISTINCT b.sub_category_id FROM budget b
            WHERE b.user_id = :userId)
            """, nativeQuery = true)
    List<Object[]> findDistinctSubCategoriesByUserId(@Param ("userId") Integer userId);

    @Query(value="""
            SELECT sc.id AS id, sc.sub_category AS name, sc.category_id AS cid, c.category AS cname, 'standard' AS type
            FROM sub_category sc
            JOIN category c ON sc.category_id = c.id

            UNION ALL

            SELECT csc.custom_sub_category_id AS id, csc.custom_sub_category_name AS name, csc.category_id AS cid, c.category AS cname, 'custom' AS type
            FROM custom_sub_category csc
            JOIN category c ON csc.category_id = c.id
            WHERE csc.user_id = :userId
            """, nativeQuery = true)
    List<Object[]> findDistinctSubCategories(@Param ("userId") Integer userId);

    @Query(value = """
        SELECT finicity_category, sub_category FROM sub_category
        WHERE finicity_category IN :finicityCategories
        """, nativeQuery = true)
    List<Object[]> fetchSubCategoryMappings(@Param("finicityCategories") List<String> finicityCategories);

    @Query(value = """
        SELECT mx_category, sub_category FROM sub_category
        WHERE mx_category IN :MxCategories
        """, nativeQuery = true)
    List<Object[]> fetchMxSubCategoryMappings(@Param("MxCategories") List<String> MxCategories);
}
