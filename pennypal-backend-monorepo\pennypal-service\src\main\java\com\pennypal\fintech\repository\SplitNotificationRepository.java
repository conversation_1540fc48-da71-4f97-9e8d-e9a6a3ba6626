package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.SplitNotification;

import java.util.Optional;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SplitNotificationRepository extends JpaRepository<SplitNotification, Integer> {
    List<SplitNotification> findBySplitTransactionId(Integer splitTransactionId);
    List<SplitNotification> findByStatus(String status);
    // You can add custom query methods here if needed
}
