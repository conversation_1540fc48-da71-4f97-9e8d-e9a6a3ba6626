package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.StripeProducts;

import java.util.List;

@Repository
public interface StripeProductRepository extends JpaRepository<StripeProducts, Integer> {
    List<StripeProducts> findAll();
    StripeProducts findByPriceId(String priceId);
}