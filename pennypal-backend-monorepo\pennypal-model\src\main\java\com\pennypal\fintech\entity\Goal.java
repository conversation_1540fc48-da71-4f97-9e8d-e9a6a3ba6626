package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "goals")
@Getter
@Setter
@NoArgsConstructor
public class Goal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(nullable = false)
    private String goalName;

    @Column(nullable = false)
    private Double goalAmount;

    @Column(nullable = false)
    private Double currentAmount;

    @Column(nullable = false)
    private LocalDate startDate;

    @Column(nullable = false)
    private LocalDate targetDate;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private GoalType goalType;

    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private GoalStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private Users user;

    @CreationTimestamp
    private LocalDateTime createdAt;

    @UpdateTimestamp
    private LocalDateTime updatedAt;

    // Updated GoalType enum with INVESTMENT and PERSONAL added
    public enum GoalType {
        HOUSING,
        EDUCATION,
        EMERGENCY_FUND,
        RETIREMENT,
        VEHICLE,
        VACATION,
        INVESTMENT, 
        PERSONAL,   
        OTHER
    }

    public enum GoalStatus {
        IN_PROGRESS,
        COMPLETED,
        FAILED
    }

    // Method to update goal status based on current amount and target date
    public void updateStatus() {
        if (currentAmount >= goalAmount) {
            this.status = GoalStatus.COMPLETED;
        } else if (LocalDate.now().isAfter(targetDate)) {
            this.status = GoalStatus.FAILED;
        } else {
            this.status = GoalStatus.IN_PROGRESS;
        }
    }
}