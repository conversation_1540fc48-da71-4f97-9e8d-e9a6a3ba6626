package com.pennypal.fintech.dto;

import com.pennypal.fintech.entity.BudgetRules.ConditionType;
import com.pennypal.fintech.entity.BudgetRules.ExecutionFrequency;
import lombok.Data;
import java.math.BigDecimal;

@Data
public class BudgetRuleDto {
    private Integer id;
    private Integer userId;
    private String ruleType;
    private Integer fromCategoryId;
    private Integer fromSubCategoryId;
    private Integer toCategoryId;
    private Integer toSubCategoryId;
    private ConditionType conditionType;
    private BigDecimal thresholdAmount;
    private BigDecimal thresholdPercentage;
    private Integer transferDay;
    private String merchantNamePattern;
    private Boolean isActive;
    private BigDecimal maxTransferAmount;
    private BigDecimal minTransferAmount;
    private BigDecimal maxTransferPercent;
    private BigDecimal minTransferPercent;
    private ExecutionFrequency executionFrequency;
    private String renamedMerchant;
    private Boolean cascadeFlag;
    private String fromSubCategoryName;
    private String toSubCategoryName;
    private Boolean merchantMatchRegex;
    private String amountType;
    private String amountMatch;
    private Integer accountId;
    private Boolean hideTransactionsFlag;
    private String tags;
    private String goal;
    private String fromCatName;
    private String toCatName;
    private String accountName;
    private Integer toCustomSubCategoryId;
}