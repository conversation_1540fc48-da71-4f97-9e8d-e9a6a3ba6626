package com.pennypal.fintech.service;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;

import jakarta.annotation.PostConstruct;

import lombok.extern.slf4j.Slf4j;

import com.twilio.Twilio;
import com.twilio.rest.api.v2010.account.Message;
import com.twilio.type.PhoneNumber;

@Service
@Slf4j
public class TwilioService {

    // @Value("${twilio.account.sid}")
    // private String accountSid;

    // @Value("${twilio.auth.token}")
    // private String authToken;

    // @Value("${twilio.phone.number}")
    // private String fromNumber;

    // @Value("${twilio.whatsapp.number}")
    // private String fromWhatsAppNumber;

    private final AppConfigService appConfigService;

    public TwilioService(AppConfigService appConfigService) {
        this.appConfigService = appConfigService;
    }

    private String accountSid;
    private String authToken;
    private String fromNumber;
    private String fromWhatsAppNumber;

    @EventListener(ContextRefreshedEvent.class)
    public void loadTwilioConfigs() {
        accountSid = appConfigService.getConfig("twilio_account_sid");
        authToken = appConfigService.getConfig("twilio_auth_token");
        fromNumber = appConfigService.getConfig("twilio_phone_number");
        fromWhatsAppNumber = appConfigService.getConfig("twilio_whatsapp_number");
        log.info("Twilio configs loaded");
        
        init();
    }

    // @PostConstruct
    public void init() {
        Twilio.init(accountSid, authToken);
    }

    @EventListener(AppConfigService.ConfigurationRefreshedEvent.class)
    public void reloadTwilioConfigs(AppConfigService.ConfigurationRefreshedEvent event) {
        loadTwilioConfigs();
    }

    public void sendSms(String to, String message) {
        Message.creator(
                new PhoneNumber(to),
                new PhoneNumber(fromNumber),
                message
        ).create();
    }

    public void sendWhatsApp(String to, String message) {
        Message.creator(
                new PhoneNumber("whatsapp:" + to),
                new PhoneNumber("whatsapp:" + fromWhatsAppNumber),
                message
        ).create();
    }
}