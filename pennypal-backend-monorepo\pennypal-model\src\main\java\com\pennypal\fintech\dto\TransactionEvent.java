package com.pennypal.fintech.dto;


import org.springframework.context.ApplicationEvent;
import com.pennypal.fintech.dto.TransactionDto;
import java.util.List;

// Event Publisher
public class TransactionEvent extends ApplicationEvent {
    private final List<TransactionDto> transactions;

    public TransactionEvent(Object source, List<TransactionDto> transactions) {
        super(source);
        this.transactions = transactions;
    }

    public List<TransactionDto> getTransactions() {
        return transactions;
    }
}