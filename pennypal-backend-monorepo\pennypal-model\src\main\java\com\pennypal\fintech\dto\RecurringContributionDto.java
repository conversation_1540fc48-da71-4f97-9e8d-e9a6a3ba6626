package com.pennypal.fintech.dto;

import com.pennypal.fintech.entity.RecurringContribution;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

public class RecurringContributionDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateRecurringContributionRequest {
        private Integer goalId;
        private Integer accountId;
        private Double amount;
        private RecurringContribution.ContributionFrequency frequency;
        private LocalDate startDate;
        private LocalDate endDate; // Optional
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateRecurringContributionRequest {
        private Double amount;
        private RecurringContribution.ContributionFrequency frequency;
        private LocalDate endDate;
        private RecurringContribution.ContributionStatus status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RecurringContributionResponse {
        private Integer id;
        private Integer goalId;
        private String goalName;
        private Integer accountId;
        private String accountName;
        private Double amount;
        private RecurringContribution.ContributionFrequency frequency;
        private LocalDate startDate;
        private LocalDate endDate;
        private RecurringContribution.ContributionStatus status;
        private LocalDate lastContributionDate;
        private LocalDate nextContributionDate;
        private LocalDate lastAttemptDate;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContributeToGoalRequest {
        private Integer goalId;
        private Integer accountId;
        private Double amount;
    }
}