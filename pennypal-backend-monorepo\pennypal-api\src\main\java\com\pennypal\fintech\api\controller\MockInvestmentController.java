package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.DailyInvestmentStockDto;
import com.pennypal.fintech.dto.InvestmentDto;
import com.pennypal.fintech.service.MockInvestmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/mock/investments")
public class MockInvestmentController {

    private final MockInvestmentService mockInvestmentService;

    @Autowired
    public MockInvestmentController(MockInvestmentService mockInvestmentService) {
        this.mockInvestmentService = mockInvestmentService;
    }

    /**
     * Get all investments for a user
     * @param userId User ID
     * @return List of investments
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<InvestmentDto>> getAllInvestmentsForUser(@PathVariable int userId) {
        List<InvestmentDto> investments = mockInvestmentService.getAllInvestmentsForUser(userId);
        return ResponseEntity.ok(investments);
    }

    /**
     * Get investment performance metrics for a user
     * @param userId User ID
     * @return Performance metrics
     */
    @GetMapping("/performance/{userId}")
    public ResponseEntity<Map<String, Object>> getPerformanceMetrics(@PathVariable int userId) {
        Map<String, Object> metrics = mockInvestmentService.getPerformanceMetrics(userId);
        return ResponseEntity.ok(metrics);
    }

    /**
     * Fetch investment holdings from Plaid for a user
     * @param userId User ID
     * @return Fetched investments
     */
    @PostMapping("/fetch/{userId}")
    public ResponseEntity<Map<String, Object>> fetchInvestmentsFromPlaid(@PathVariable int userId) {
        Map<String, Object> result = mockInvestmentService.fetchInvestmentsFromPlaid(userId);
        return ResponseEntity.ok(result);
    }

  
    /**
     * Get investment accounts for a user with Plaid connection status
     * @param userId User ID
     * @return Investment accounts with status
     */
    @GetMapping("/accounts/{userId}")
    public ResponseEntity<List<Map<String, Object>>> getInvestmentAccounts(@PathVariable int userId) {
        List<Map<String, Object>> accounts = mockInvestmentService.getInvestmentAccounts(userId);
        return ResponseEntity.ok(accounts);
    }

    /**
     * Get portfolio diversity metrics
     * @param userId User ID
     * @return Diversity metrics
     */
    @GetMapping("/diversity/{userId}")
    public ResponseEntity<Map<String, Object>> getPortfolioDiversity(@PathVariable int userId) {
        Map<String, Object> diversity = mockInvestmentService.getPortfolioDiversity(userId);
        return ResponseEntity.ok(diversity);
    }

    /**
     * Get portfolio optimization recommendations
     * @param userId User ID
     * @return Recommendations
     */
    @GetMapping("/optimization/{userId}")
    public ResponseEntity<Map<String, Object>> getPortfolioOptimization(@PathVariable int userId) {
        Map<String, Object> optimization = mockInvestmentService.getPortfolioOptimization(userId);
        return ResponseEntity.ok(optimization);
    }
   
}