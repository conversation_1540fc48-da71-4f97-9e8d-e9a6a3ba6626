package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.time.LocalDateTime;

@Entity
@Table(name = "reconcile")
@Data

public class Reconcile {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;
    
    @Column(name = "description")
    private String description;

    @Column(name = "category")
    private String category;
    
    @Column(name = "amount")
    private Double amount;
    
    @Column(name = "reconcile_id")
    private String reconcileId;
    
    
    @Column(name = "reconcile_flag")
    private String reconcileFlag; 

    @Column(name = "removed")
    private Boolean removed = false;

    @Column(name = "accountName")
    private String accountName;

     @Column(name = "exclude_from_budget")
    private Boolean excludeFromBudget = false; 
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }
    
    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public Double getAmount() {
        return amount;
    }
    
    public void setAmount(Double amount) {
        this.amount = amount;
    }
    
    public String getReconcileId() {
        return reconcileId;
    }
    
    public void setReconcileId(String reconcileId) {
        this.reconcileId = reconcileId;
    }
    
    public String getReconcileFlag() {
        return reconcileFlag;
    }
    
    public void setReconcileFlag(String reconcileFlag) {
        this.reconcileFlag = reconcileFlag;
    }

    public Boolean getExcludeFromBudget() {
    return excludeFromBudget;
}

public void setExcludeFromBudget(Boolean excludeFromBudget) {
    this.excludeFromBudget = excludeFromBudget;
}

    
public Boolean getRemoved() {
    return removed;
}

public void setRemoved(Boolean removed) {
    this.removed = removed;
}

 public String getAccountName() {
        return accountName;
    }
    
    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }
    
}


