package com.pennypal.fintech.dto;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class InvestmentAggregationResponseDto {
    private Integer lookbackMonths;
    private Integer intervalDays;
    private LocalDate startDate;
    private LocalDate endDate;
    private Map<String, List<StockDataPointDto>> stockData;
    private Map<String, TickerSummaryDto> tickerSummaries;
    private OverallSummaryDto overallSummary;

    public InvestmentAggregationResponseDto() {}

    public InvestmentAggregationResponseDto(Integer lookbackMonths, Integer intervalDays, 
                                            LocalDate startDate, LocalDate endDate) {
        this.lookbackMonths = lookbackMonths;
        this.intervalDays = intervalDays;
        this.startDate = startDate;
        this.endDate = endDate;
        this.stockData = new HashMap<>();
        this.tickerSummaries = new HashMap<>();
    }

    public Integer getLookbackMonths() { return lookbackMonths; }
    public void setLookbackMonths(Integer lookbackMonths) { this.lookbackMonths = lookbackMonths; }

    public Integer getIntervalDays() { return intervalDays; }
    public void setIntervalDays(Integer intervalDays) { this.intervalDays = intervalDays; }

    public LocalDate getStartDate() { return startDate; }
    public void setStartDate(LocalDate startDate) { this.startDate = startDate; }

    public LocalDate getEndDate() { return endDate; }
    public void setEndDate(LocalDate endDate) { this.endDate = endDate; }

    public Map<String, List<StockDataPointDto>> getStockData() { return stockData; }
    public void setStockData(Map<String, List<StockDataPointDto>> stockData) { this.stockData = stockData; }

    public Map<String, TickerSummaryDto> getTickerSummaries() { return tickerSummaries; }
    public void setTickerSummaries(Map<String, TickerSummaryDto> tickerSummaries) { this.tickerSummaries = tickerSummaries; }

    public OverallSummaryDto getOverallSummary() { return overallSummary; }
    public void setOverallSummary(OverallSummaryDto overallSummary) { this.overallSummary = overallSummary; }
}
