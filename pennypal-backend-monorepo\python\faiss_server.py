import faiss
import numpy as np
import os
import pickle
from sentence_transformers import SentenceTransformer

# Use a robust model
model = SentenceTransformer("all-MiniLM-L6-v2")

# File paths
INDEX_FILE = "faiss_index.bin"
DATA_FILE = "faiss_data.pkl"

# Initialize index and data store
embedding_size = 384
index = faiss.IndexFlatIP(embedding_size)
data_store = []

# Load if existing
if os.path.exists(INDEX_FILE) and os.path.exists(DATA_FILE):
    index = faiss.read_index(INDEX_FILE)
    with open(DATA_FILE, "rb") as f:
        data_store = pickle.load(f)

def save_faiss():
    faiss.write_index(index, INDEX_FILE)
    with open(DATA_FILE, "wb") as f:
        pickle.dump(data_store, f)

def embed_text(text):
    return model.encode([text])[0] / np.linalg.norm(model.encode([text])[0])  # normalized

def add_entry(user_query, sql_query):
    user_query = user_query.strip()
    sql_query = sql_query.strip()
    
    # Check for duplicate user_query
    for entry in data_store:
        if entry["user_query"].lower() == user_query.lower():
            print("Duplicate entry detected. Skipping...")
            return False
    print("No duplicate entry detected. Adding...")

    vector = embed_text(user_query).astype("float32")
    index.add(np.array([vector]))
    data_store.append({"user_query": user_query, "sql_query": sql_query})
    save_faiss()
    print("Entry added successfully.")
    return True

def search(query_text, top_k=5):
    query_vec = embed_text(query_text).astype("float32")
    D, I = index.search(np.array([query_vec]), top_k)
    # return top matched SQL query
    if len(I[0]) == 0:
        return []
    return data_store[I[0][0]]["sql_query"]