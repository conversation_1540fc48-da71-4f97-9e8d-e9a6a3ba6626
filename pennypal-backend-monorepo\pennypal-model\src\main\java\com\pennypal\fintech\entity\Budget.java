package com.pennypal.fintech.entity;


import jakarta.persistence.*;
import lombok.Data;
//import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDate;


@Data
@Entity
@Table(name = "budget")
public class Budget {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    
    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
    private Category category;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "sub_category_id")
    private SubCategory subCategory;  // Nullable SubCategory
    
    // Store only the ID reference to custom subcategory (NO FOREIGN KEY)
    @Column(name = "custom_sub_category_id")
    private Integer customSubCategoryId;  

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private Users user;
    
    //@Column(nullable = false)
    @Column(name = "allocated")
    private BigDecimal allocated;
    
    //@Column(nullable = false)
    @Column(name = "actual")
    private BigDecimal actual;
    
    //@Column(nullable = false)
    @Column(name = "remaining")
    private BigDecimal remaining;
    
    @Column(name = "is_dynamic")
    private Boolean isDynamic = false;
    
    @Column(name = "dynamic_allocated", nullable = true)
    private BigDecimal dynamicAllocated;

    // Date field with formatting
    @Column(name = "date") // Allow NULL values
   // @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy/MM/dd") // Format YYYY/MM/DD
    private LocalDate date;

    // New fields from the database table
    @Column(name = "rollover", nullable = true) // Nullable Boolean
    private Boolean rollover = false;

    @Column(name = "exclude", nullable = true) // Nullable Boolean
    private Boolean exclude = false;
    // toString method for debugging and logging

    // New field for icon
    @Column(name = "icon")
    private String icon;
    
    @Override
    public String toString() {
        return "Budget{" +
                "id=" + id +
                ", category=" + (category != null ? category.getId() : null) +
                ", subCategory=" + (subCategory != null ? subCategory.getId() : null) +
                ", customSubCategoryId=" + customSubCategoryId +
                ", user=" + (user != null ? user.getId() : null) +
                ", allocated=" + allocated +
                ", actual=" + actual +
                ", remaining=" + remaining +
                ", isDynamic=" + isDynamic +
                ", dynamicAllocated=" + dynamicAllocated +
                ", date=" + date +
                ", rollover=" + rollover +
                ", exclude=" + exclude +
                ", icon='" + icon + '\'' +
                '}';
    }
}