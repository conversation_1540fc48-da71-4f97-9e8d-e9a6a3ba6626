package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.ProfilePictureUploadDto;
import com.pennypal.fintech.dto.ProfilePictureResponseDto;
import com.pennypal.fintech.dto.ProfilePictureDeleteDto;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.UserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class ProfilePictureService {
    
    @Autowired
    private UserRepository userRepository;
    
    // Allowed image types
    private static final List<String> ALLOWED_CONTENT_TYPES = Arrays.asList(
        "image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"
    );
    
    // Maximum file size (5MB)
    private static final long MAX_FILE_SIZE = 5 * 1024 * 1024;
    
    @Transactional
    public ProfilePictureResponseDto uploadProfilePicture(Integer userId, MultipartFile file) throws IOException {
        log.info("Uploading profile picture for user: {}", userId);
        
        // Validate user exists
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Validate file
        validateFile(file);
        
        // Process and save profile picture
        user.setProfilePictureData(file.getBytes());
        user.setProfilePictureContentType(file.getContentType());
        user.setProfilePictureFilename(file.getOriginalFilename());
        user.setProfilePictureSize(file.getSize());
        user.setProfilePictureUrl("/api/users/" + userId + "/profile-picture");
        user.setUpdateDateTime(LocalDateTime.now());
        
        userRepository.save(user);
        
        log.info("Profile picture uploaded successfully for user: {}", userId);
        
        return new ProfilePictureResponseDto(
            userId,
            user.getProfilePictureUrl(),
            user.getProfilePictureFilename(),
            user.getProfilePictureContentType(),
            user.getProfilePictureSize(),
            true
        );
    }
    
    @Transactional
    public ProfilePictureResponseDto uploadProfilePictureFromDto(ProfilePictureUploadDto dto) {
        log.info("Uploading profile picture from DTO for user: {}", dto.getUserId());
        
        // Validate user exists
        Users user = userRepository.findById(dto.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Validate data
        validateFileData(dto);
        
        // Process and save profile picture
        user.setProfilePictureData(dto.getData());
        user.setProfilePictureContentType(dto.getContentType());
        user.setProfilePictureFilename(dto.getFilename());
        user.setProfilePictureSize(dto.getSize());
        user.setProfilePictureUrl("/api/users/" + dto.getUserId() + "/profile-picture");
        user.setUpdateDateTime(LocalDateTime.now());
        
        userRepository.save(user);
        
        log.info("Profile picture uploaded successfully for user: {}", dto.getUserId());
        
        return new ProfilePictureResponseDto(
            dto.getUserId(),
            user.getProfilePictureUrl(),
            user.getProfilePictureFilename(),
            user.getProfilePictureContentType(),
            user.getProfilePictureSize(),
            true
        );
    }
    
    public byte[] getProfilePictureData(Integer userId) {
        log.info("Retrieving profile picture data for user: {}", userId);
        
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        if (user.getProfilePictureData() == null) {
            throw new RuntimeException("Profile picture not found for user");
        }
        
        return user.getProfilePictureData();
    }
    
    public ProfilePictureResponseDto getProfilePictureInfo(Integer userId) {
        log.info("Retrieving profile picture info for user: {}", userId);
        
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        boolean hasProfilePicture = user.getProfilePictureData() != null;
        
        return new ProfilePictureResponseDto(
            userId,
            hasProfilePicture ? user.getProfilePictureUrl() : null,
            user.getProfilePictureFilename(),
            user.getProfilePictureContentType(),
            user.getProfilePictureSize(),
            hasProfilePicture
        );
    }
    
    @Transactional
    public boolean deleteProfilePicture(Integer userId) {
        log.info("Deleting profile picture for user: {}", userId);
        
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        if (user.getProfilePictureData() == null) {
            throw new RuntimeException("No profile picture found to delete");
        }
        
        // Clear profile picture data
        user.setProfilePictureData(null);
        user.setProfilePictureContentType(null);
        user.setProfilePictureFilename(null);
        user.setProfilePictureSize(null);
        user.setProfilePictureUrl(null);
        user.setUpdateDateTime(LocalDateTime.now());
        
        userRepository.save(user);
        
        log.info("Profile picture deleted successfully for user: {}", userId);
        return true;
    }
    
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("File is required");
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("File size exceeds maximum limit of 5MB");
        }
        
        String contentType = file.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
            throw new RuntimeException("Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            throw new RuntimeException("Invalid filename");
        }
    }
    
    private void validateFileData(ProfilePictureUploadDto dto) {
        if (dto.getData() == null || dto.getData().length == 0) {
            throw new RuntimeException("File data is required");
        }
        
        if (dto.getSize() == null || dto.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("File size exceeds maximum limit of 5MB");
        }
        
        String contentType = dto.getContentType();
        if (contentType == null || !ALLOWED_CONTENT_TYPES.contains(contentType.toLowerCase())) {
            throw new RuntimeException("Invalid file type. Only JPEG, PNG, GIF, and WebP files are allowed");
        }
        
        if (dto.getFilename() == null || dto.getFilename().trim().isEmpty()) {
            throw new RuntimeException("Invalid filename");
        }
    }
    
    public String getContentType(Integer userId) {
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        return user.getProfilePictureContentType();
    }
}