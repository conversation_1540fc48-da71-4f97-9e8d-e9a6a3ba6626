package com.pennypal.fintech.util;

/* 
import java.util.HashMap;

import org.springframework.stereotype.Component;

import com.plaid.client.ApiClient;
import com.plaid.client.request.PlaidApi;

@Component
public class PennyPalPlaidApi {

    private PlaidApi plaidClient;

    private ApiClient apiClient;

    public PennyPalPlaidApi() throws Exception {
        setUpClient();
    }
    
    public void setUpClient() throws Exception {
       // String plaidClientId = System.getenv("PLAID_CLIENT_ID");
       // String plaidSecret = System.getenv("PLAID_SECRET");

        String plaidClientId = "67655adcb9d9be001afdfdbb";
        String plaidSecret = "8d9e072acc1e07480668af914ac95b";
      

        HashMap<String, String> apiKeys = new HashMap<String, String>();
        apiKeys.put("clientId", plaidClientId);
        apiKeys.put("secret", plaidSecret);
        //apiKeys.put("plaidVersion", "2020-09-14");
        apiClient = new ApiClient(apiKeys);
        apiClient.setPlaidAdapter(ApiClient.Sandbox);
        apiClient.setTimeout(61);

        plaidClient = apiClient.createService(PlaidApi.class);
    }

    public PlaidApi client() {
        return plaidClient;
    }

    public ApiClient apiClient() {
        return apiClient;
    }
}*/
import com.plaid.client.ApiClient;
import com.plaid.client.request.PlaidApi;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Component
public class PennyPalPlaidApi {

    private static final Logger logger = LoggerFactory.getLogger(PennyPalPlaidApi.class);

    private PlaidApi plaidClient;
    private ApiClient apiClient;

    public PennyPalPlaidApi() {
        setUpClient();
    }

    public void setUpClient() {
        String clientId = "67655adcb9d9be001afdfdbb";
        String secret = "8d9e072acc1e07480668af914ac95b";

        // Logging interceptor
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor(logger::info);
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);

        // Header interceptor for Plaid authentication and headers
        Interceptor authInterceptor = new Interceptor() {
            @Override
            public Response intercept(Chain chain) throws IOException {
                Request originalRequest = chain.request();
                Request requestWithHeaders = originalRequest.newBuilder()
                        .header("PLAID-CLIENT-ID", clientId)
                        .header("PLAID-SECRET", secret)
                        .header("Plaid-Version", "2020-09-14")
                        .header("User-Agent", "Plaid Java v29.0.0")
                        .build();
                return chain.proceed(requestWithHeaders);
            }
        };

        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .addInterceptor(authInterceptor)
                .addInterceptor(loggingInterceptor)
                .readTimeout(60, TimeUnit.SECONDS)
                .connectTimeout(60, TimeUnit.SECONDS)
                .build();

        // Construct ApiClient with the custom OkHttpClient
        apiClient = new ApiClient(okHttpClient);
        apiClient.setPlaidAdapter(ApiClient.Sandbox);  // or use ApiClient.Production for production

        plaidClient = apiClient.createService(PlaidApi.class);
    }

    public PlaidApi client() {
        return plaidClient;
    }

    public ApiClient apiClient() {
        return apiClient;
    }
}
