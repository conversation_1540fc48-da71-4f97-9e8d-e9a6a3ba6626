package com.pennypal.fintech.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PortfolioSummaryDto {
    private Double totalValue;
    private Double totalCostBasis;
    private Double totalGainLoss;
    private Double gainLossPercentage;
    private Integer numberOfHoldings;
    private Integer numberOfAccounts;
    private LocalDateTime lastUpdated;
    private List<InvestmentAccountSummaryDto> accounts;

    
}
