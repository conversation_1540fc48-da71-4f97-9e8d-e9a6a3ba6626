package com.pennypal.fintech.dto;

import lombok.Data;
import java.math.BigDecimal;

@Data
public class StripeProductsDto {
    private String productId;
    private String priceId;
    private String productName;
    private BigDecimal unitPrice;
    private String frequencyCharge;

    public StripeProductsDto(String productId, String priceId, String productName, BigDecimal unitPrice, String frequencyCharge) {
        this.productId = productId;
        this.priceId = priceId;
        this.productName = productName;
        this.unitPrice = unitPrice;
        this.frequencyCharge = frequencyCharge;
    }
}