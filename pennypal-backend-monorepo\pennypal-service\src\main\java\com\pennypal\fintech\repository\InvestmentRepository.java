package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.Investment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
@Repository
public interface InvestmentRepository extends JpaRepository<Investment, Integer> {
    List<Investment> findByUser_Id(int userId);
    List<Investment> findByAccount_Id(int accountId);
    Optional<Investment> findByInvestmentId(String investmentId);
    List<Investment> findBySecurityId(String securityId);
    List<Investment> findByTicker(String ticker);
    int countByAccount_Id(int accountId);
    @Query("SELECT SUM(i.value) FROM Investment i WHERE i.user.id = :userId")
Double getTotalValueByUserId(@Param("userId") Integer userId);
List<Investment> findByUser_IdAndSecurityType(int userId, String securityType);

@Query("SELECT SUM(i.value) FROM Investment i WHERE i.account.id = :accountId")
Double getTotalValueByAccountId(@Param("accountId") Integer accountId);
List<Investment> findByUserId(Integer userId);
    
    List<Investment> findByAccountId(Integer accountId);
    
    Optional<Investment> findByInvestmentIdAndAccountId(String investmentId, Integer accountId);
    
    @Query("SELECT i FROM Investment i WHERE i.user.id = :userId AND i.account.accountType = 'investment'")
    List<Investment> findByUserIdAndInvestmentAccount(@Param("userId") Integer userId);
    
    void deleteByInvestmentIdAndAccountId(String investmentId, Integer accountId);
// Add this to InvestmentRepository interface
@Query("SELECT DISTINCT i.user.id FROM Investment i")
List<Integer> findDistinctUserIds();
 // Method to find investments by user ID
    List<Investment> findByUser_Id(Integer userId);
      // Method to find investments by account ID
    List<Investment> findByAccount_Id(Integer accountId);
    // Method to find investments by user ID and security type
    List<Investment> findByUser_IdAndSecurityType(Integer userId, String securityType);
    // Add this method to your InvestmentRepository interface

// Add this method to your InvestmentRepository interface
@Query("SELECT i FROM Investment i WHERE i.ticker = :ticker AND i.user.id = :userId")
Optional<Investment> findByTickerAndUser_Id(@Param("ticker") String ticker, @Param("userId") Integer userId);
}