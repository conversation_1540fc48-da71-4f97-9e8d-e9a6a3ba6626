package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.ReceiptItems;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface ReceiptItemRepository extends JpaRepository<ReceiptItems, Integer> {
    List<ReceiptItems> findByReceiptId(long receiptId);

   @Query("SELECT r.item, SUM(r.quantity * r.price) AS totalSpent FROM ReceiptItems r GROUP BY r.item ORDER BY totalSpent DESC")
List<Object[]> findItemSummary();

    // Custom queries can be added here if necessary
}

