package com.pennypal.fintech.dto;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InvestmentHoldingsResponseDto {
    private String accountId;
    private String accountName;
    private List<HoldingDto> holdings; // Changed to generic HoldingDto
    private Integer totalHoldings;
    private Double totalValue;
    private LocalDateTime lastUpdated;
    private String provider; // Add this field
}