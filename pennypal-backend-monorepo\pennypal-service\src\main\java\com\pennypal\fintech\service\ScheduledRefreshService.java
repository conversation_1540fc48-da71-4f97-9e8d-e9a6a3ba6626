// package com.pennypal.fintech.service;

// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.scheduling.annotation.EnableScheduling;
// import org.springframework.scheduling.annotation.Scheduled;
// import org.springframework.stereotype.Service;

// import com.pennypal.fintech.repository.AccountRepository;
// import java.time.format.DateTimeFormatter;

// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;

// import java.time.LocalDateTime;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.concurrent.TimeUnit;

// @Service
// @EnableScheduling
// public class ScheduledRefreshService {

//     private static final Logger logger = LoggerFactory.getLogger(ScheduledRefreshService.class);

//     @Autowired
//     private AccountRepository accountRepository;

//     @Autowired
//     private PlaidService plaidService;
// @Autowired
// // private PlaidBudgetService plaidBudgetService;
//     /**
//      * Scheduled job that runs every 5 minutes to refresh all users' accounts and transactions
//      */
//     // @Scheduled(fixedRate = 300000) // 5 minutes in milliseconds
//     @Scheduled(cron = "0 0 0,12 * * ?")
//     public void scheduledAccountRefresh() {
//         logger.info("Starting scheduled account refresh job at {}", LocalDateTime.now());

//         Map<String, Object> jobSummary = new HashMap<>();
//         int totalUsers = 0;
//         int successfulUsers = 0;
//         int failedUsers = 0;
        
//         try {
//             // Get all users who have at least one account
//             List<Integer> usersWithAccounts = accountRepository.findDistinctUserIds();
//             totalUsers = usersWithAccounts.size();

//             logger.info("Found {} users with accounts to process", totalUsers);
            
//             // Process each user's accounts
//             for (Integer userId : usersWithAccounts) {
//                 try {
//                     logger.info("Processing accounts for user ID: {}", userId);
                    
//                     // Step 1: Sync transactions for all accounts of this user
//                     plaidService.asyncSyncAllAccountsForUser(userId).get(); // Wait for completion
                    
//                     // // Step 2: Process transactions for budget
//                     // String syncId = "scheduled-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
//                     // plaidBudgetService.processTransactionsForBudget(userId, syncId);
                    
//                     successfulUsers++;
                    
//                     // Small delay between users to avoid rate limiting
//                     TimeUnit.SECONDS.sleep(1);
                    
//                 } catch (Exception e) {
//                     failedUsers++;
//                     logger.error("Error processing user ID: {}", userId, e);
//                 }
//             }

//             // Complete job summary
//             jobSummary.put("totalUsers", totalUsers);
//             jobSummary.put("successfulUsers", successfulUsers);
//             jobSummary.put("failedUsers", failedUsers);
//             jobSummary.put("completionTime", LocalDateTime.now());

//             logger.info("Scheduled account refresh completed. Summary: {}", jobSummary);

//         } catch (Exception e) {
//             logger.error("Critical error in scheduled account refresh job", e);
//         }
//     }
// }
// // package com.pennypal.fintech.service;

// // import org.springframework.beans.factory.annotation.Autowired;
// // import org.springframework.scheduling.annotation.EnableScheduling;
// // import org.springframework.scheduling.annotation.Scheduled;
// // import org.springframework.stereotype.Service;

// // import com.pennypal.fintech.repository.AccountRepository;

// // import org.slf4j.Logger;
// // import org.slf4j.LoggerFactory;

// // import java.time.LocalDateTime;
// // import java.util.HashMap;
// // import java.util.List;
// // import java.util.Map;

// // @Service
// // @EnableScheduling
// // public class ScheduledRefreshService {

// //     private static final Logger logger = LoggerFactory.getLogger(ScheduledRefreshService.class);
    
// //     @Autowired
// //     private AccountRepository accountRepository;
    
// //     @Autowired
// //     private PlaidService plaidService;

// //     /**
// //      * Scheduled job that runs every 5 minutes to refresh all users' accounts and transactions
// //      */
// //     @Scheduled(fixedRate = 300000) // 5 minutes in milliseconds
// //     public void scheduledAccountRefresh() {
// //         logger.info("Starting scheduled account refresh job at {}", LocalDateTime.now());
        
// //         Map<String, Object> jobSummary = new HashMap<>();
// //         int totalUsers = 0;
// //         int successfulUsers = 0;
// //         int failedUsers = 0;
// //         int totalAccountsProcessed = 0;
// //         int totalTransactionsProcessed = 0;
// //         int totalBalancesUpdated = 0;
        
// //         try {
// //             // Get all users who have at least one account
// //             List<Integer> usersWithAccounts = accountRepository.findDistinctUserIds();
// //             totalUsers = usersWithAccounts.size();
            
// //             logger.info("Found {} users with accounts to process", totalUsers);
// //             jobSummary.put("totalUsers", totalUsers);
            
// //             // Process each user's accounts
// //             for (Integer userId : usersWithAccounts) {
// //                 try {
// //                     logger.info("Processing accounts for user ID: {}", userId);
                    
// //                     // Call the existing method to refresh all accounts for this user
// //                     Map<String, Object> userResult = plaidService.refreshAllUserAccounts(userId);
                    
// //                     // Update summary statistics
// //                     boolean userSuccess = (Boolean) userResult.getOrDefault("success", false);
// //                     if (userSuccess) {
// //                         successfulUsers++;
// //                     } else {
// //                         failedUsers++;
// //                         logger.warn("Failed to refresh accounts for user ID: {}", userId);
// //                     }
                    
// //                     // Add to totals
// //                     totalAccountsProcessed += (Integer) userResult.getOrDefault("totalAccounts", 0);
// //                     totalTransactionsProcessed += (Integer) userResult.getOrDefault("totalTransactionsProcessed", 0);
// //                     totalBalancesUpdated += (Integer) userResult.getOrDefault("totalBalancesUpdated", 0);
                    
// //                 } catch (Exception e) {
// //                     failedUsers++;
// //                     logger.error("Error processing user ID: {}", userId, e);
// //                 }
// //             }
            
// //             // Complete job summary
// //             jobSummary.put("successfulUsers", successfulUsers);
// //             jobSummary.put("failedUsers", failedUsers);
// //             jobSummary.put("totalAccountsProcessed", totalAccountsProcessed);
// //             jobSummary.put("totalTransactionsProcessed", totalTransactionsProcessed);
// //             jobSummary.put("totalBalancesUpdated", totalBalancesUpdated);
// //             jobSummary.put("completionTime", LocalDateTime.now());
            
// //             logger.info("Scheduled account refresh completed. Summary: {}", jobSummary);
            
// //         } catch (Exception e) {
// //             logger.error("Critical error in scheduled account refresh job", e);
// //         }
// //     }
// // }
