package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;


import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;

import jakarta.persistence.Table;
import lombok.Data;
import jakarta.persistence.Column;

import java.time.LocalDate;
import java.time.LocalDateTime;



@Entity
@Table(name = "accounts")
@Data
public class Accounts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
	
	@Column(name = "transaction_cursor")
	private String transactionCursor;

	@Column(name = "last_sync_time")
	private LocalDateTime lastSyncTime;

    @Column(name = "account_name")
    private String accountName;

    @Column(name = "account_type")
    private String accountType;

    @Column(name = "account_subtype")
    private String accountSubtype;

    @Column(name = "financial_inst_name")
    private String financialInstName;

    @Column(name = "balance")
    private Double balance;

    @Column(name = "currency_type")
    private String currencyType;

    @Column(name = "plaid_unique_no", unique = true, nullable = false)
    private String plaidUniqueNo;

    @Column(name = "account_mask")
    private String accountMask;
    
    @Column(name = "account_category")
    private String accountCategory;

    @Column(name = "institution_id")
    private String institutionId;

    @Column(name = "item_id")
    private String itemId;

    @Column(name = "sync_id")
    private String syncId;

    @Column(name = "access_token")
    private String accessToken;

    @Column(name = "insert_datetime")
    private LocalDateTime insertDatetime;

    @Column(name = "update_datetime")
    private LocalDateTime updateDatetime;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private Users user;  // Note: it's 'user', not 'users'

    @Column(name = "authentication_required")
    private Boolean authenticationRequired = true;

    @Column(name = "auth_partner")
    private String authPartner;

    @Column(name = "oldest_txn_date")
    private Long oldestTxnDate;

    @Column(name = "last_txn_date")
    private Long lastTxnDate;

    @Column(name = "mx_last_txn_date")
    private LocalDate mxLastTxnDate;
}
    
