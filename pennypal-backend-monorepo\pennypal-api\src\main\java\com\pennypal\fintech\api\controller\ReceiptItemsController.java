package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.pennypal.fintech.repository.ReceiptItemRepository;

import com.pennypal.fintech.entity.ReceiptItems;

@RestController
@RequestMapping("/api/v1/receipt-items")
public class ReceiptItemsController {
    
    @Autowired
    private ReceiptItemRepository receiptItemRepository;

    @GetMapping("/by-receipt/{receiptId}")
    public ResponseEntity<List<ReceiptItems>> getItemsByReceiptId(@PathVariable int receiptId) {
        List<ReceiptItems> items = receiptItemRepository.findByReceiptId(receiptId);
        return ResponseEntity.ok(items);
    }

    @GetMapping("/all")
public ResponseEntity<List<ReceiptItems>> getAllItems() {
    List<ReceiptItems> items = receiptItemRepository.findAll();
    return ResponseEntity.ok(items);
}

@GetMapping("/summary")
public ResponseEntity<List<Map<String, Object>>> getItemSummary() {
    List<Object[]> results = receiptItemRepository.findItemSummary();
    List<Map<String, Object>> summary = results.stream().map(row -> {
        Map<String, Object> map = new HashMap<>();
        map.put("item", row[0]);
        map.put("totalSpent", row[1]);  
        return map;
    }).collect(Collectors.toList());
    return ResponseEntity.ok(summary);
}


}
