    package com.pennypal.fintech.entity;

    import jakarta.persistence.Entity;
    import jakarta.persistence.GeneratedValue;
    import jakarta.persistence.GenerationType;
    import jakarta.persistence.Id;
    import jakarta.persistence.JoinColumn;
    import jakarta.persistence.ManyToOne;
    import jakarta.persistence.Table;

    import java.math.BigDecimal;
    import java.math.RoundingMode;
    import java.time.LocalDateTime;
    
    import com.fasterxml.jackson.annotation.JsonBackReference;

    @Entity
    @Table(name = "investments")
    public class Investment {
        
        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        private int id;
        
        @ManyToOne
        @JoinColumn(name = "user_id", nullable = false)
        @JsonBackReference
        private Users user;
        
        @ManyToOne
        @JoinColumn(name = "account_id", nullable = false)
         @JsonBackReference
        private Accounts account;
     

    // ✅ Add this if it's missing
    private String investmentGuid;
        public String getInvestmentGuid() {
        return investmentGuid;
    }

    public void setInvestmentGuid(String investmentGuid) {
        this.investmentGuid = investmentGuid;
    }

        private String investmentId;
        private String securityId;
        private String securityName;
        private String ticker;
        private String securityType;
        private Double quantity;
        private Double value;
        private Double costBasis;
        private Double averagePurchasePrice;
        private Double currentPrice;
        private String currencyCode;
        private LocalDateTime lastUpdated;
        private LocalDateTime insertDateTime;
        private String syncId;
        
        // New columns
        private Double marketValue;
        private Double gainLoss;
        private Double returnPercentage;
        private String cusip;
        
        // Getters and Setters
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public Users getUser() {
            return user;
        }

        public void setUser(Users user) {
            this.user = user;
        }

        public Accounts getAccount() {
            return account;
        }

        public void setAccount(Accounts account) {
            this.account = account;
        }

        public String getInvestmentId() {
            return investmentId;
        }

        public void setInvestmentId(String investmentId) {
            this.investmentId = investmentId;
        }

        public String getSecurityId() {
            return securityId;
        }

        public void setSecurityId(String securityId) {
            this.securityId = securityId;
        }

        public String getSecurityName() {
            return securityName;
        }

        public void setSecurityName(String securityName) {
            this.securityName = securityName;
        }

        public String getTicker() {
            return ticker;
        }

        public void setTicker(String ticker) {
            this.ticker = ticker;
        }

        public String getSecurityType() {
            return securityType;
        }

        public void setSecurityType(String securityType) {
            this.securityType = securityType;
        }

        public Double getQuantity() {
            return quantity;
        }

        public void setQuantity(Double quantity) {
            this.quantity = quantity;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }

        public Double getCostBasis() {
            return costBasis;
        }

        public void setCostBasis(Double costBasis) {
            this.costBasis = costBasis;
        }

        public Double getAveragePurchasePrice() {
            return averagePurchasePrice;
        }

        public void setAveragePurchasePrice(Double averagePurchasePrice) {
            this.averagePurchasePrice = averagePurchasePrice;
        }

        public Double getCurrentPrice() {
            return currentPrice;
        }

        public void setCurrentPrice(Double currentPrice) {
            this.currentPrice = currentPrice;
        }

        public String getCurrencyCode() {
            return currencyCode;
        }

        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }

        public LocalDateTime getLastUpdated() {
            return lastUpdated;
        }

        public void setLastUpdated(LocalDateTime lastUpdated) {
            this.lastUpdated = lastUpdated;
        }

        public LocalDateTime getInsertDateTime() {
            return insertDateTime;
        }

        public void setInsertDateTime(LocalDateTime insertDateTime) {
            this.insertDateTime = insertDateTime;
        }

        public String getSyncId() {
            return syncId;
        }

        public void setSyncId(String syncId) {
            this.syncId = syncId;
        }

        // New getters and setters
        public Double getMarketValue() {
            return marketValue;
        }

        public void setMarketValue(Double marketValue) {
            this.marketValue = marketValue;
        }

        public Double getGainLoss() {
            return gainLoss;
        }

        public void setGainLoss(Double gainLoss) {
            this.gainLoss = gainLoss;
        }

        public Double getReturnPercentage() {
            return returnPercentage;
        }

        public void setReturnPercentage(Double returnPercentage) {
            this.returnPercentage = returnPercentage;
        }

        public String getCusip() {
            return cusip;
        }

        public void setCusip(String cusip) {
            this.cusip = cusip;
        }
        
        // Helper method to calculate derived values
        public void calculateDerivedValues() {
            if (quantity != null && currentPrice != null) {
                this.marketValue = quantity * currentPrice;
            }
            
            if (marketValue != null && costBasis != null) {
                this.gainLoss = marketValue - costBasis;
                
                if (costBasis > 0) {
                    this.returnPercentage = (gainLoss / costBasis) * 100;
                    // Round to 2 decimal places
                    this.returnPercentage = BigDecimal.valueOf(this.returnPercentage)
                        .setScale(2, RoundingMode.HALF_UP).doubleValue();
                }
            }
        }
    }