package com.pennypal.fintech.repository;



import com.pennypal.fintech.entity.UserRelationship;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import com.pennypal.fintech.entity.UserRelationship;

import java.util.List;
import java.util.Optional;

@Repository
public interface MembershipRepository extends JpaRepository<UserRelationship, Integer> {
    List<UserRelationship> findByPrimaryUserId(int primaryUserId);
    Optional<UserRelationship> findBySecondaryUserEmail(String email);
      Optional<UserRelationship> findBySecondaryUserId(int secondaryUserId);
}