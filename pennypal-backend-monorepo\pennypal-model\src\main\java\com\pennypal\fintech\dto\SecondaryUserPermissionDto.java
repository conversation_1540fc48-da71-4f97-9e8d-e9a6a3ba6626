package com.pennypal.fintech.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SecondaryUserPermissionDto {
    
    private String pageName;
    private String actionName;
    private String permissionType;
    
    // Additional constructors for convenience
    public SecondaryUserPermissionDto(String pageName, String actionName) {
        this.pageName = pageName;
        this.actionName = actionName;
        this.permissionType = "READ_ONLY"; // default permission type
    }
    
    // Utility methods
    public boolean isReadOnly() {
        return "READ_ONLY".equalsIgnoreCase(this.permissionType);
    }
    
    public boolean isWriteAccess() {
        return "WRITE".equalsIgnoreCase(this.permissionType) || 
               "FULL_ACCESS".equalsIgnoreCase(this.permissionType);
    }
    
    public boolean canPerformAction(String action) {
        if (action == null || action.isEmpty()) {
            return false;
        }
        
        switch (this.permissionType.toUpperCase()) {
            case "READ_ONLY":
                return action.equalsIgnoreCase("VIEW") || 
                       action.equalsIgnoreCase("READ") || 
                       action.equalsIgnoreCase("DOWNLOAD");
            case "WRITE":
            case "FULL_ACCESS":
                return action.equalsIgnoreCase("VIEW") || 
                       action.equalsIgnoreCase("READ") || 
                       action.equalsIgnoreCase("CREATE") || 
                       action.equalsIgnoreCase("UPDATE") || 
                       action.equalsIgnoreCase("DELETE") || 
                       action.equalsIgnoreCase("DOWNLOAD");
            default:
                return action.equalsIgnoreCase("VIEW") || 
                       action.equalsIgnoreCase("READ");
        }
    }
    
    @Override
    public String toString() {
        return String.format("SecondaryUserPermissionDto{pageName='%s', actionName='%s', permissionType='%s'}", 
                           pageName, actionName, permissionType);
    }
}