package com.pennypal.fintech.entity;

import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.SqlResultSetMapping;

@SqlResultSetMapping(
    name = "CategoryMonthlySummaryMapping",
    classes = @ConstructorResult(
        targetClass = com.pennypal.fintech.dto.CategoryMonthlySummaryDto.class,
        columns = {
            @ColumnResult(name = "month", type = String.class),
            @ColumnResult(name = "total", type = Double.class)
        }
    )
)
@Entity
public class CategoryMonthlyMapping {
    @Id
    private Long dummyId;
}
