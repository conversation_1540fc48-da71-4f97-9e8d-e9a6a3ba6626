package com.pennypal.fintech.dto;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
public class BudgetDto {

    private int id;
    private Integer categoryId;
    private Integer subcategoryId;  // Nullable
    private Integer customSubCategoryId;
    private String customSubCategoryName;  // add this field

    private BigDecimal allocated;
    private BigDecimal actual;
    private BigDecimal remaining;
    private Boolean isDynamic;
    private BigDecimal dynamicAllocated;
    private CategoryDto category;
    private SubCategoryDto subcategory;
    private Boolean isRollover;
    private Boolean isExcluded;
    private Integer userId;

    /**
     * APT-126 fix for category/subcategory name not being displayed properly - to update redux state
     */
    private String categoryName;
    private String subcategoryName;
    
    // Map JSON property "icon" to this field.
    @JsonProperty("icon")
    private String iconKey;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    private String date;               // For date, represented as a String (or you could use LocalDate depending on your use case)

    public LocalDate getDateAsLocalDate() {
        return (date != null) ? LocalDate.parse(date, DATE_FORMATTER) : null;
    }
    public void setDateAsLocalDate(LocalDate localDate) {
        this.date = (localDate != null) ? localDate.format(DATE_FORMATTER) : null;
    }
    @Override
    public String toString() {
        return "BudgetDto{" +
                "id=" + id +
                ", categoryId=" + categoryId +
                ", subcategoryId=" + subcategoryId +
                ", customSubCategoryId='" + customSubCategoryId + '\'' +
                ", allocated=" + allocated +
                ", actual=" + actual +
                ", remaining=" + remaining +
                ", isDynamic=" + isDynamic +
                ", dynamicAllocated=" + dynamicAllocated +
                ", date='" + date + '\'' +
                ", isRollover=" + isRollover +
                ", isExcluded=" + isExcluded +
                ", userId=" + userId +
                ", iconKey='" + iconKey + '\'' +
                '}';
    }
}
