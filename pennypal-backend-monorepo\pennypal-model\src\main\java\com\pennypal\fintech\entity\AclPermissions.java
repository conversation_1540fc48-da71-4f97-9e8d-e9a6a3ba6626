package com.pennypal.fintech.entity;

import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import lombok.Data;

@Entity
@Table(name = "acl_permissions", uniqueConstraints = {
       @UniqueConstraint(columnNames = {"group_id", "page_id", "action_id"})
})
@Data
public class AclPermissions {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "group_id", foreignKey = @ForeignKey(name = "acl_permissions_ibfk_1"))
    private AclGroups group;

    @ManyToOne
    @JoinColumn(name = "page_id", foreignKey = @ForeignKey(name = "acl_permissions_ibfk_2"))
    private AclPages page;

    @ManyToOne
    @JoinColumn(name = "action_id", foreignKey = @ForeignKey(name = "acl_permissions_ibfk_3"))
    private AclActions action;
}