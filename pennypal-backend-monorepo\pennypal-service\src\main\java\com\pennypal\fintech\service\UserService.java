package com.pennypal.fintech.service;

import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import java.security.interfaces.ECPrivateKey;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import com.pennypal.fintech.dto.SecondaryUserPermissionDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.security.core.Authentication;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestTemplate;
import com.pennypal.fintech.repository.MembershipRepository;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;

import com.pennypal.fintech.dto.UserDto;
import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.entity.AclGroups;
import com.pennypal.fintech.entity.EnumAuthProvider;
import com.pennypal.fintech.entity.UserRelationship;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.entity.UsersGroup;
import com.pennypal.fintech.entity.VerificationToken;
import com.pennypal.fintech.repository.AclGroupsRepository;
import com.pennypal.fintech.repository.EnumAuthProviderRepository;
import com.pennypal.fintech.repository.MembershipRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.repository.UserGroupRepository;
import com.pennypal.fintech.repository.VerificationTokenRepository;

import com.google.api.client.googleapis.auth.oauth2.GoogleIdToken;
import com.google.api.client.googleapis.auth.oauth2.GoogleIdTokenVerifier;
import com.google.api.client.googleapis.auth.oauth2.GooglePublicKeysManager;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.gson.GsonFactory;
// import com.nimbusds.jwt.JWT;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;

@Slf4j
@Service
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private JWTService jwtService;
@Autowired
private MembershipRepository membershipRepository;
    @Autowired
    AuthenticationManager authManager;

    @Autowired
    EnumAuthProviderRepository enumAuthProviderRepository;

    @Autowired
    VerificationTokenRepository verificationTokenRepository;

    @Autowired
    private JavaMailSender emailSender;

    @Autowired
    private AppConfigService appConfigService;

    @Autowired
    private AclGroupsRepository aclGroupsRepository;

    @Autowired
    private AclService aclService;

    @Autowired
    private UserGroupRepository usersGroupRepository;

    // private static final String CLIENT_ID = "542094643512-4ii30dirf596vm7ncnbkvf6q21vvk6sb.apps.googleusercontent.com";


    private BCryptPasswordEncoder encoder = new BCryptPasswordEncoder(12);


    private boolean validateUser(UserDto userDto){

        if(userDto != null && userDto.getEmailId() != null){
            return true;
        }
        return true;
    }

    // Called from signInUser method
    public String verify(UserDto user, Integer userId, HttpServletResponse response) {
        String res = "fail";
        try {

            Authentication authentication = authManager.authenticate(new UsernamePasswordAuthenticationToken(user.getEmailId(), user.getPassword()));
            if (authentication.isAuthenticated()) {
                // If authenticated and user has selected 'Remember Me', generate refresh token
                System.out.println("Remember me?: " + user.getRememberMe());
                if (user.getRememberMe() != null && user.getRememberMe()) {
                    String refreshToken = jwtService.generateRefreshToken(user.getEmailId(), userId);
                    System.out.println("Refresh token: " + refreshToken);
                    // Store in HTTP Only Cookie
					/*
                    ResponseCookie cookie = ResponseCookie.from("refreshToken", refreshToken)
                        .httpOnly(true)
                        .secure(false) // Set to true in production
                        .path("/api/v1/auth/refresh-token")
                        .maxAge(Duration.ofDays(30))
                        .sameSite("Strict")
                        .build();
                    response.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());*/
					user.setRefreshToken(refreshToken);
                }
                // Get user permissions
                List<UserPermissionDto> permissions = aclService.
                    getPermissionsByUserId(userId);
                // Generate JWT access token
                return jwtService.generateToken(user.getEmailId(), userId, permissions);
            } else {
                return "fail";
            }
            
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
        }
        return res;
       
    }

    private boolean isUserAlreadyExist(UserDto userDto){
        if(userDto != null){
           List<Users> userList =  userRepository.findByEmailId(userDto.getEmailId());
           if(userList != null && !userList.isEmpty()){
              //User already exists in the database
              return true;
           }
        }

        return false;
    }
  // Called from /api/v1/auth/signin - normal login
    // Updated signInUser method in UserService.java
public UserDto signInUser(UserDto userDto, HttpServletResponse response) {
    System.out.println("Inside signInUser");
    System.out.println(userDto.toString());
    if(userDto != null && userDto.getEmailId() != null && userDto.getPassword() != null){
        List<Users> userList = userRepository.findByEmailId(userDto.getEmailId());
        if(userList != null && !userList.isEmpty()){
            Users usr = userList.get(0);
			userDto.setId(usr.getId());
            System.out.println(usr.toString());
            System.out.println(usr.getPassword());
            System.out.println(userDto.getPassword());
            System.out.println("About to verify the user");
            String token = verify(userDto, usr.getId(), response);
            System.out.println(token);
            
            // Create a new response DTO with all necessary fields
            UserDto responseDto = new UserDto();
            responseDto.setId(usr.getId()); // Set actual DB ID
            responseDto.setEmailId(usr.getEmailId());
            responseDto.setName(usr.getName());
            responseDto.setPhoneNumber(usr.getPhoneNumber());
            responseDto.setJwtToken(token);
             responseDto.setIsPrimary(usr.getIsPrimary()); 
            // Add debugging to verify ID is being set correctly
             log.info("User login successful. User ID in response: " + responseDto.getId());
            
            // Return the new DTO instead of modifying the input one
            return responseDto;
        }
    }
    throw new RuntimeException("Invalid login credentials");
}


// Called from /api/v1/auth/signin/otp/verify - OTP login
    public UserDto signInUserViaOtp(UserDto userDto, HttpServletResponse response) {
        System.out.println("Inside signInUserViaOtp");
        System.out.println(userDto.toString());

        // if(userDto != null && userDto.getEmailId() != null){
        if(userDto != null && (userDto.getEmailId() != null || userDto.getPhoneNumber() != null)){
            // List<Users> userList = userRepository.findByEmailId(userDto.getEmailId());
            List<Users> userList = null;
            if (userDto.getEmailId() != null) {
                userList = userRepository.findByEmailId(userDto.getEmailId());
            } else if (userDto.getPhoneNumber() != null) {
                userList = userRepository.findByPhoneNumber(userDto.getPhoneNumber());
            }
            if(userList != null && !userList.isEmpty()){
                Users usr = userList.get(0);
                
                userDto.setEmailId(usr.getEmailId());
                userDto.setId(usr.getId());
                userDto.setPhoneNumber(usr.getPhoneNumber());
                userDto.setName(usr.getName());
 userDto.setIsPrimary(usr.getIsPrimary());
                String token = verifyOtpUser(userDto, usr.getId(), response);
                if (token == null) {
                    throw new RuntimeException("Unable to Login User");
                }
                userDto.setJwtToken(token);
                
                // Nullify password and OTP
                userDto.setPassword(null);
                userDto.setOtp(null);
            }
        }
        return userDto;
    }

    // Called from signInUserViaOtp method
    public String verifyOtpUser(UserDto user, Integer userId, HttpServletResponse response) {
        try {
            // If authenticated and user has selected 'Remember Me', generate refresh token
            if (user.getRememberMe() != null && user.getRememberMe()) {
                String refreshToken = jwtService.generateRefreshToken(user.getEmailId(), userId);
                System.out.println("Refresh token: " + refreshToken);
                user.setRefreshToken(refreshToken);
            }
            // Get user permissions
            List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(userId);
            // Generate JWT access token
            return jwtService.generateToken(user.getEmailId(), userId, permissions);     
        } catch (Exception e) {
            return null;
        }
    }

    // Called from /api/v1/auth/register - to send verification e-mail
    public ResponseEntity<?> registerUser(UserDto userDto) {
        System.out.println("Inside registerUser in UserService");
        System.out.println(userDto.toString());
        System.out.println(userDto.getEmailId());

        if(userDto != null && userDto.getEmailId() != null){
            if ( validateUser(userDto) && isUserAlreadyExist(userDto)){
                throw new RuntimeException("This user already available.");
            }
        }

        if(verificationTokenRepository.existsByEmail(userDto.getEmailId())) {
            throw new RuntimeException("Verification email already sent. Please check your inbox.");
        }

        String phoneNumber = null;
        if (userDto.getPhoneNumber() != null) {
            phoneNumber = "+" + userDto.getPhoneNumber();
        }

        // Send verification e-mail to user:
        String token = UUID.randomUUID().toString();
        System.out.println("Token: " + token);
        String encodedEmail = Base64.getUrlEncoder().encodeToString(userDto.getEmailId().getBytes());
        System.out.println("Encoded email: " + encodedEmail);
        VerificationToken vt = new VerificationToken(userDto.getEmailId(), token, LocalDateTime.now().plusDays(2), phoneNumber);
        verificationTokenRepository.save(vt);
        String link = "http://localhost:5173/set-password?token=" + token +"&email=" + encodedEmail;
        System.out.println("Link: " + link);
        
        SimpleMailMessage message = new SimpleMailMessage();
        message.setFrom("<EMAIL>");
        message.setTo(userDto.getEmailId());
        message.setSubject("Verification email for PennyPal registration");
        message.setText(String.format("Greetings from PennyPal team! Please click on the following link to verify your email and set your password: %s", link));
        System.out.println("Message: " + message);
        emailSender.send(message);

        return ResponseEntity.ok("Verification email sent");

        // Users newUser = new Users();
        // newUser.setEmailId(userDto.getEmailId());
        // newUser.setPhoneNumber(userDto.getPhoneNumber());

        // newUser.setName(userDto.getName());
       
        // newUser.setPassword(encoder.encode(userDto.getPassword()));
        // newUser.setInsertDateTime(LocalDateTime.now());
        // System.out.println( newUser.toString());

        // Users savedUser = userRepository.save(newUser);
        // userDto.setPassword(null);//Nullify the password.
        
        // userDto.setId(savedUser.getId());

  
        // return userDto;
    }

    // Called from /api/v1/auth/verify - to set password and register user
    public UserDto verifyUser(UserDto userDto) {
        log.info("Inside verifyUser in UserService");
        String decodedEmail = new String(Base64.getUrlDecoder().decode(userDto.getEncodedEmail()));
        System.out.println("Decoded email: " + decodedEmail);
        String verificationToken = userDto.getVerificationToken();

        if(userDto != null && decodedEmail != null){
            if (userRepository.existsByEmailId(decodedEmail)) {
                throw new RuntimeException("User already registered");
            }
        }

        VerificationToken vt = verificationTokenRepository.findByEmail(decodedEmail);
        if (vt == null || !vt.getToken().equals(verificationToken) ||
            vt.getExpiryDate().isBefore(LocalDateTime.now())) {
            throw new RuntimeException("Invalid verification token. Please signup again");
        }
        String phoneNumber = null;
        if (vt.getPhoneNumber() != null) {
            phoneNumber = vt.getPhoneNumber();
        }

        Users newUser = new Users();
        newUser.setEmailId(decodedEmail);
        newUser.setInsertDateTime(LocalDateTime.now());
        newUser.setPassword(encoder.encode(userDto.getPassword()));
        newUser.setPhoneNumber(phoneNumber);
        newUser.setName(userDto.getName());
       newUser.setIsPrimary(true);
        Users savedUser = userRepository.save(newUser);

        userDto.setPassword(null);
        userDto.setId(savedUser.getId());

        // Remove registration token
        verificationTokenRepository.deleteByEmail(decodedEmail);
        
        // Add to users_group with 'TRIAL' tier
        UsersGroup ug = new UsersGroup();
        ug.setUserId(savedUser.getId());
        ug.setGroup(aclGroupsRepository.findByName(AclGroups.GroupName.TRIAL));
        ug.setStatus("ACTIVE");
        ug.setExpiresAt(LocalDateTime.now().plusDays(90));
        usersGroupRepository.save(ug);
        
        return userDto;
    }
    
    // Called from registerGoogleUser and registerAppleUser
    public UserDto verifyUserOAuth(UserDto userDto) {
        System.out.println("Inside verifyUserOAuth in UserService");
        System.out.println(userDto.toString());
        System.out.println(userDto.getEmailId());
        if(userDto != null && userDto.getEmailId() != null){
            if (validateUser(userDto) && isUserAlreadyExist(userDto)) {
                throw new RuntimeException("User already registered");
            }
        }

        Users newUser = new Users();
        newUser.setEmailId(userDto.getEmailId());
        newUser.setInsertDateTime(LocalDateTime.now());
        newUser.setPassword(encoder.encode(userDto.getPassword()));
        newUser.setPhoneNumber(userDto.getPhoneNumber());
        newUser.setName(userDto.getName());
 newUser.setIsPrimary(true); 
        // if (userDto.getSourceOfAuth() != null) {
        //     EnumAuthProvider provider = enumAuthProviderRepository.findByProviderName(userDto.getSourceOfAuth());
        //     newUser.setAuthProvider(provider);
        // }
       
        Users savedUser = userRepository.save(newUser);

        userDto.setPassword(null);
        userDto.setId(savedUser.getId());

        // Add to users_group with 'TRIAL' tier
        UsersGroup ug = new UsersGroup();
        ug.setUserId(savedUser.getId());
        ug.setGroup(aclGroupsRepository.findByName(AclGroups.GroupName.TRIAL));
        ug.setStatus("ACTIVE");
        ug.setExpiresAt(LocalDateTime.now().plusDays(90));
        usersGroupRepository.save(ug);
        
        return userDto;
    }

    public UserDto fetchUserByEmailId(String emailId){
        UserDto userDto = null;
        /* 
        List<UserAccountAccess> userAccountAccessList =  userRepository.getUserAccountAccess(emailId);
        if(userAccountAccessList != null && !userAccountAccessList.isEmpty()){
            UserAccountAccess userAccountAccess = userAccountAccessList.get(0);
            
            userDto = new UserDto();

            UserAccountAccessDto userAccountAccessDto = new UserAccountAccessDto();
            userAccountAccessDto.setAccessToken(userAccountAccess.getAccessToken());
            userAccountAccessDto.setId(userAccountAccess.getId());

            List<UserAccountAccessDto> userAccountAccessDtoList = new ArrayList<>();
            userAccountAccessDtoList.add(userAccountAccessDto);
           
            userDto.setUserAccountAccessList(userAccountAccessDtoList); 
        }*/
        return userDto;
    }

    public boolean updateAccessToken(String accessToken,String emailId){
        List<Users> userList = userRepository.findByEmailId(emailId);
       
        return true;
    }

    public String getAccessToken() throws Exception{
        
       // String publicToken = PennipalUtil.plaidCall("public_token");
       // String accessToken = PennipalUtil.plaidCall("access_token",publicToken);

        return "";
    }

    public Optional<Users> authenticate(String userName , String password){
        return userRepository.findById(1);

          //
    }

    public Optional<Users> findById(Integer id){
        return userRepository.findById(id);
    }

public UserDto addUser(UserDto userDto){
    Users user = new Users();
    user.setEmailId(userDto.getEmailId());
    user.setPhoneNumber(userDto.getPhoneNumber());
    user.setAuthProvider(null);
    user.setInsertDateTime(LocalDateTime.now());
    user.setIsPrimary(true); // Explicitly set as primary user

    Users savedUser = userRepository.save(user);
    userDto.setId(savedUser.getId());
    
    return userDto;
}
    // Sign In logic using only the email ID
        public boolean signIn(String emailId) {
            // Retrieve user by emailId
            List<Users> users = userRepository.findByEmailId(emailId);
    
            // Check if the user exists
            return users != null && !users.isEmpty();
        }

    // Register via Google OAuth
    public UserDto registerGoogleUser(String googleToken) {
        try {
            System.out.println("Inside registerGoogleUser in UserService");
            GoogleIdToken idToken = verifyGoogleToken(googleToken);
            System.out.println("Google token: " + googleToken);
            
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();
                System.out.println("Payload: " + payload);
                
                String email = payload.getEmail();
                if (userRepository.existsByEmailId(email)) {
                    throw new RuntimeException("e-mail already registered");
                }
                System.out.println("Email: " + email);
                String name = (String) payload.get("name");
                System.out.println("Name: " + name);
  // Check if this email has a pending invitation
            Optional<UserRelationship> pendingInvitation = membershipRepository.findAll().stream()
                    .filter(r -> r.getSecondaryUserEmail() != null && 
                          r.getSecondaryUserEmail().equalsIgnoreCase(email) &&
                          r.getStatus().equals("PENDING"))
                    .findFirst();

                UserDto userDto = new UserDto();
                userDto.setEmailId(email);
                String randomPassword = UUID.randomUUID().toString();
                userDto.setPassword(randomPassword);
                userDto.setName(name);
                userDto.setSourceOfAuth("Google OAuth");
 // If there's a pending invitation, create as secondary user
            if (pendingInvitation.isPresent()) {
                UserRelationship relationship = pendingInvitation.get();
                
                // Create secondary user (not primary)
                Users newUser = new Users();
                newUser.setEmailId(email);
                newUser.setInsertDateTime(LocalDateTime.now());
                newUser.setPassword(encoder.encode(randomPassword));
                newUser.setName(name);
                newUser.setIsPrimary(false); // Set as secondary user
                
                Users savedUser = userRepository.save(newUser);
                
                // Update the relationship
                relationship.setSecondaryUserId(savedUser.getId());
                relationship.setStatus("ACTIVE");
                relationship.setUpdatedAt(LocalDateTime.now());
                membershipRepository.save(relationship);
                
                // Return secondary user details
                userDto.setId(savedUser.getId());
                userDto.setPassword(null);
                userDto.setIsPrimary(false);
                
                log.info("Secondary user created via Google OAuth with ID: {} for primary user ID: {}", 
                        savedUser.getId(), relationship.getPrimaryUserId());
                
                return userDto;}
                else {
                // No pending invitation, create as primary user (existing logic)
                return verifyUserOAuth(userDto);
            }
            } else {
                throw new RuntimeException("Google token is null");
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to verify Google token: " + e.getMessage(), e);
        }
    }

    // Google OAuth based user registration
    private GoogleIdToken verifyGoogleToken(String token) throws Exception {
        String CLIENT_ID = appConfigService.getConfig("google_client_id");
        GoogleIdTokenVerifier verifier = new GoogleIdTokenVerifier.Builder(
                new GooglePublicKeysManager.Builder(
                        GoogleNetHttpTransport.newTrustedTransport(),
                        GsonFactory.getDefaultInstance()
                ).build()
        )
        .setAudience(Collections.singletonList(CLIENT_ID))
        .build();

        return verifier.verify(token);
    }

    // Apple OAuth based user registration
    public UserDto registerAppleUser(String code) {
        String clientId = "<your-client-id>";
        String teamId = "<your-team-id>";
        String keyId = "<your-key-id>";
        String privateKey = "<your-private-key>"; // from .p8 file
        String redirectUri = "http://localhost:8080/pennypal/api/v1/register/apple";

        String clientSecret = generateClientSecret(clientId, teamId, keyId, privateKey);

        // Exchange code for token
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("grant_type", "authorization_code");
        body.add("code", code);
        body.add("client_id", clientId);
        body.add("client_secret", clientSecret);
        body.add("redirect_uri", redirectUri);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(body, headers);

        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<Map> response = restTemplate.postForEntity(
            "https://appleid.apple.com/auth/token", request, Map.class);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Apple token exchange failed");
        }

        String idToken = (String) response.getBody().get("id_token");

        // Decode ID token (JWT)
        DecodedJWT jwt = JWT.decode(idToken);
        String email = jwt.getClaim("email").asString();
        boolean emailVerified = jwt.getClaim("email_verified").asBoolean();
        String name = jwt.getClaim("name").asString();

        if (!emailVerified) {
            throw new RuntimeException("Email not verified by Apple");
        }

        UserDto userDto = new UserDto();
        userDto.setEmailId(email);
        String randomPassword = UUID.randomUUID().toString();
        userDto.setPassword(randomPassword);
        userDto.setName(name);
        userDto.setSourceOfAuth("Apple OAuth");

        return verifyUser(userDto);
    }

    private String generateClientSecret(String clientId, String teamId, String keyId, String privateKey) {
        Algorithm algorithm = Algorithm.ECDSA256(null, getPrivateKey(privateKey));

        return JWT.create()
            .withIssuer(teamId)
            .withIssuedAt(new Date())
            .withExpiresAt(Date.from(Instant.now().plus(180, ChronoUnit.DAYS)))
            .withAudience("https://appleid.apple.com")
            .withSubject(clientId)
            .withKeyId(keyId)
            .sign(algorithm);
    }

    private ECPrivateKey getPrivateKey(String privateKeyContent) {
        try {
            // Remove headers and format
            String privateKey = privateKeyContent
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

            byte[] pkcs8EncodedBytes = Base64.getDecoder().decode(privateKey);

            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(pkcs8EncodedBytes);
            KeyFactory kf = KeyFactory.getInstance("EC");
            return (ECPrivateKey) kf.generatePrivate(keySpec);
        } catch (Exception e) {
            throw new RuntimeException("Invalid Apple private key", e);
        }
    }

    // Sign In via Google OAuth
    public UserDto signInGoogleUser(String googleToken) {
        try {
            GoogleIdToken idToken = verifyGoogleToken(googleToken);
            
            if (idToken != null) {
                GoogleIdToken.Payload payload = idToken.getPayload();

                String email = payload.getEmail();

                List<Users> users = userRepository.findByEmailId(email);
                if (users.isEmpty()) {
                    throw new RuntimeException("User not registered");
                }
                Users user = users.get(0);

                UserDto userDto = new UserDto();
                userDto.setId(user.getId());
                userDto.setEmailId(email);
                userDto.setPhoneNumber(user.getPhoneNumber());
                userDto.setPassword(null);
                userDto.setName(user.getName());
                 userDto.setIsPrimary(user.getIsPrimary());
                // Get user permissions
                List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(user.getId());
                userDto.setJwtToken(jwtService.generateToken(user.getEmailId(), user.getId(), permissions));
                 // If this is a secondary user, also include relationship info
            if (user.getIsPrimary() != null && !user.getIsPrimary()) {
                Optional<UserRelationship> relationship = membershipRepository.findAll().stream()
                        .filter(r -> r.getSecondaryUserId() != null && 
                              r.getSecondaryUserId() == user.getId() &&
                              r.getStatus().equals("ACTIVE"))
                        .findFirst();
                        
                if (relationship.isPresent()) {
                    // You can add primary user info to the response if needed
                    userDto.setPrimaryUserId(relationship.get().getPrimaryUserId());
                    userDto.setPermissionType(relationship.get().getPermissionType());
                }
            }
                return userDto;
            } else {
                throw new RuntimeException("Invalid Google token");
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    // Sign In via Apple OAuth
    public UserDto signInAppleUser(String appleToken) {
        try {
            // Parse the ID token (JWT from Apple)
            SignedJWT signedJWT = SignedJWT.parse(appleToken);

            if (appleToken != null) {
                JWTClaimsSet claims = signedJWT.getJWTClaimsSet();

                String email = claims.getStringClaim("email");

                List<Users> users = userRepository.findByEmailId(email);
                if (users.isEmpty()) {
                    throw new RuntimeException("User not found");
                }
                Users user = users.get(0);

                UserDto userDto = new UserDto();
                userDto.setId(user.getId());
                userDto.setEmailId(email);
                userDto.setPhoneNumber(user.getPhoneNumber());
                userDto.setPassword(null);
                userDto.setName(user.getName());
                 userDto.setIsPrimary(user.getIsPrimary()); 
                // Get user permissions
                List<UserPermissionDto> permissions = aclService.getPermissionsByUserId(user.getId());
                userDto.setJwtToken(jwtService.generateToken(user.getEmailId(), user.getId(), permissions));

                return userDto;
            } else {
                throw new RuntimeException("Invalid Apple ID token");
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to verify Apple ID token: " + e.getMessage(), e);
        }
    }

    public void resetPassword(UserDto userDto) {
        if (userDto.getEmailId() != null) {
            List<Users> users = userRepository.findByEmailId(userDto.getEmailId());
            if (users.isEmpty()) {
                throw new RuntimeException("User not found");
            }
            Users user = users.get(0);
            user.setPassword(encoder.encode(userDto.getPassword()));
            userRepository.save(user);
        } else if (userDto.getPhoneNumber() != null) {
            List<Users> users = userRepository.findByPhoneNumber(userDto.getPhoneNumber());
            if (users.isEmpty()) {
                throw new RuntimeException("User not found");
            }
            Users user = users.get(0);
            user.setPassword(encoder.encode(userDto.getPassword()));
            userRepository.save(user);
        } else {
            throw new RuntimeException("Email or phone number is required");
        }
    }
   
    public UserDto fetchUserById(Integer userId) {   // ← no space
    Users user = userRepository.findById(userId)
                               .orElseThrow(() -> new RuntimeException("User not found"));
    return convertToDto(user);
}

    public UserDto updateUser (UserDto userDto) {
        Users user = userRepository.findById(userDto.getId()).orElseThrow(() -> new RuntimeException("User  not found"));
        user.setName(userDto.getName());
        user.setEmailId(userDto.getEmailId());
        user.setPhoneNumber(userDto.getPhoneNumber());
        user.setAddress(userDto.getAddress());
        user.setBio(userDto.getBio());
        userRepository.save(user);
        return convertToDto(user);
    }
      private UserDto convertToDto(Users user) {
        UserDto userDto = new UserDto();
        userDto.setId(user.getId());
        userDto.setEmailId(user.getEmailId());
        userDto.setName(user.getName());
        userDto.setPhoneNumber(user.getPhoneNumber());
        userDto.setAddress(user.getAddress());
        userDto.setBio(user.getBio());
        userDto.setIsPrimary(user.getIsPrimary());
        return userDto;
    }

// When a secondary user logs in or completes signup:
public List<UserPermissionDto> getPermissionsForUser(Users user) {
    if (user.getIsPrimary() != null && user.getIsPrimary()) {
        // Primary user: get all their permissions
        return aclService.getPermissionsByUserId(user.getId());
    } else {
        // Secondary user: find their relationship and filter permissions
        Optional<UserRelationship> relOpt = membershipRepository.findBySecondaryUserId(user.getId());
        if (relOpt.isPresent() && "ACTIVE".equals(relOpt.get().getStatus())) {
            int primaryUserId = relOpt.get().getPrimaryUserId();
            // Get permissions as secondary user (returns List<SecondaryUserPermissionDto>)
            List<SecondaryUserPermissionDto> secondaryPermissions = aclService.getSecondaryUserPermissions(primaryUserId, user.getId());
            // Convert to List<UserPermissionDto>
            return secondaryPermissions.stream()
                .map(p -> new UserPermissionDto(p.getPageName(), 
                    com.pennypal.fintech.entity.AclActions.ActionName.valueOf(p.getActionName())))
                .collect(java.util.stream.Collectors.toList());
        } else {
            // Not a valid secondary user, return empty or throw
            return List.of();
        }
    }
}
}