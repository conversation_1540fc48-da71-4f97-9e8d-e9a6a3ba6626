package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.plaid.client.model.*;
import com.plaid.client.request.PlaidApi;
import com.pennypal.fintech.util.PennyPalPlaidApi;
import com.pennypal.fintech.dto.*;
import com.pennypal.fintech.entity.*;
import com.pennypal.fintech.repository.*;

import retrofit2.Response;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service
public class PlaidInvestmentService {
    private static final Logger logger = LoggerFactory.getLogger(PlaidInvestmentService.class);

    @Autowired
    private InvestmentRepository investmentRepository;
    
    @Autowired
    private DailyInvestmentStockRepository dailyInvestmentStockRepository;
    
    @Autowired
    private AccountRepository accountRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PennyPalPlaidApi pennyPalPlaidApi;
    
    @Autowired
    private PlaidApi plaidApiClient;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;

    @Value("${plaid.client_id}")
    private String clientId;

    @Value("${plaid.secret}")
    private String secret;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * Sync all investment holdings for a user's investment accounts
     */
    @Transactional
    public List<Investment> syncInvestmentHoldingsForUser(int userId) {
        logger.info("Starting investment holdings sync for user: {}", userId);
        
        Optional<Users> userOpt = userRepository.findById(userId);
        if (userOpt.isEmpty()) {
            throw new RuntimeException("User not found with ID: " + userId);
        }
        
        Users user = userOpt.get();
        // Fixed: Use proper repository method signature
        List<Accounts> investmentAccounts = accountRepository.findByUserIdAndAccountCategory(userId, "Investment Accounts");
        
        if (investmentAccounts.isEmpty()) {
            logger.info("No investment accounts found for user: {}", userId);
            return new ArrayList<>();
        }
        
        List<Investment> allInvestments = new ArrayList<>();
        
        for (Accounts account : investmentAccounts) {
            try {
                List<Investment> accountInvestments = syncInvestmentHoldingsForAccount(account);
                allInvestments.addAll(accountInvestments);
            } catch (Exception e) {
                logger.error("Error syncing investments for account ID: {}", account.getId(), e);
            }
        }
        
        logger.info("Completed investment holdings sync for user: {}, total holdings: {}", userId, allInvestments.size());
        return allInvestments;
    }

    /**
     * Sync investment holdings for a specific account
     */
    @Transactional
    public List<Investment> syncInvestmentHoldingsForAccount(Accounts account) {
        logger.info("Syncing investment holdings for account ID: {}", account.getId());
        
        try {
            InvestmentsHoldingsGetRequest request = new InvestmentsHoldingsGetRequest()
                .accessToken(account.getAccessToken());
            
            Response<InvestmentsHoldingsGetResponse> response = pennyPalPlaidApi.client()
                .investmentsHoldingsGet(request)
                .execute();
            
            if (!response.isSuccessful() || response.body() == null) {
                logger.error("Failed to fetch investment holdings for account ID: {}, HTTP code: {}", 
                    account.getId(), response.code());
                return new ArrayList<>();
            }
            
            InvestmentsHoldingsGetResponse holdingsResponse = response.body();
            List<Holding> holdings = holdingsResponse.getHoldings();
            List<Security> securities = holdingsResponse.getSecurities();
            
            // Create a map of security ID to security details for quick lookup
            Map<String, Security> securityMap = securities.stream()
                .collect(Collectors.toMap(Security::getSecurityId, security -> security));
            
            List<Investment> investments = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            String syncId = generateSyncId(account.getId());
            
            for (Holding holding : holdings) {
                // Skip holdings not belonging to this account
                if (!holding.getAccountId().equals(account.getPlaidUniqueNo())) {
                    continue;
                }
                
                Security security = securityMap.get(holding.getSecurityId());
                Investment investment = processHolding(holding, security, account, now, syncId);
                
                if (investment != null) {
                    investments.add(investment);
                }
            }
            
            // Save all investments
            if (!investments.isEmpty()) {
                List<Investment> savedInvestments = investmentRepository.saveAll(investments);
                logger.info("Saved {} investment holdings for account ID: {}", savedInvestments.size(), account.getId());
                
                // Update daily stock records
                updateDailyStockRecords(savedInvestments, now);
                
                return savedInvestments;
            }
            
            return new ArrayList<>();
            
        } catch (Exception e) {
            logger.error("Error syncing investment holdings for account ID: {}", account.getId(), e);
            throw new RuntimeException("Failed to sync investment holdings for account ID: " + account.getId(), e);
        }
    }

    /**
     * Process a single holding and create/update Investment entity
     */
    private Investment processHolding(Holding holding, Security security, Accounts account, 
                                    LocalDateTime syncTime, String syncId) {
        try {
            // Check if investment already exists
            Optional<Investment> existingInvestment = investmentRepository
                .findByInvestmentId(holding.getSecurityId() + "_" + account.getId());
            
            Investment investment = existingInvestment.orElse(new Investment());
            
            // Set basic investment details
            investment.setUser(account.getUser());
            investment.setAccount(account);
            investment.setInvestmentId(holding.getSecurityId() + "_" + account.getId());
            investment.setSecurityId(holding.getSecurityId());
            investment.setQuantity(holding.getQuantity());
            investment.setValue(holding.getInstitutionValue());
            investment.setCostBasis(holding.getCostBasis());
            investment.setLastUpdated(syncTime);
            investment.setSyncId(syncId);
            
            if (!existingInvestment.isPresent()) {
                investment.setInsertDateTime(syncTime);
            }
            
            // Set security details if available
            if (security != null) {
                investment.setSecurityName(security.getName());
                investment.setTicker(security.getTickerSymbol());
                investment.setSecurityType(security.getType());
                investment.setCurrencyCode(security.getIsoCurrencyCode());
                
                // Calculate current price and average purchase price
                if (holding.getQuantity() != null && holding.getQuantity() > 0) {
                    if (holding.getInstitutionValue() != null) {
                        investment.setCurrentPrice(holding.getInstitutionValue() / holding.getQuantity());
                    }
                    
                    if (holding.getCostBasis() != null) {
                        investment.setAveragePurchasePrice(holding.getCostBasis() / holding.getQuantity());
                    }
                }
            }
            
            return investment;
            
        } catch (Exception e) {
            logger.error("Error processing holding for security ID: {}", holding.getSecurityId(), e);
            return null;
        }
    }

    /**
     * Update daily stock records for investments
     */
    private void updateDailyStockRecords(List<Investment> investments, LocalDateTime timestamp) {
        logger.info("Updating daily stock records for {} investments", investments.size());
        
        List<DailyInvestmentStock> dailyRecords = new ArrayList<>();
        
        for (Investment investment : investments) {
            try {
                // Get previous record to calculate price change
                DailyInvestmentStock previousRecord = dailyInvestmentStockRepository
                    .findLatestByInvestmentId(investment.getId());
                
                DailyInvestmentStock dailyRecord = new DailyInvestmentStock();
                dailyRecord.setInvestment(investment);
                dailyRecord.setPrice(investment.getCurrentPrice());
                dailyRecord.setQuantity(investment.getQuantity());
                dailyRecord.setValue(investment.getValue());
                dailyRecord.setTimestamp(timestamp);
                
                // Calculate price change and percent change
                if (previousRecord != null && previousRecord.getPrice() != null && 
                    investment.getCurrentPrice() != null) {
                    Double priceChange = investment.getCurrentPrice() - previousRecord.getPrice();
                    dailyRecord.setPriceChange(priceChange);
                    
                    if (previousRecord.getPrice() > 0) {
                        Double percentChange = (priceChange / previousRecord.getPrice()) * 100;
                        dailyRecord.setPercentChange(percentChange);
                    }
                }
                
                dailyRecords.add(dailyRecord);
                
            } catch (Exception e) {
                logger.error("Error creating daily record for investment ID: {}", investment.getId(), e);
            }
        }
        
        if (!dailyRecords.isEmpty()) {
            dailyInvestmentStockRepository.saveAll(dailyRecords);
            logger.info("Saved {} daily investment stock records", dailyRecords.size());
        }
    }

    /**
     * Get investment transactions for an account
     */
    @Transactional
    public List<InvestmentTransaction> getInvestmentTransactions(int accountId, int daysBack) {
        logger.info("Fetching investment transactions for account ID: {}, days back: {}", accountId, daysBack);
        
        Optional<Accounts> accountOpt = accountRepository.findById(accountId);
        if (accountOpt.isEmpty()) {
            throw new RuntimeException("Account not found with ID: " + accountId);
        }
        
        Accounts account = accountOpt.get();
        
        try {
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = endDate.minusDays(daysBack);
            
            InvestmentsTransactionsGetRequest request = new InvestmentsTransactionsGetRequest()
                .accessToken(account.getAccessToken())
                .startDate(startDate)
                .endDate(endDate);
            
            Response<InvestmentsTransactionsGetResponse> response = pennyPalPlaidApi.client()
                .investmentsTransactionsGet(request)
                .execute();
            
            if (!response.isSuccessful() || response.body() == null) {
                logger.error("Failed to fetch investment transactions for account ID: {}", accountId);
                return new ArrayList<>();
            }
            
            InvestmentsTransactionsGetResponse transactionsResponse = response.body();
            List<InvestmentTransaction> transactions = transactionsResponse.getInvestmentTransactions();
            
            // Filter transactions for this specific account
            List<InvestmentTransaction> accountTransactions = transactions.stream()
                .filter(tx -> tx.getAccountId().equals(account.getPlaidUniqueNo()))
                .collect(Collectors.toList());
            
            logger.info("Retrieved {} investment transactions for account ID: {}", accountTransactions.size(), accountId);
            return accountTransactions;
            
        } catch (Exception e) {
            logger.error("Error fetching investment transactions for account ID: {}", accountId, e);
            throw new RuntimeException("Failed to fetch investment transactions for account ID: " + accountId, e);
        }
    }

    /**
     * Get portfolio summary for a user
     */
    public PortfolioSummaryDto getPortfolioSummary(int userId) {
        logger.info("Generating portfolio summary for user: {}", userId);
        
        List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
        
        if (userInvestments.isEmpty()) {
            logger.info("No investments found for user: {}", userId);
            return createEmptyPortfolioSummary();
        }
        
        PortfolioSummaryDto summary = new PortfolioSummaryDto();
        
        // Calculate totals
        Double totalValue = userInvestments.stream()
            .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
            .sum();
        
        Double totalCostBasis = userInvestments.stream()
            .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
            .sum();
        
        Double totalGainLoss = totalValue - totalCostBasis;
        Double gainLossPercentage = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0.0;
        
        summary.setTotalValue(totalValue);
        summary.setTotalCostBasis(totalCostBasis);
        summary.setTotalGainLoss(totalGainLoss);
        summary.setGainLossPercentage(gainLossPercentage);
        summary.setNumberOfHoldings(userInvestments.size());
        
        // Get unique accounts
        Set<Integer> uniqueAccountIds = userInvestments.stream()
            .map(inv -> inv.getAccount().getId())
            .collect(Collectors.toSet());
        summary.setNumberOfAccounts(uniqueAccountIds.size());
        
        // Set last updated time
        Optional<LocalDateTime> lastUpdated = userInvestments.stream()
            .map(Investment::getLastUpdated)
            .filter(Objects::nonNull)
            .max(LocalDateTime::compareTo);
        summary.setLastUpdated(lastUpdated.orElse(LocalDateTime.now()));
        
        // Create account summaries
        List<InvestmentAccountSummaryDto> accountSummaries = createAccountSummaries(userInvestments);
        summary.setAccounts(accountSummaries);
        
        logger.info("Generated portfolio summary for user: {} - Total Value: {}, Holdings: {}, Accounts: {}", 
            userId, totalValue, userInvestments.size(), uniqueAccountIds.size());
        
        return summary;
    }

    /**
     * Create account summaries from investments
     */
    private List<InvestmentAccountSummaryDto> createAccountSummaries(List<Investment> investments) {
        Map<Integer, List<Investment>> investmentsByAccount = investments.stream()
            .collect(Collectors.groupingBy(inv -> inv.getAccount().getId()));
        
        return investmentsByAccount.entrySet().stream()
            .map(entry -> {
                Integer accountId = entry.getKey();
                List<Investment> accountInvestments = entry.getValue();
                
                InvestmentAccountSummaryDto summary = new InvestmentAccountSummaryDto();
                // Fixed: Convert Integer to String properly
                summary.setAccountId(String.valueOf(accountId));
                
                // Get account name from first investment
                if (!accountInvestments.isEmpty()) {
                    Accounts account = accountInvestments.get(0).getAccount();
                    summary.setAccountName(account.getAccountName());
                    summary.setAccountType(account.getAccountType());
                }
                
                // Calculate account totals
                Double accountValue = accountInvestments.stream()
                    .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
                    .sum();
                summary.setAccountValue(accountValue);
                summary.setHoldingsCount(accountInvestments.size());
                
                // Get latest update time
                Optional<LocalDateTime> lastUpdated = accountInvestments.stream()
                    .map(Investment::getLastUpdated)
                    .filter(Objects::nonNull)
                    .max(LocalDateTime::compareTo);
                summary.setLastUpdated(lastUpdated.orElse(LocalDateTime.now()));
                
                return summary;
            })
            .collect(Collectors.toList());
    }
/**
 * Get investment holdings for a specific account
 */
public InvestmentHoldingsResponseDto getInvestmentHoldings(int accountId) {
    logger.info("Fetching investment holdings for account ID: {}", accountId);
    
    Optional<Accounts> accountOpt = accountRepository.findById(accountId);
    if (accountOpt.isEmpty()) {
        throw new RuntimeException("Account not found with ID: " + accountId);
    }
    
    Accounts account = accountOpt.get();
    List<Investment> investments = investmentRepository.findByAccountId(accountId);
    
    InvestmentHoldingsResponseDto response = new InvestmentHoldingsResponseDto();
    response.setAccountId(String.valueOf(accountId));
    response.setAccountName(account.getAccountName());
    response.setTotalHoldings(investments.size());
    
    // Calculate total value
    Double totalValue = investments.stream()
        .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
        .sum();
    response.setTotalValue(totalValue);
    
    // Convert investments to generic holdings DTOs
    List<HoldingDto> holdings = investments.stream()
        .map(this::convertToHoldingDto)
        .collect(Collectors.toList());
    response.setHoldings(holdings);
    
    // Set last updated time
    Optional<LocalDateTime> lastUpdated = investments.stream()
        .map(Investment::getLastUpdated)
        .filter(Objects::nonNull)
        .max(LocalDateTime::compareTo);
    response.setLastUpdated(lastUpdated.orElse(LocalDateTime.now()));
    
    return response;
}

    /**
 * Convert Investment entity to generic HoldingDto
 */
private HoldingDto convertToHoldingDto(Investment investment) {
    HoldingDto dto = new HoldingDto();
    
    // Set basic fields
    dto.setSecurityId(investment.getSecurityId());
    dto.setSecurityName(investment.getSecurityName());
    dto.setTicker(investment.getTicker());
    dto.setSecurityType(investment.getSecurityType());
    dto.setQuantity(investment.getQuantity());
    dto.setValue(investment.getValue());
    dto.setCostBasis(investment.getCostBasis());
    dto.setCurrentPrice(investment.getCurrentPrice());
    dto.setAveragePurchasePrice(investment.getAveragePurchasePrice());
    dto.setCurrencyCode(investment.getCurrencyCode());
    dto.setLastUpdated(investment.getLastUpdated());
    dto.setAccountId(String.valueOf(investment.getAccount().getId()));
    dto.setInvestmentId(investment.getInvestmentId());
    dto.setProvider("plaid"); // Indicate this is from Plaid
    
    // Calculate gain/loss
    if (investment.getValue() != null && investment.getCostBasis() != null) {
        Double gainLoss = investment.getValue() - investment.getCostBasis();
        dto.setGainLoss(gainLoss);
        
        if (investment.getCostBasis() > 0) {
            Double gainLossPercentage = (gainLoss / investment.getCostBasis()) * 100;
            dto.setGainLossPercentage(gainLossPercentage);
        }
    }
    
    return dto;
}

    /**
     * Helper method to check if a setter method exists (reflection-based)
     * This is a workaround for missing setter methods in DTO
     */
    private boolean hasSetterMethod(Object obj, String methodName) {
        try {
            // This is a simplified check - you might want to make it more robust
            obj.getClass().getMethod(methodName, String.class);
            return true;
        } catch (NoSuchMethodException e) {
            try {
                obj.getClass().getMethod(methodName, Double.class);
                return true;
            } catch (NoSuchMethodException e2) {
                return false;
            }
        }
    }

    /**
     * Sync investment data based on request
     */
    @Async
    @Transactional
    public void syncInvestmentData(SyncInvestmentRequestDto request) {
        logger.info("Starting investment sync for user: {}, account: {}", 
            request.getUserId(), request.getAccountId());
        
        try {
            if (request.getAccountId() != null) {
                // Sync specific account
                Optional<Accounts> accountOpt = accountRepository.findById(Integer.parseInt(request.getAccountId()));
                if (accountOpt.isPresent()) {
                    syncInvestmentHoldingsForAccount(accountOpt.get());
                    
                    if (Boolean.TRUE.equals(request.getIncludeTransactions())) {
                        int daysBack = request.getDaysBack() != null ? request.getDaysBack() : 90;
                        getInvestmentTransactions(Integer.parseInt(request.getAccountId()), daysBack);
                    }
                }
            } else {
                // Sync all investment accounts for user
                syncInvestmentHoldingsForUser(request.getUserId());
                
                if (Boolean.TRUE.equals(request.getIncludeTransactions())) {
                    // Fixed: Use proper repository method signature
                    List<Accounts> investmentAccounts = accountRepository
                        .findByUserIdAndAccountCategory(request.getUserId(), "Investment Accounts");
                    
                    int daysBack = request.getDaysBack() != null ? request.getDaysBack() : 90;
                    
                    for (Accounts account : investmentAccounts) {
                        getInvestmentTransactions(account.getId(), daysBack);
                    }
                }
            }
            
            logger.info("Completed investment sync for user: {}", request.getUserId());
            
        } catch (Exception e) {
            logger.error("Error during investment sync for user: {}", request.getUserId(), e);
        }
    }

    /**
     * Create empty portfolio summary
     */
    private PortfolioSummaryDto createEmptyPortfolioSummary() {
        PortfolioSummaryDto summary = new PortfolioSummaryDto();
        summary.setTotalValue(0.0);
        summary.setTotalCostBasis(0.0);
        summary.setTotalGainLoss(0.0);
        summary.setGainLossPercentage(0.0);
        summary.setNumberOfHoldings(0);
        summary.setNumberOfAccounts(0);
        summary.setLastUpdated(LocalDateTime.now());
        summary.setAccounts(new ArrayList<>());
        return summary;
    }

    /**
     * Generate a unique sync ID
     */
    private String generateSyncId(int accountId) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        return "INV_" + accountId + "_" + timestamp;
    }

    /**
     * Get investment account summary
     */
    public List<InvestmentAccountResponseDto> getInvestmentAccountSummary(int userId) {
    logger.info("Getting investment account summary for user: {}", userId);
    
    List<Accounts> investmentAccounts = accountRepository
        .findByUserIdAndAccountCategory(userId, "Investment Accounts");
    
    return investmentAccounts.stream()
        .map(account -> {
            InvestmentAccountResponseDto dto = new InvestmentAccountResponseDto();
            dto.setId(String.valueOf(account.getId()));
            dto.setName(account.getAccountName());
            dto.setType(account.getAccountType());
            dto.setAccountType(account.getAccountCategory());
            dto.setBalance(account.getBalance());
            dto.setAccountNumberDisplay(account.getAccountMask());
            dto.setDisplayName(account.getAccountName());
            dto.setStatus("active");
            dto.setCurrency(account.getCurrencyType());
            
            if (account.getLastSyncTime() != null) {
                dto.setLastUpdated(account.getLastSyncTime().toEpochSecond(java.time.ZoneOffset.UTC));
            }
            if (account.getInsertDatetime() != null) {
                dto.setCreatedDate(account.getInsertDatetime().toEpochSecond(java.time.ZoneOffset.UTC));
            }
            
            // Get holdings for this account
            List<Investment> investments = investmentRepository.findByAccountId(account.getId());
            List<HoldingDto> holdings = investments.stream()
                .map(this::convertToHoldingDto)
                .collect(Collectors.toList());
            dto.setHoldings(holdings);
            
            return dto;
        })
        .collect(Collectors.toList());
}
}