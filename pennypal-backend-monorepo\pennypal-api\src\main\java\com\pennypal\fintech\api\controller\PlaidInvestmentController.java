package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.*;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.service.PlaidInvestmentService;
import com.plaid.client.model.InvestmentTransaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@RestController
@RequestMapping("/api/v1/plaid/investment")
public class PlaidInvestmentController {

    private static final Logger logger = LoggerFactory.getLogger(PlaidInvestmentController.class);

    @Autowired
    private PlaidInvestmentService plaidInvestmentService;

    /**
     * Get investment account summary for a user
     */
    @GetMapping("/accounts/{userId}")
    public ResponseEntity<?> getInvestmentAccounts(@PathVariable Integer userId) {
        try {
            logger.info("Getting investment accounts for user: {}", userId);
            List<InvestmentAccountResponseDto> accounts = plaidInvestmentService.getInvestmentAccountSummary(userId);
            return ResponseEntity.ok(accounts);
        } catch (Exception e) {
            logger.error("Error getting investment accounts for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving investment accounts: " + e.getMessage());
        }
    }

    /**
     * Get holdings for a specific investment account
     */
    @GetMapping("/accounts/{userId}/holdings/{accountId}")
    public ResponseEntity<?> getAccountHoldings(
            @PathVariable Integer userId,
            @PathVariable Integer accountId) {
        try {
            logger.info("Getting holdings for user: {} and account: {}", userId, accountId);
            InvestmentHoldingsResponseDto holdings = plaidInvestmentService.getInvestmentHoldings(accountId);
            return ResponseEntity.ok(holdings);
        } catch (RuntimeException e) {
            logger.error("Error getting holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Error retrieving holdings: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error getting holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving holdings: " + e.getMessage());
        }
    }

    /**
     * Sync investment holdings for a user
     */
    @PostMapping("/sync-holdings/{userId}")
    public ResponseEntity<?> syncInvestmentHoldings(@PathVariable Integer userId) {
        try {
            logger.info("Syncing investment holdings for user: {}", userId);
            List<Investment> investments = plaidInvestmentService.syncInvestmentHoldingsForUser(userId);
            return ResponseEntity.ok("Successfully synced " + investments.size() + " investment holdings");
        } catch (RuntimeException e) {
            logger.error("Error syncing holdings for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error syncing holdings: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error syncing holdings for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing holdings: " + e.getMessage());
        }
    }

    /**
     * Sync investment holdings for a specific account
     */
    @PostMapping("/sync-holdings/{userId}/account/{accountId}")
    public ResponseEntity<?> syncAccountHoldings(
            @PathVariable Integer userId,
            @PathVariable Integer accountId) {
        try {
            logger.info("Syncing investment holdings for user: {} and account: {}", userId, accountId);
            
            // Create sync request for specific account
            SyncInvestmentRequestDto request = new SyncInvestmentRequestDto();
            request.setUserId(userId);
            request.setAccountId(String.valueOf(accountId));
            request.setIncludeTransactions(false);
            
            plaidInvestmentService.syncInvestmentData(request);
            return ResponseEntity.ok("Investment holdings sync initiated for account: " + accountId);
        } catch (Exception e) {
            logger.error("Error syncing holdings for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing holdings: " + e.getMessage());
        }
    }

    /**
     * Get investment transactions for an account
     */
    @GetMapping("/accounts/{userId}/transactions/{accountId}")
    public ResponseEntity<?> getInvestmentTransactions(
            @PathVariable Integer userId,
            @PathVariable Integer accountId,
            @RequestParam(defaultValue = "90") Integer daysBack) {
        try {
            logger.info("Getting investment transactions for user: {}, account: {}, days back: {}", 
                    userId, accountId, daysBack);
            List<InvestmentTransaction> transactions = plaidInvestmentService.getInvestmentTransactions(accountId, daysBack);
            return ResponseEntity.ok(transactions);
        } catch (RuntimeException e) {
            logger.error("Error getting transactions for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("Error retrieving transactions: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error getting transactions for user: {} and account: {}", userId, accountId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving transactions: " + e.getMessage());
        }
    }

    /**
     * Get user's investment portfolio summary
     */
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<?> getInvestmentPortfolio(@PathVariable Integer userId) {
        try {
            logger.info("Getting investment portfolio for user: {}", userId);
            PortfolioSummaryDto portfolio = plaidInvestmentService.getPortfolioSummary(userId);
            return ResponseEntity.ok(portfolio);
        } catch (Exception e) {
            logger.error("Error getting portfolio for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving portfolio: " + e.getMessage());
        }
    }

    /**
     * Comprehensive sync - sync holdings and transactions
     */
    @PostMapping("/sync-all/{userId}")
    public ResponseEntity<?> syncAllInvestmentData(
            @PathVariable Integer userId,
            @RequestParam(required = false) String accountId,
            @RequestParam(defaultValue = "false") Boolean includeTransactions,
            @RequestParam(defaultValue = "90") Integer daysBack) {
        try {
            logger.info("Starting comprehensive sync for user: {}, account: {}, includeTransactions: {}", 
                    userId, accountId, includeTransactions);
                    
            SyncInvestmentRequestDto request = new SyncInvestmentRequestDto();
            request.setUserId(userId);
            request.setAccountId(accountId);
            request.setIncludeTransactions(includeTransactions);
            request.setDaysBack(daysBack);
            
            plaidInvestmentService.syncInvestmentData(request);
            
            String message = accountId != null ? 
                    "Investment data sync initiated for account: " + accountId :
                    "Investment data sync initiated for all user accounts";
                    
            return ResponseEntity.ok(message);
        } catch (Exception e) {
            logger.error("Error during comprehensive sync for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error syncing investment data: " + e.getMessage());
        }
    }

    /**
     * Get investment holdings by security type
     */
    @GetMapping("/holdings/{userId}/by-type")
    public ResponseEntity<?> getHoldingsByType(@PathVariable Integer userId) {
        try {
            logger.info("Getting holdings by type for user: {}", userId);
            PortfolioSummaryDto portfolio = plaidInvestmentService.getPortfolioSummary(userId);
            
            // You can enhance this to group holdings by security type
            // For now, return the portfolio summary
            return ResponseEntity.ok(portfolio);
        } catch (Exception e) {
            logger.error("Error getting holdings by type for user: {}", userId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Error retrieving holdings by type: " + e.getMessage());
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<?> healthCheck() {
        return ResponseEntity.ok("Plaid Investment Service is running");
    }
}