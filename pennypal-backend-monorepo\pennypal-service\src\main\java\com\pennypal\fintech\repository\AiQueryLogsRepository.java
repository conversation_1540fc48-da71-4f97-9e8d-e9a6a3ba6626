package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.AiQueryLogs;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AiQueryLogsRepository extends JpaRepository<AiQueryLogs, Long> {
    AiQueryLogs findById(Integer id);
    List<AiQueryLogs> findByUserId(Integer userId);

    @Query("UPDATE AiQueryLogs a SET a.isSaved = :isSaved WHERE a.id = :chatId")
    void updateIsSavedByChatId(@Param("chatId") Integer chatId, @Param("isSaved") Boolean isSaved);
}