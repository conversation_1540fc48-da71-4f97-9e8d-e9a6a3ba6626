package com.pennypal.fintech.entity;


import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;

//import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Entity
@Table(name = "sub_category")
@Data
public class SubCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // Auto-increment for MySQL
    private int id;

    @ManyToOne
    @JoinColumn(name = "category_id", nullable = false)
   // @JsonIgnoreProperties("subCategories")  // This prevents infinite recursion
    private Category category; // Assuming Category is another entity

    @Column(name = "sub_category", length = 255)
    private String subCategory;

    @Column(name = "icon_key", nullable = true)  // New field
    private String iconKey;  // Add this field to map the icon_key column


    @Column(name = "insert_Datetime")
    private Date insertDatetime;

    @Column(name = "update_Datetime")
    private Date updateDatetime;

    @Column(name = "finicity_category")
    private String finicityCategory;
   
    @Column(name = "mx_category")
    private String mxCategory;
    
      @Lob
@Column(name = "icon_blob", columnDefinition = "LONGBLOB")
private byte[] iconBlob;
}
