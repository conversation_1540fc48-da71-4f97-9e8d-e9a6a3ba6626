package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import java.sql.Timestamp;
import lombok.*;

@Entity
@Table(name = "custom_charts")
@Data
public class CustomChart {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "chat_id")
    private Integer chatId;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "title")
    private String title;

    @Column(name = "type")
    private String type;

    @Column(columnDefinition = "TEXT", name = "data")
    @Lob
    private String data;

    @Column(name = "created_at", insertable = false, updatable = false,
            columnDefinition = "TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
    private Timestamp createdAt;
}