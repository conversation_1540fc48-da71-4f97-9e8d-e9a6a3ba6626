package com.pennypal.fintech.dto;




import lombok.Data;



@Data
public class UserDto {

    private Integer id;
    private String emailId;
    private String phoneNumber;
    private String password;
    private String name;
    private String address;
    private String bio;
    private String jwtToken;
    private String encodedEmail;
    private Integer otp;
    private String refreshToken;
    private Boolean rememberMe;
    private String sourceOfAuth;
    private String verificationToken;
    private Boolean isPrimary;
 
   private Integer primaryUserId;
    private String  permissionType;
    
}
