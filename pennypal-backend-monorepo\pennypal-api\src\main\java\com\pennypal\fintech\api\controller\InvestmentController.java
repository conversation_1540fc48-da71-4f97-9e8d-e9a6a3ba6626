// package com.pennypal.fintech.api.controller;

// import com.pennypal.fintech.entity.Investment;
// import com.pennypal.fintech.dto.InvestmentDto;
// import com.pennypal.fintech.entity.DailyInvestmentStock;
// import com.pennypal.fintech.service.InvestmentService;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.format.annotation.DateTimeFormat;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.*;

// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.Optional;

// @RestController
// @RequestMapping("/api/investments")
// @Slf4j
// public class InvestmentController {

//     private final InvestmentService investmentService;

//     public InvestmentController(InvestmentService investmentService) {
//         this.investmentService = investmentService;
//     }

//     // 1. Sync investments for a user
//     @PostMapping("/sync/{userId}")
//     public ResponseEntity<Map<String, Object>> syncInvestments(@PathVariable Integer userId) {
//         try {
//             String result = investmentService.syncAndSaveInvestmentData(userId);
//             Map<String, Object> response = new HashMap<>();
//             response.put("success", true);
//             response.put("message", result);
//             response.put("userId", userId);
//             return ResponseEntity.ok(response);
//         } catch (IllegalArgumentException e) {
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", e.getMessage());
//             return ResponseEntity.badRequest().body(errorResponse);
//         } catch (Exception e) {
//             log.error("Error syncing investments for user {}: {}", userId, e.getMessage(), e);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", "Failed to sync investment data");
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
//         }
//     }

//     // 2. Get all investments for a user (returns DTOs)
//     @GetMapping("/user/{userId}")
//     public ResponseEntity<List<InvestmentDto>> getInvestmentsByUser(@PathVariable Integer userId) {
//         try {
//             List<InvestmentDto> investments = investmentService.getInvestmentsByUserId(userId);
//             return ResponseEntity.ok(investments);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching investments for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 3. Get investments by account ID (returns DTOs)
//     @GetMapping("/account/{accountId}")
//     public ResponseEntity<List<InvestmentDto>> getInvestmentsByAccount(@PathVariable Integer accountId) {
//         try {
//             List<InvestmentDto> investments = investmentService.getInvestmentsDtoByAccountId(accountId);
//             return ResponseEntity.ok(investments);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid account ID: {}", accountId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching investments for account {}: {}", accountId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 4. Get investment by investmentId (string GUID) - returns entity
//     @GetMapping("/by-guid/{investmentGuid}")
//     public ResponseEntity<Investment> getInvestmentByInvestmentId(@PathVariable String investmentGuid) {
//         try {
//             Optional<Investment> investment = investmentService.getInvestmentByInvestmentId(investmentGuid);
//             return investment.map(ResponseEntity::ok)
//                     .orElse(ResponseEntity.notFound().build());
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid investment GUID: {}", investmentGuid);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching investment by GUID {}: {}", investmentGuid, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 5. Get investment by DB ID - returns DTO
//     @GetMapping("/{id}")
//     public ResponseEntity<InvestmentDto> getInvestmentById(@PathVariable Integer id) {
//         try {
//             Optional<InvestmentDto> investment = investmentService.getInvestmentDtoById(id);
//             return investment.map(ResponseEntity::ok)
//                     .orElse(ResponseEntity.notFound().build());
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid investment ID: {}", id);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching investment by ID {}: {}", id, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 6. Get daily stocks data for a user
//     @GetMapping("/daily/user/{userId}")
//     public ResponseEntity<List<DailyInvestmentStock>> getUserDailyStockData(
//             @PathVariable Integer userId,
//             @RequestParam("from") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
//             @RequestParam("to") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {
//         try {
//             LocalDateTime from = fromDate.atStartOfDay();
//             LocalDateTime to = toDate.atTime(23, 59, 59);
//             List<DailyInvestmentStock> dailyData = investmentService.getUserDailyStockData(userId, from, to);
//             return ResponseEntity.ok(dailyData);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid parameters - userId: {}, from: {}, to: {}", userId, fromDate, toDate);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching daily stock data for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 7. Get daily stock data by investment ID
//     @GetMapping("/daily/investment/{investmentId}")
//     public ResponseEntity<List<DailyInvestmentStock>> getDailyStockData(
//             @PathVariable Integer investmentId,
//             @RequestParam("from") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
//             @RequestParam("to") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {
//         try {
//             LocalDateTime from = fromDate.atStartOfDay();
//             LocalDateTime to = toDate.atTime(23, 59, 59);
//             List<DailyInvestmentStock> dailyData = investmentService.getDailyStockData(investmentId, from, to);
//             return ResponseEntity.ok(dailyData);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid parameters - investmentId: {}, from: {}, to: {}", investmentId, fromDate, toDate);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching daily stock data for investment {}: {}", investmentId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 8. Get total portfolio value
//     @GetMapping("/total/user/{userId}")
//     public ResponseEntity<Map<String, Object>> getTotalPortfolioValue(@PathVariable Integer userId) {
//         try {
//             Double totalValue = investmentService.getTotalPortfolioValue(userId);
//             Map<String, Object> response = new HashMap<>();
//             response.put("userId", userId);
//             response.put("totalValue", totalValue);
//             return ResponseEntity.ok(response);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching total portfolio value for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 9. Get total value by account
//     @GetMapping("/total/account/{accountId}")
//     public ResponseEntity<Map<String, Object>> getAccountTotalValue(@PathVariable Integer accountId) {
//         try {
//             Double totalValue = investmentService.getAccountTotalValue(accountId);
//             Map<String, Object> response = new HashMap<>();
//             response.put("accountId", accountId);
//             response.put("totalValue", totalValue);
//             return ResponseEntity.ok(response);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid account ID: {}", accountId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching total account value for account {}: {}", accountId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 10. Delete investment
//     @DeleteMapping("/{investmentId}")
//     public ResponseEntity<Map<String, Object>> deleteInvestment(@PathVariable Integer investmentId) {
//         try {
//             investmentService.deleteInvestment(investmentId);
//             Map<String, Object> response = new HashMap<>();
//             response.put("success", true);
//             response.put("message", "Investment deleted successfully");
//             response.put("investmentId", investmentId);
//             return ResponseEntity.ok(response);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid investment ID: {}", investmentId);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", e.getMessage());
//             return ResponseEntity.badRequest().body(errorResponse);
//         } catch (RuntimeException e) {
//             log.error("Error deleting investment {}: {}", investmentId, e.getMessage(), e);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", e.getMessage());
//             return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
//         } catch (Exception e) {
//             log.error("Unexpected error deleting investment {}: {}", investmentId, e.getMessage(), e);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", "Failed to delete investment");
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
//         }
//     }

//     // 11. Get portfolio summary with statistics
//     @GetMapping("/portfolio/summary/{userId}")
//     public ResponseEntity<InvestmentService.PortfolioSummaryDto> getPortfolioSummary(@PathVariable Integer userId) {
//         try {
//             InvestmentService.PortfolioSummaryDto summary = investmentService.getPortfolioStatistics(userId);
//             return ResponseEntity.ok(summary);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching portfolio summary for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 12. Get investments by security type
//     @GetMapping("/user/{userId}/type/{securityType}")
//     public ResponseEntity<List<InvestmentDto>> getInvestmentsBySecurityType(
//             @PathVariable Integer userId,
//             @PathVariable String securityType) {
//         try {
//             List<InvestmentDto> investments = investmentService.getInvestmentsDtoBySecurityType(userId, securityType);
//             return ResponseEntity.ok(investments);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid parameters - userId: {}, securityType: {}", userId, securityType);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching investments by security type for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 13. Get stocks only
//     @GetMapping("/user/{userId}/stocks")
//     public ResponseEntity<List<InvestmentDto>> getStocksOnly(@PathVariable Integer userId) {
//         try {
//             List<InvestmentDto> stocks = investmentService.getStocksOnly(userId);
//             return ResponseEntity.ok(stocks);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching stocks for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 14. Get mutual funds only
//     @GetMapping("/user/{userId}/mutual-funds")
//     public ResponseEntity<List<InvestmentDto>> getMutualFundsOnly(@PathVariable Integer userId) {
//         try {
//             List<InvestmentDto> mutualFunds = investmentService.getMutualFundsOnly(userId);
//             return ResponseEntity.ok(mutualFunds);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching mutual funds for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 15. Get ETFs only
//     @GetMapping("/user/{userId}/etfs")
//     public ResponseEntity<List<InvestmentDto>> getETFsOnly(@PathVariable Integer userId) {
//         try {
//             List<InvestmentDto> etfs = investmentService.getETFsOnly(userId);
//             return ResponseEntity.ok(etfs);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching ETFs for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 16. Get bonds only
//     @GetMapping("/user/{userId}/bonds")
//     public ResponseEntity<List<InvestmentDto>> getBondsOnly(@PathVariable Integer userId) {
//         try {
//             List<InvestmentDto> bonds = investmentService.getBondsOnly(userId);
//             return ResponseEntity.ok(bonds);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching bonds for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 17. Get daily data by security type
//     @GetMapping("/daily/user/{userId}/type/{securityType}")
//     public ResponseEntity<List<DailyInvestmentStock>> getDailyDataBySecurityType(
//             @PathVariable Integer userId,
//             @PathVariable String securityType,
//             @RequestParam("from") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
//             @RequestParam("to") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {
//         try {
//             List<DailyInvestmentStock> dailyData = investmentService.getDailyDataBySecurityType(
//                     userId, securityType, fromDate, toDate);
//             return ResponseEntity.ok(dailyData);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid parameters - userId: {}, securityType: {}, from: {}, to: {}", 
//                     userId, securityType, fromDate, toDate);
//             return ResponseEntity.badRequest().build();
//         } catch (Exception e) {
//             log.error("Error fetching daily data by security type for user {}: {}", userId, e.getMessage(), e);
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
//         }
//     }

//     // 18. Update investments from daily data
//     @PostMapping("/update-from-daily/{userId}")
//     public ResponseEntity<Map<String, Object>> updateInvestmentsFromDailyData(@PathVariable Integer userId) {
//         try {
//             investmentService.updateInvestmentsFromDailyData(userId);
//             Map<String, Object> response = new HashMap<>();
//             response.put("success", true);
//             response.put("message", "Investments updated from daily data successfully");
//             response.put("userId", userId);
//             return ResponseEntity.ok(response);
//         } catch (IllegalArgumentException e) {
//             log.error("Invalid user ID: {}", userId);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", e.getMessage());
//             return ResponseEntity.badRequest().body(errorResponse);
//         } catch (Exception e) {
//             log.error("Error updating investments from daily data for user {}: {}", userId, e.getMessage(), e);
//             Map<String, Object> errorResponse = new HashMap<>();
//             errorResponse.put("success", false);
//             errorResponse.put("error", "Failed to update investments from daily data");
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
//         }
//     }
// }