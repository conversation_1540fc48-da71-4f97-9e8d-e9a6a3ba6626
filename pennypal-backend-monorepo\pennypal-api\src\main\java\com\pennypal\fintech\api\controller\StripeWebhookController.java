package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.pennypal.fintech.entity.AclGroups;
import com.pennypal.fintech.entity.StripePaymentMethod;
import com.pennypal.fintech.entity.StripePaymentIntent;
import com.pennypal.fintech.entity.StripeSubscription;
import com.pennypal.fintech.entity.StripeCustomer;
import com.pennypal.fintech.entity.StripeInvoice;
import com.pennypal.fintech.entity.UsersGroup;
import com.pennypal.fintech.repository.AclGroupsRepository;
import com.pennypal.fintech.repository.StripeCustomerRepository;
import com.pennypal.fintech.repository.StripePaymentMethodRepository;
import com.pennypal.fintech.repository.StripePaymentIntentRepository;
import com.pennypal.fintech.repository.StripeProductRepository;
import com.pennypal.fintech.repository.StripeSubscriptionRepository;
import com.pennypal.fintech.repository.StripeInvoiceRepository;
import com.pennypal.fintech.repository.UserGroupRepository;
import com.pennypal.fintech.service.AppConfigService;
import com.stripe.exception.StripeException;
import com.stripe.model.Event;
import com.stripe.model.EventDataObjectDeserializer;
import com.stripe.model.Invoice;
import com.stripe.model.Subscription;
import com.stripe.net.ApiResource;
import com.stripe.net.Webhook;
import com.stripe.param.InvoiceUpdateParams;
import com.stripe.param.SubscriptionRetrieveParams;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping("/api/v1/stripe-webhooks")
public class StripeWebhookController {

    // @Value("${stripe.webhook.secret}")
    // private String endpointSecret;

    private final AppConfigService appConfigService;

    @Autowired
    private StripeCustomerRepository stripeCustomerRepository;

    @Autowired
    private StripePaymentMethodRepository stripePaymentMethodRepository;

    @Autowired
    private StripePaymentIntentRepository stripePaymentIntentRepository;

    @Autowired
    private StripeSubscriptionRepository stripeSubscriptionRepository;

    @Autowired
    private StripeInvoiceRepository stripeInvoiceRepository;

    @Autowired
    private AclGroupsRepository aclGroupsRepository;

    @Autowired
    private StripeProductRepository stripeProductsRepository;

    @Autowired
    private UserGroupRepository usersGroupRepository;

    public StripeWebhookController(AppConfigService appConfigService,
                                   StripeCustomerRepository stripeCustomerRepository,
                                   StripePaymentMethodRepository paymentMethodRepository,
                                   StripePaymentIntentRepository stripePaymentIntentRepository,
                                   StripeSubscriptionRepository stripeSubscriptionRepository,
                                   StripeInvoiceRepository stripeInvoiceRepository,
                                   AclGroupsRepository aclGroupsRepository,
                                   StripeProductRepository stripeProductsRepository,
                                   UserGroupRepository usersGroupRepository) {
        this.appConfigService = appConfigService;
        this.stripeCustomerRepository = stripeCustomerRepository;
        this.stripePaymentMethodRepository = paymentMethodRepository;
        this.stripePaymentIntentRepository = stripePaymentIntentRepository;
        this.stripeSubscriptionRepository = stripeSubscriptionRepository;
        this.stripeInvoiceRepository = stripeInvoiceRepository;
        this.aclGroupsRepository = aclGroupsRepository;
        this.stripeProductsRepository = stripeProductsRepository;
        this.usersGroupRepository = usersGroupRepository;
    }

    @Autowired
    private StripeWebhookProducer stripeWebhookProducer;

    @PostMapping("/payments")
    public ResponseEntity<String> handleStripeEvent(@RequestBody String payload,
                                                    @RequestHeader(value = "Stripe-Signature", required = false) String sigHeader) {
        
        log.info("Received Stripe event: " + payload);
        Event event;

        try {
            String endpointSecret = appConfigService.getConfig("stripe_webhook_secret");
            if (endpointSecret == null || endpointSecret.isEmpty()) {
                log.error("Stripe webhook secret is not configured");
                return ResponseEntity.status(500).body("Webhook secret not configured");
            }
            if (sigHeader == null || sigHeader.isEmpty()) {
                log.error("Stripe-Signature header is missing");
                return ResponseEntity.status(400).body("Stripe-Signature header is required");
            }
            
            try {
                event = Webhook.constructEvent(payload, sigHeader, endpointSecret);
            } catch (Exception e) {
                log.error("Error constructing Stripe event: {}", e.getMessage());
                return ResponseEntity.status(400).body("Invalid signature");
            }
            log.info("Event type: " + event.getType());
            if ("customer.updated".equals(event.getType()) ||
                "customer.subscription.created".equals(event.getType()) ||
                "customer.subscription.updated".equals(event.getType()) ||
                "customer.subscription.deleted".equals(event.getType()) ||
                "payment_method.attached".equals(event.getType()) ||
                "payment_method.updated".equals(event.getType()) ||
                "payment_intent.succeeded".equals(event.getType()) ||
                "invoice.payment_succeeded".equals(event.getType())
                ) {

                if ("customer.updated".equals(event.getType())) {
                    log.info("Inside customer.updated");
                    EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                    String rawJson = dataObjectDeserializer.getRawJson();
                    JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                    log.info(obj.toString());
                    
                    String customerId = obj.get("id").getAsString();
                    log.info("customerId: " + customerId);

                    Integer userId;
                    if (obj.has("metadata") && !obj.get("metadata").isJsonNull() && 
                        obj.getAsJsonObject("metadata").has("userId")) {
                        userId = obj.getAsJsonObject("metadata").get("userId").getAsInt();
                    } else {
                        userId = stripeCustomerRepository.findByStripeCustomerId(customerId).getUserId();
                    }
                    log.info("userId: " + userId);

                    String email = obj.get("email").getAsString();
                    log.info("email: " + email);
                    String name = obj.get("name").getAsString();
                    log.info("name: " + name);
                    
                    String defaultPaymentMethod = null;
                    if (obj.has("invoice_settings") && obj.get("invoice_settings").isJsonObject()) {
                        JsonObject invoiceSettings = obj.getAsJsonObject("invoice_settings");
                        if (invoiceSettings.has("default_payment_method") && !invoiceSettings.get("default_payment_method").isJsonNull()) {
                            defaultPaymentMethod = invoiceSettings.get("default_payment_method").getAsString();
                        }
                    }
                    log.info("defaultPaymentMethod: " + defaultPaymentMethod);
                    Long stripeBalance = obj.get("balance").getAsLong();

                    // Save to database
                    StripeCustomer stripeCustomer = stripeCustomerRepository.findByUserId(userId);
                    stripeCustomer.setStripeCustomerId(customerId);
                    stripeCustomer.setUpdatedAt(LocalDateTime.now());
                    stripeCustomer.setEmail(email);
                    stripeCustomer.setName(name);
                    stripeCustomer.setDefaultPaymentMethod(defaultPaymentMethod);
                    stripeCustomer.setStripeBalance(stripeBalance);
                    stripeCustomerRepository.save(stripeCustomer);
                }

                if ("payment_method.attached".equals(event.getType()) ||
                    "payment_method.updated".equals(event.getType()) ||
                    "payment_method.detached".equals(event.getType())) {
                    EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                    String rawJson = dataObjectDeserializer.getRawJson();
                    JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                    log.info(obj.toString());
                    
                    String customerId = obj.get("customer").getAsString();

                    Integer userId;
                    if (obj.has("metadata") && !obj.get("metadata").isJsonNull() && 
                        obj.getAsJsonObject("metadata").has("userId")) {
                        userId = obj.getAsJsonObject("metadata").get("userId").getAsInt();
                    } else {
                        userId = stripeCustomerRepository.findByStripeCustomerId(customerId).getUserId();
                    }
                    
                    String paymentMethodId = obj.get("id").getAsString();
                    String paymentMethodType = obj.get("type").getAsString();
                    String fingerprint = obj.get("card").getAsJsonObject().get("fingerprint").getAsString();
                    String funding = obj.get("card").getAsJsonObject().get("funding").getAsString();
                    String last4 = obj.get("card").getAsJsonObject().get("last4").getAsString();
                    String brand = obj.get("card").getAsJsonObject().get("brand").getAsString();
                    Integer expMonth = obj.get("card").getAsJsonObject().get("exp_month").getAsInt();
                    Integer expYear = obj.get("card").getAsJsonObject().get("exp_year").getAsInt();

                    log.info("Payment method attached/updated: " + paymentMethodId);
                    log.info("User ID: " + userId);
                    log.info("Customer ID: " + customerId);
                    log.info("Payment method type: " + paymentMethodType);
                    log.info("Fingerprint: " + fingerprint);
                    log.info("Funding: " + funding);
                    log.info("Last 4: " + last4);
                    log.info("Brand: " + brand);
                    log.info("Exp month: " + expMonth);
                    log.info("Exp year: " + expYear);

                    // Save to database
                    if (stripePaymentMethodRepository.existsByUserIdAndPaymentMethodIdAndPaymentMethodType(userId, paymentMethodId, paymentMethodType)) {
                        stripePaymentMethodRepository.deleteByUserIdAndPaymentMethodId(userId, paymentMethodId);
                    }
                    stripePaymentMethodRepository.save(new StripePaymentMethod(
                        userId, customerId, paymentMethodId, paymentMethodType,
                        fingerprint, funding, last4, brand, expMonth, expYear));
                }

                if ("payment_intent.succeeded".equals(event.getType())) {
                    EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                    String rawJson = dataObjectDeserializer.getRawJson();
                    JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                    log.info(obj.toString());
                    
                    String customerId = obj.has("customer") && !obj.get("customer").isJsonNull()
                        ? obj.get("customer").getAsString() : null;

                    Integer userId;
                    if (obj.has("metadata") && !obj.get("metadata").isJsonNull() && 
                        obj.getAsJsonObject("metadata").has("userId")) {
                        userId = obj.getAsJsonObject("metadata").get("userId").getAsInt();
                    } else {
                        userId = stripeCustomerRepository.findByStripeCustomerId(customerId).getUserId();
                    }
                    
                    String paymentIntentId = obj.get("id").getAsString();
                    String paymentMethod = obj.has("payment_method") && !obj.get("payment_method").isJsonNull()
                        ? obj.get("payment_method").getAsString() : null;
                    long amount = obj.get("amount").getAsLong();
                    String currency = obj.get("currency").getAsString();
                    long createdAt = obj.get("created").getAsLong();
                    String status = obj.get("status").getAsString();
                    String description = obj.has("description") && !obj.get("description").isJsonNull()
                        ? obj.get("description").getAsString() : null;
                    LocalDateTime createdAtDate = Instant.ofEpochSecond(createdAt).atZone(ZoneId.systemDefault()).toLocalDateTime();

                    log.info("Payment intent succeeded: " + paymentIntentId);
                    log.info("User ID: " + userId);
                    log.info("Customer ID: " + customerId);
                    log.info("Payment method: " + paymentMethod);
                    log.info("Amount: " + amount);
                    log.info("Currency: " + currency);
                    log.info("Created at: " + createdAtDate);
                    log.info("Status: " + status);
                    log.info("Description: " + description);

                    // Save to database
                    stripePaymentIntentRepository.save(new StripePaymentIntent(
                        userId, customerId, paymentIntentId, paymentMethod,
                        amount, currency, createdAtDate, status, description));
                }

                if ("customer.subscription.created".equals(event.getType()) ||
                    "customer.subscription.updated".equals(event.getType()) ||
                    "customer.subscription.deleted".equals(event.getType())) {
                    EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                    String rawJson = dataObjectDeserializer.getRawJson();
                    JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                    log.info(obj.toString());
                    
                    String customerId = obj.get("customer").getAsString();

                    Integer userId;
                    if (obj.has("metadata") && !obj.get("metadata").isJsonNull() && 
                        obj.getAsJsonObject("metadata").has("userId")) {
                        userId = obj.getAsJsonObject("metadata").get("userId").getAsInt();
                    } else {
                        userId = stripeCustomerRepository.findByStripeCustomerId(customerId).getUserId();
                    }

                    String subscriptionId = obj.get("id").getAsString();
                    long createdAt = obj.get("created").getAsLong();
                    long startedAt = obj.get("start_date").getAsLong();
                    long billingCycleAnchor = obj.get("billing_cycle_anchor").getAsLong();
                    Long cancelAt = obj.has("cancel_at") && !obj.get("cancel_at").isJsonNull() ? obj.get("cancel_at").getAsLong() : null;
                    Long canceledAt = obj.has("canceled_at") && !obj.get("canceled_at").isJsonNull() ? obj.get("canceled_at").getAsLong() : null;

                    JsonObject plan = obj.getAsJsonObject("plan");
                    String priceId = plan.get("id").getAsString();
                    long amount = plan.get("amount").getAsLong();
                    String currency = plan.get("currency").getAsString();
                    String frequency = plan.get("interval").getAsString();
                    String status = obj.get("status").getAsString();

                    JsonArray itemsArray = obj.getAsJsonObject("items").getAsJsonArray("data");
                    long currentPeriodStart = 0;
                    long currentPeriodEnd = 0;
                    if (itemsArray.size() > 0) {
                        JsonObject firstItem = itemsArray.get(0).getAsJsonObject();
                        currentPeriodStart = firstItem.get("current_period_start").getAsLong();
                        currentPeriodEnd = firstItem.get("current_period_end").getAsLong();
                    }

                    // Convert to LocalDateTime
                    LocalDateTime createdAtDate = Instant.ofEpochSecond(createdAt).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime startedAtDate = Instant.ofEpochSecond(startedAt).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime billingCycleAnchorDate = Instant.ofEpochSecond(billingCycleAnchor).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime cancelAtDate = cancelAt != null ? Instant.ofEpochSecond(cancelAt).atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
                    LocalDateTime canceledAtDate = canceledAt != null ? Instant.ofEpochSecond(canceledAt).atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
                    LocalDateTime currentPeriodStartDate = Instant.ofEpochSecond(currentPeriodStart).atZone(ZoneId.systemDefault()).toLocalDateTime();
                    LocalDateTime currentPeriodEndDate = Instant.ofEpochSecond(currentPeriodEnd).atZone(ZoneId.systemDefault()).toLocalDateTime();

                    Subscription subscription = Subscription.retrieve(
                        subscriptionId,
                        SubscriptionRetrieveParams.builder()
                            .addExpand("default_payment_method")
                            .build(),
                        null
                    );

                    String defaultPaymentMethod = subscription.getDefaultPaymentMethod() != null
                        ? subscription.getDefaultPaymentMethod() : null;

                    log.info("Subscription created: " + subscriptionId);
                    log.info("User ID: " + userId);
                    log.info("Customer ID: " + customerId);
                    log.info("Price ID: " + priceId);
                    log.info("Amount: " + amount);
                    log.info("Frequency: " + frequency);
                    log.info("Status: " + status);
                    log.info("Created at: " + createdAtDate);
                    log.info("Start date: " + startedAtDate);
                    log.info("Billing cycle anchor: " + billingCycleAnchorDate);
                    log.info("Cancel at: " + cancelAtDate);
                    log.info("Canceled at: " + canceledAtDate);
                    log.info("Current period start: " + currentPeriodStartDate);
                    log.info("Current period end: " + currentPeriodEndDate);
                    log.info("Default payment method: " + defaultPaymentMethod);

                    StripeSubscription stripesub = stripeSubscriptionRepository.findByUserId(userId);
                    log.info("stripesub: " + stripesub);
                    
                    if (stripesub != null) {
                        log.info("Subscription found: " + stripesub);
                        stripesub.setSubscriptionId(subscriptionId);
                        stripesub.setPriceId(priceId);
                        stripesub.setAmount(amount);
                        stripesub.setCurrency(currency);
                        stripesub.setFrequency(frequency);
                        stripesub.setStatus(status);
                        stripesub.setCreatedAt(createdAtDate);
                        stripesub.setStartDate(startedAtDate);
                        stripesub.setBillingCycleAnchor(billingCycleAnchorDate);
                        stripesub.setCancelAt(cancelAtDate);
                        stripesub.setCanceledAt(canceledAtDate);
                        stripesub.setCurrentPeriodStart(currentPeriodStartDate);
                        stripesub.setCurrentPeriodEnd(currentPeriodEndDate);
                        stripesub.setDefaultPaymentMethod(defaultPaymentMethod);
                        stripeSubscriptionRepository.save(stripesub);
                    } else {
                        log.info("Subscription not found for customer: " + customerId);
                        stripeSubscriptionRepository.save(new StripeSubscription(
                            userId, customerId, subscriptionId, priceId, amount,
                            currency, frequency, status, createdAtDate, startedAtDate,
                            billingCycleAnchorDate, cancelAtDate, canceledAtDate,
                            currentPeriodStartDate, currentPeriodEndDate, defaultPaymentMethod));
                    }

                    // Add/update users_group
                    if ("customer.subscription.created".equals(event.getType()) ||
                        "customer.subscription.updated".equals(event.getType())) {
                        String productName = stripeProductsRepository.findByPriceId(priceId).getProductName();
                        if (productName.contains("Standard")) {
                            UsersGroup usersGroup = usersGroupRepository.findByUserId(userId).orElse(new UsersGroup());
                            usersGroup.setUserId(userId);
                            usersGroup.setGroup(aclGroupsRepository.findByName(AclGroups.GroupName.STANDARD));
                            usersGroup.setStatus("ACTIVE");
                            usersGroup.setExpiresAt(currentPeriodEndDate);
                            usersGroupRepository.save(usersGroup);
                        } else if (productName.contains("Premium")) {
                            UsersGroup usersGroup = usersGroupRepository.findByUserId(userId).orElse(new UsersGroup());
                            usersGroup.setUserId(userId);
                            usersGroup.setGroup(aclGroupsRepository.findByName(AclGroups.GroupName.PREMIUM));
                            usersGroup.setStatus("ACTIVE");
                            usersGroup.setExpiresAt(currentPeriodEndDate);
                            usersGroupRepository.save(usersGroup);
                        }
                    }

                    if ("customer.subscription.deleted".equals(event.getType())) {
                        UsersGroup usersGroup = usersGroupRepository.findByUserId(userId).orElse(new UsersGroup());
                        usersGroup.setUserId(userId);
                        usersGroup.setGroup(aclGroupsRepository.findByName(AclGroups.GroupName.FREE));
                        usersGroup.setStatus("INACTIVE");
                        usersGroup.setExpiresAt(null);
                        usersGroupRepository.save(usersGroup);
                    }
                }

                if ("invoice.payment_succeeded".equals(event.getType())) {
                    EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                    String rawJson = dataObjectDeserializer.getRawJson();
                    JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                    log.info(obj.toString());

                    String customerId = obj.get("customer").getAsString();

                    Integer userId;
                    if (obj.has("metadata") && !obj.get("metadata").isJsonNull() && 
                        obj.getAsJsonObject("metadata").has("userId")) {
                        userId = obj.getAsJsonObject("metadata").get("userId").getAsInt();
                    } else {
                        userId = stripeCustomerRepository.findByStripeCustomerId(customerId).getUserId();
                    }
                    
                    // Getting subscription ID
                    String subscriptionId = obj.has("subscription") && !obj.get("subscription").isJsonNull()
                        ? obj.get("subscription").getAsString()
                        : null;
                    if ((subscriptionId == null || subscriptionId.isEmpty())
                        && obj.has("lines")
                        && obj.getAsJsonObject("lines").has("data")) {

                        JsonArray linesData = obj.getAsJsonObject("lines").getAsJsonArray("data");

                        if (!linesData.isEmpty()) {
                            JsonObject firstLine = linesData.get(0).getAsJsonObject();

                            if (firstLine.has("parent") && firstLine.get("parent").isJsonObject()) {
                                JsonObject parent = firstLine.getAsJsonObject("parent");

                                if (parent.has("subscription_item_details") && parent.get("subscription_item_details").isJsonObject()) {
                                    JsonObject details = parent.getAsJsonObject("subscription_item_details");

                                    if (details.has("subscription") && !details.get("subscription").isJsonNull()) {
                                        subscriptionId = details.get("subscription").getAsString();
                                    }
                                }
                            }
                        }
                    }

                    String invoiceId = obj.get("id").getAsString();
                    long amountPaid = obj.get("amount_paid").getAsLong();
                    String currency = obj.get("currency").getAsString();
                    long createdAt = obj.get("created").getAsLong();
                    String billingReason = obj.get("billing_reason").getAsString();
                    String collectionMethod = obj.get("collection_method").getAsString();

                    String customerEmail = obj.has("customer_email") && !obj.get("customer_email").isJsonNull()
                        ? obj.get("customer_email").getAsString()
                        : null;
                    String customerName = obj.has("customer_name") && !obj.get("customer_name").isJsonNull()
                            ? obj.get("customer_name").getAsString()
                            : null;
                    String customerPhone = obj.has("customer_phone") && !obj.get("customer_phone").isJsonNull()
                            ? obj.get("customer_phone").getAsString()
                            : null;
                    String invoiceUrl = obj.get("hosted_invoice_url").getAsString();
                    String invoicePdf = obj.get("invoice_pdf").getAsString();

                    LocalDateTime createdAtDate = Instant.ofEpochSecond(createdAt)
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();

                    log.info("Invoice payment succeeded: " + invoiceId);
                    log.info("User ID: " + userId);
                    log.info("Customer ID: " + customerId);
                    log.info("Subscription ID: " + subscriptionId);
                    log.info("Amount paid: " + amountPaid);
                    log.info("Created at: " + createdAtDate);
                    log.info("Billing reason: " + billingReason);
                    log.info("Collection method: " + collectionMethod);
                    log.info("Customer email: " + customerEmail);
                    log.info("Customer name: " + customerName);
                    log.info("Customer phone: " + customerPhone);
                    log.info("Invoice URL: " + invoiceUrl);
                    log.info("Invoice PDF: " + invoicePdf);

                    // Save to database
                    stripeInvoiceRepository.save(new StripeInvoice(
                        userId, customerId, subscriptionId, invoiceId, amountPaid,
                        currency, billingReason, collectionMethod, customerEmail, customerName,
                        customerPhone, invoiceUrl, invoicePdf, createdAtDate));
                }
            }

            // Update upcoming invoice when trial ends for new users with annual subscription
            if ("invoice.upcoming".equals(event.getType())) {
                log.info("Inside invoice.upcoming");
                EventDataObjectDeserializer dataObjectDeserializer = event.getDataObjectDeserializer();
                String rawJson = dataObjectDeserializer.getRawJson();
                JsonObject obj = ApiResource.GSON.fromJson(rawJson, JsonObject.class);
                log.info(obj.toString());
                
                if (obj != null && obj.has("id")) {
                    try {
                        String invoiceId = obj.get("id").getAsString();
                        log.info("invoiceId: " + invoiceId);
                        InvoiceUpdateParams params = InvoiceUpdateParams.builder()
                            .setDiscounts(
                                java.util.List.of(
                                    InvoiceUpdateParams.Discount.builder()
                                        .setCoupon("Xv1wam6J")
                                        .build()
                                )
                            )
                            .build();

                        Invoice updatedInvoice = Invoice.retrieve(invoiceId);
                        log.info("Updated invoice: " + updatedInvoice);
                        updatedInvoice.update(params);
                    } catch (StripeException e) {
                        e.printStackTrace();
                        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to apply coupon");
                    }
                }
            }

            // log.info("Sending to Kafka");
            // Send payload to Kafka
            // stripeWebhookProducer.publishEvent(payload);
            // log.info("Stripe event forwarded to Kafka");
            // Send response to Stripe
            return ResponseEntity.ok("Stripe event forwarded to Kafka");
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Invalid signature");
        }
    }

    /*
	
	@PostMapping("/payments")
    public ResponseEntity<String> handleStripeEvent(@RequestBody String payload,
                                                    @RequestHeader(value = "Stripe-Signature", required = false) String sigHeader) {
        
        System.out.println("Received Stripe event: " + payload);
        Event event;

        try {
            event = Webhook.constructEvent(payload, sigHeader, endpointSecret);

            log.info("Sending to Kafka");
            // Send payload to Kafka
            stripeWebhookProducer.publishEvent(payload);
            log.info("Stripe event forwarded to Kafka");
            // Send response to Stripe
            return ResponseEntity.ok("Stripe event forwarded to Kafka");
            
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Invalid signature");
        }
    } */
}