package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.ReceiptConsolidate;

import io.lettuce.core.dynamic.annotation.Param;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface ReceiptConsolidateRepository extends JpaRepository<ReceiptConsolidate, Integer> {

    @Query("SELECT r FROM ReceiptConsolidate r WHERE r.userId = :userId AND r.receiptItem = :receiptItem")
Optional<ReceiptConsolidate> findByUserIdAndReceiptItem(@Param("userId") Long userId, @Param("receiptItem") String receiptItem);

}
