package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.pennypal.fintech.dto.CategoryDto;
import com.pennypal.fintech.dto.CategoryMappingDto;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.service.CategoryService;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/category")
//@CrossOrigin(origins = "*")
public class CategoryController {

    @Autowired
    private final CategoryService categoryService;

 
    public CategoryController(CategoryService categoryService) {
        this.categoryService = categoryService;
    }

   
    /**
     * Get category by ID
     */
    @GetMapping("/{id}")
    public ResponseEntity<CategoryDto> getCategoryById(@PathVariable Integer id) {
        try {
            Category category = categoryService.getCategoryById(id);
            CategoryDto dto = convertToDto(category);
            return ResponseEntity.ok(dto);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    /**
     * Get all categories
     */
    @GetMapping("/all")
    public ResponseEntity<List<CategoryDto>> getAllCategories() {
        List<Category> categories = categoryService.getAllCategories();
        List<CategoryDto> dtos = categories.stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
        return ResponseEntity.ok(dtos);
    }


    @PutMapping("/{id}/icon")
    public ResponseEntity<Void> updateCategoryIcon(@PathVariable Integer id, @RequestParam String iconKey) {
        try {
            categoryService.updateCategoryIconKey(id, iconKey);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (RuntimeException e) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    // Helper method to map Category to CategoryDto
    private CategoryDto convertToDto(Category category) {
        CategoryDto dto = new CategoryDto();
        dto.setId(category.getId());
        dto.setCategory(category.getCategory());
        dto.setCategoryIconKey(category.getCategoryIconKey());
        // Optionally map subcategories if needed:
        // dto.setSubCategories( ... );
        return dto;
    }

    @GetMapping("/byCategory/{category}")
    public ResponseEntity<List<SubCategory>> getAllCategories(@PathVariable String category) {
        System.out.println("category is "+category);
        List<SubCategory> categories = categoryService.getAllCategoriesByCategory(category);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/sync/plaid-pfc")
    public String syncPlaidPFC() {
        try {
            categoryService.processAndStorePlaidPFC();
            return "Categories synced successfully!";
        } catch (Exception e) {
            return "Error syncing categories: " + e.getMessage();
        }
    }

    @GetMapping("/get/plaid-pfc")
    public List<CategoryMappingDto> getPlaidPFC() {
        return categoryService.getAllMappings();
    }



}