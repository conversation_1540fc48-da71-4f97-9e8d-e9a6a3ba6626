package com.pennypal.fintech.dto;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * DTO for Plaid investment holdings data
 */
public class PlaidHoldingDto {
    
    @JsonProperty("security_id")
    private String securityId;
    
    @JsonProperty("security_name")
    private String securityName;
    
    @JsonProperty("ticker")
    private String ticker;
    
    @JsonProperty("security_type")
    private String securityType;
    
    @JsonProperty("quantity")
    private Double quantity;
    
    @JsonProperty("value")
    private Double value;
    
    @JsonProperty("cost_basis")
    private Double costBasis;
    
    @JsonProperty("current_price")
    private Double currentPrice;
    
    @JsonProperty("average_purchase_price")
    private Double averagePurchasePrice;
    
    @JsonProperty("currency_code")
    private String currencyCode;
    
    @JsonProperty("gain_loss")
    private Double gainLoss;
    
    @JsonProperty("gain_loss_percentage")
    private Double gainLossPercentage;
    
    @JsonProperty("last_updated")
    private LocalDateTime lastUpdated;
    
    @JsonProperty("account_id")
    private String accountId;
    
    @JsonProperty("investment_id")
    private String investmentId;

    // Default constructor
    public PlaidHoldingDto() {
    }

    // Constructor with essential fields
    public PlaidHoldingDto(String securityId, String securityName, String ticker, 
                          Double quantity, Double value, Double costBasis) {
        this.securityId = securityId;
        this.securityName = securityName;
        this.ticker = ticker;
        this.quantity = quantity;
        this.value = value;
        this.costBasis = costBasis;
    }

    // Getters and Setters
    public String getSecurityId() {
        return securityId;
    }

    public void setSecurityId(String securityId) {
        this.securityId = securityId;
    }

    public String getSecurityName() {
        return securityName;
    }

    public void setSecurityName(String securityName) {
        this.securityName = securityName;
    }

    public String getTicker() {
        return ticker;
    }

    public void setTicker(String ticker) {
        this.ticker = ticker;
    }

    public String getSecurityType() {
        return securityType;
    }

    public void setSecurityType(String securityType) {
        this.securityType = securityType;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public Double getCostBasis() {
        return costBasis;
    }

    public void setCostBasis(Double costBasis) {
        this.costBasis = costBasis;
    }

    public Double getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(Double currentPrice) {
        this.currentPrice = currentPrice;
    }

    public Double getAveragePurchasePrice() {
        return averagePurchasePrice;
    }

    public void setAveragePurchasePrice(Double averagePurchasePrice) {
        this.averagePurchasePrice = averagePurchasePrice;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public Double getGainLoss() {
        return gainLoss;
    }

    public void setGainLoss(Double gainLoss) {
        this.gainLoss = gainLoss;
    }

    public Double getGainLossPercentage() {
        return gainLossPercentage;
    }

    public void setGainLossPercentage(Double gainLossPercentage) {
        this.gainLossPercentage = gainLossPercentage;
    }

    public LocalDateTime getLastUpdated() {
        return lastUpdated;
    }

    public void setLastUpdated(LocalDateTime lastUpdated) {
        this.lastUpdated = lastUpdated;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getInvestmentId() {
        return investmentId;
    }

    public void setInvestmentId(String investmentId) {
        this.investmentId = investmentId;
    }

    // Utility methods
    
    /**
     * Calculate gain/loss based on current value and cost basis
     */
    public void calculateGainLoss() {
        if (this.value != null && this.costBasis != null) {
            this.gainLoss = this.value - this.costBasis;
            
            if (this.costBasis > 0) {
                this.gainLossPercentage = (this.gainLoss / this.costBasis) * 100;
            }
        }
    }
    
    /**
     * Check if this is a profitable investment
     */
    public boolean isProfitable() {
        return this.gainLoss != null && this.gainLoss > 0;
    }
    
    /**
     * Get formatted gain/loss percentage as string
     */
    public String getFormattedGainLossPercentage() {
        if (this.gainLossPercentage == null) {
            return "N/A";
        }
        return String.format("%.2f%%", this.gainLossPercentage);
    }

    @Override
    public String toString() {
        return "PlaidHoldingDto{" +
                "securityId='" + securityId + '\'' +
                ", securityName='" + securityName + '\'' +
                ", ticker='" + ticker + '\'' +
                ", securityType='" + securityType + '\'' +
                ", quantity=" + quantity +
                ", value=" + value +
                ", costBasis=" + costBasis +
                ", currentPrice=" + currentPrice +
                ", averagePurchasePrice=" + averagePurchasePrice +
                ", currencyCode='" + currencyCode + '\'' +
                ", gainLoss=" + gainLoss +
                ", gainLossPercentage=" + gainLossPercentage +
                ", lastUpdated=" + lastUpdated +
                ", accountId='" + accountId + '\'' +
                ", investmentId='" + investmentId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PlaidHoldingDto that = (PlaidHoldingDto) o;

        if (securityId != null ? !securityId.equals(that.securityId) : that.securityId != null) return false;
        return accountId != null ? accountId.equals(that.accountId) : that.accountId == null;
    }

    @Override
    public int hashCode() {
        int result = securityId != null ? securityId.hashCode() : 0;
        result = 31 * result + (accountId != null ? accountId.hashCode() : 0);
        return result;
    }
}