package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.FinicityInvestmentService;

import java.time.LocalDateTime;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import lombok.extern.slf4j.Slf4j;
@RestController

@RequestMapping("/api/v1/finicity/investment")
@Slf4j
public class FinicityInvestmentController {

    @Autowired
    private FinicityInvestmentService finicityInvestmentService;
  @GetMapping("/debug/all-accounts/{userId}")
    public ResponseEntity<?> debugGetAllAccounts(@PathVariable Integer userId) {
        return finicityInvestmentService.debugGetAllAccounts(userId);
    }
    /**
     * Get investment accounts for a user
     */
   @GetMapping("/accounts/{userId}")
    public ResponseEntity<?> getInvestmentAccounts(@PathVariable Integer userId) {
        try {
            return finicityInvestmentService.getInvestmentAccounts(userId);
        } catch (Exception e) {
            log.error("Controller error getting investment accounts for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(500).body(Map.of(
                "error", "Failed to get investment accounts",
                "message", e.getMessage(),
                "userId", userId
            ));
        }
    }
    /**
     * Get holdings for a specific investment account
     */
    @GetMapping("/accounts/{userId}/holdings/{accountId}")
    public ResponseEntity<?> getAccountHoldings(
            @PathVariable Integer userId,
            @PathVariable String accountId) {
        return finicityInvestmentService.getAccountHoldings(userId, accountId);
    }

    /**
     * Sync all investment holdings for a user
     */
    @PostMapping("/sync-holdings/{userId}")
    public ResponseEntity<?> syncInvestmentHoldings(@PathVariable Integer userId) {
        return finicityInvestmentService.syncInvestmentHoldings(userId);
    }

    /**
     * Get investment transactions for an account
     */
    @GetMapping("/accounts/{userId}/transactions/{accountId}")
    public ResponseEntity<?> getInvestmentTransactions(
            @PathVariable Integer userId,
            @PathVariable String accountId) {
        return finicityInvestmentService.getInvestmentTransactions(userId, accountId);
    }

    /**
     * Get user's investment portfolio summary
     */
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<?> getInvestmentPortfolio(@PathVariable Integer userId) {
        return finicityInvestmentService.getInvestmentPortfolio(userId);
    }
   /**
 * Get all investment holdings for a user
 * This endpoint fetches all investment accounts and their holdings
 */
@GetMapping("/holdings/{userId}")
public ResponseEntity<?> getAllUserHoldings(@PathVariable Integer userId) {
    log.info("Controller: Getting all investment holdings for userId: {}", userId);
    
    // Validate userId
    if (userId == null || userId <= 0) {
        log.error("Invalid userId provided: {}", userId);
        return ResponseEntity.badRequest().body(Map.of(
            "error", "Invalid user ID",
            "message", "User ID must be a positive integer",
            "userId", userId
        ));
    }
    
    try {
        long startTime = System.currentTimeMillis();
        ResponseEntity<?> response = finicityInvestmentService.getAllUserHoldings(userId);
        long endTime = System.currentTimeMillis();
        
        log.info("Controller: Successfully processed holdings request for user {} in {}ms", 
            userId, (endTime - startTime));
        
        return response;
        
    } catch (IllegalArgumentException e) {
        log.error("Controller: Invalid argument for user {}: {}", userId, e.getMessage());
        return ResponseEntity.badRequest().body(Map.of(
            "error", "Invalid request parameters",
            "message", e.getMessage(),
            "userId", userId
        ));
        
    } catch (SecurityException e) {
        log.error("Controller: Security error for user {}: {}", userId, e.getMessage());
        return ResponseEntity.status(403).body(Map.of(
            "error", "Access denied",
            "message", "You are not authorized to access this resource",
            "userId", userId
        ));
        
    } catch (Exception e) {
        log.error("Controller: Unexpected error getting all holdings for user {}: {}", 
            userId, e.getMessage(), e);
        return ResponseEntity.status(500).body(Map.of(
            "error", "Internal server error",
            "message", "An unexpected error occurred while retrieving investment holdings",
            "userId", userId,
            "timestamp", LocalDateTime.now(),
            "requestId", java.util.UUID.randomUUID().toString()
        ));
    }
}
// Add to your FinicityInvestmentController
@GetMapping("/debug/holdings/{userId}/{accountId}")
public ResponseEntity<?> debugGetAccountHoldings(
        @PathVariable Integer userId,
        @PathVariable String accountId) {
    return finicityInvestmentService.debugGetAccountHoldings(userId, accountId);
}
}
