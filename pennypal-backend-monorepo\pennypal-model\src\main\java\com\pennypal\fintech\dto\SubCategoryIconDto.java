package com.pennypal.fintech.dto;

public class SubCategoryIconDto {
    private Integer subCategoryId;
    private String subCategoryName;
    private String base64Icon; // ✅ Replaces byte[]

    public SubCategoryIconDto(Integer subCategoryId, String subCategoryName, String base64Icon) {
        this.subCategoryId = subCategoryId;
        this.subCategoryName = subCategoryName;
        this.base64Icon = base64Icon;
    }

    public Integer getSubCategoryId() {
        return subCategoryId;
    }

    public String getSubCategoryName() {
        return subCategoryName;
    }

    public String getBase64Icon() {
        return base64Icon;
    }
}
