package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Entity
@Table(name = "budget_rules")
public class BudgetRules {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user_id", nullable = false)
    private Integer userId;

    @Column(name = "rule_type", nullable = false)
    private String ruleType;

    @Column(name = "from_category_id", nullable = false)
    private Integer fromCategoryId;

    @Column(name = "from_sub_category_id")
    private Integer fromSubCategoryId;

    @Column(name = "to_category_id", nullable = false)
    private Integer toCategoryId;

    @Column(name = "to_sub_category_id")
    private Integer toSubCategoryId;

    @Enumerated(EnumType.STRING)
    @Column(name = "condition_type", nullable = false)
    private ConditionType conditionType;

    @Column(name = "threshold_amount", precision = 38, scale = 2)
    private BigDecimal thresholdAmount;

    @Column(name = "threshold_percentage", precision = 5, scale = 2)
    private BigDecimal thresholdPercentage;

    @Column(name = "transfer_day")
    private Integer transferDay;

    @Column(name = "merchant_name_pattern")
    private String merchantNamePattern;

    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "max_transfer_amount", precision = 38, scale = 2)
    private BigDecimal maxTransferAmount;

    @Column(name = "min_transfer_amount", precision = 38, scale = 2)
    private BigDecimal minTransferAmount;

    @Column(name = "max_transfer_percent", precision = 5, scale = 2)
    private BigDecimal maxTransferPercent;

    @Column(name = "min_transfer_percent", precision = 5, scale = 2)
    private BigDecimal minTransferPercent;

    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "last_executed_at")
    private LocalDateTime lastExecutedAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "execution_frequency")
    private ExecutionFrequency executionFrequency;

    @Column(name = "renamed_merchant")
    private String renamedMerchant;

    @Column(name = "cascade_flag")
    private Boolean cascadeFlag = false;

    @Column(name = "merchant_match_regex")
    private Boolean merchantMatchRegex = true;

    @Column(name = "amount_type")
    private String amountType;

    @Column(name = "amount_match")
    private String amountMatch;

    @Column(name = "account_id")
    private Integer accountId;

    @Column(name = "hide_transactions_flag")
    private Boolean hideTransactionsFlag = false;

    @Column(name = "tags")
    private String tags;

    @Column(name = "goal")
    private String goal;

    @Column(name = "from_cat_name")
    private String fromCatName;

    @Column(name = "to_cat_name")
    private String toCatName;

    @Column(name = "account_name")
    private String accountName;

    @Column(name = "to_custom_sub_category_id")
    private Integer toCustomSubCategoryId;

    public enum ConditionType {
        THRESHOLD, REMAINING, PERCENTAGE, DATE, MERCHANT, CUSTOM
    }

    public enum ExecutionFrequency {
        DAILY, WEEKLY, MONTHLY, ON_CHANGE
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}