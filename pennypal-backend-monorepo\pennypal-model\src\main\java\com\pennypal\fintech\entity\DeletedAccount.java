package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Entity
@Table(name = "deleted_accounts")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeletedAccount {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    
    @Column(name = "original_user_id", nullable = false)
    private Integer originalUserId;
    
    @Column(name = "email_id", nullable = false)
    private String emailId;
    
    @Column(name = "phone_number")
    private String phoneNumber;
    
    @Column(name = "name")
    private String name;
    
    @Column(name = "deletion_reason", columnDefinition = "TEXT")
    private String deletionReason;
    
    @Column(name = "deleted_at", nullable = false)
    private LocalDateTime deletedAt;
    
    @Column(name = "deleted_by")
    private Integer deletedBy; // Admin user ID if deleted by admin
    
    @PrePersist
    protected void onCreate() {
        deletedAt = LocalDateTime.now();
    }
}
