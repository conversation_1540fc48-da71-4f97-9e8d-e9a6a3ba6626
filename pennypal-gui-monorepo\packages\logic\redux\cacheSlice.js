import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Icons data from api/icons/list
  icons: [],
  iconsLoaded: false,
  iconsLoading: false,
  iconsError: null,
  iconMap: {},

  // Subcategory icons from api/v1/subCategory/icons  
  subCategoryIcons: [],
  subCategoryIconsLoaded: false,
  subCategoryIconsLoading: false,
  subCategoryIconsError: null,
  subCategoryIconMap: {},

  // Categories from api/v1/category/all
  categories: [],
  categoriesLoaded: false,
  categoriesLoading: false,
  categoriesError: null,

  // Subcategories from api/v1/subCategory/all
  subcategories: [],
  subcategoriesLoaded: false,
  subcategoriesLoading: false,
  subcategoriesError: null,

  // Transactions from api/v1/transaction/transactions/user/{userId}
  transactions: [],
  transactionsLoaded: false,
  transactionsLoading: false,
  transactionsError: null,
  transactionsPagination: { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 },

  // Recurring transactions from api/v1/transaction/recurring/fetch2/{userId}
  recurringTransactions: [],
  recurringTransactionsLoaded: false,
  recurringTransactionsLoading: false,
  recurringTransactionsError: null,

  // Future recurring transactions from api/v1/transaction/recurring/fetch_future/{userId}
  futureRecurringTransactions: [],
  futureRecurringTransactionsLoaded: false,
  futureRecurringTransactionsLoading: false,
  futureRecurringTransactionsError: null,

  // Budget summary from api/v1/budget/summary_by_month/{userId}/{year}/{month}
  budgetSummary: null,
  budgetSummaryLoaded: false,
  budgetSummaryLoading: false,
  budgetSummaryError: null,
  budgetSummaryParams: { userId: null, year: null, month: null },

  // Budget data from api/v1/budget/user/{userId}/month
  budgetData: [],
  budgetDataLoaded: false,
  budgetDataLoading: false,
  budgetDataError: null,
  budgetDataParams: { userId: null },

  // Transaction summary from api/v1/transaction/summary/user/{userId}
  transactionSummary: null,
  transactionSummaryLoaded: false,
  transactionSummaryLoading: false,
  transactionSummaryError: null,
  transactionSummaryParams: { userId: null },

  // Hidden transactions from api/v1/transaction/hidden/user/{userId}
  hiddenTransactions: [],
  hiddenTransactionsLoaded: false,
  hiddenTransactionsLoading: false,
  hiddenTransactionsError: null,
  hiddenTransactionsParams: { userId: null },

  // Reconcile data from api/v1/reconcile/all
  reconcileData: [],
  reconcileDataLoaded: false,
  reconcileDataLoading: false,
  reconcileDataError: null,

  // Receipt transaction IDs from api/receipts/getReceiptTransactionIds
  receiptTransactionIds: [],
  receiptTransactionIdsLoaded: false,
  receiptTransactionIdsLoading: false,
  receiptTransactionIdsError: null,

  // Receipt items from api/v1/receipt-items/all
  receiptItems: [],
  receiptItemsLoaded: false,
  receiptItemsLoading: false,
  receiptItemsError: null,

  // Receipt summary from api/v1/receipt/summary
  receiptSummary: null,
  receiptSummaryLoaded: false,
  receiptSummaryLoading: false,
  receiptSummaryError: null,

  // User receipts from api/receipts/user/{userId}
  userReceipts: [],
  userReceiptsLoaded: false,
  userReceiptsLoading: false,
  userReceiptsError: null,
  userReceiptsParams: { userId: null },

  // Chatbot history from api/v1/chatbot/history/{userId}
  chatbotHistory: [],
  chatbotHistoryLoaded: false,
  chatbotHistoryLoading: false,
  chatbotHistoryError: null,
  chatbotHistoryParams: { userId: null },

  // Distinct subcategories from api/v1/subCategory/byUser/distinct/{userId}
  distinctSubcategories: [],
  distinctSubcategoriesLoaded: false,
  distinctSubcategoriesLoading: false,
  distinctSubcategoriesError: null,
  distinctSubcategoriesParams: { userId: null },

  // Account IDs from api/v1/account/account/ids/{userId}
  accountIds: [],
  accountIdsLoaded: false,
  accountIdsLoading: false,
  accountIdsError: null,
  accountIdsParams: { userId: null },

  // User accounts from api/accounts/{userId}
  userAccounts: [],
  userAccountsLoaded: false,
  userAccountsLoading: false,
  userAccountsError: null,
  userAccountsParams: { userId: null },

  // Account balances aggregated by type
  accountBalancesInvestment: null,
  accountBalancesInvestmentLoaded: false,
  accountBalancesInvestmentLoading: false,
  accountBalancesInvestmentError: null,
  accountBalancesInvestmentParams: { userId: null, x: null, y: null },

  accountBalancesDepository: null,
  accountBalancesDepositoryLoaded: false,
  accountBalancesDepositoryLoading: false,
  accountBalancesDepositoryError: null,
  accountBalancesDepositoryParams: { userId: null, x: null, y: null },

  accountBalancesLoan: null,
  accountBalancesLoanLoaded: false,
  accountBalancesLoanLoading: false,
  accountBalancesLoanError: null,
  accountBalancesLoanParams: { userId: null, x: null, y: null },

  accountBalancesCredit: null,
  accountBalancesCreditLoaded: false,
  accountBalancesCreditLoading: false,
  accountBalancesCreditError: null,
  accountBalancesCreditParams: { userId: null, x: null, y: null },

  // Payment subscription from api/v1/payment/subscription
  paymentSubscription: null,
  paymentSubscriptionLoaded: false,
  paymentSubscriptionLoading: false,
  paymentSubscriptionError: null,
  paymentSubscriptionParams: { userId: null },

  // Payment methods from api/v1/payment/payment-methods
  paymentMethods: [],
  paymentMethodsLoaded: false,
  paymentMethodsLoading: false,
  paymentMethodsError: null,
  paymentMethodsParams: { userId: null },

  // Payment products from api/v1/payment/products (no invalidation)
  paymentProducts: [],
  paymentProductsLoaded: false,
  paymentProductsLoading: false,
  paymentProductsError: null,
  paymentProductsParams: {},

  // Payment invoices from api/v1/payment/invoices
  paymentInvoices: [],
  paymentInvoicesLoaded: false,
  paymentInvoicesLoading: false,
  paymentInvoicesError: null,
  paymentInvoicesParams: { userId: null },

  // Upcoming invoice from api/v1/payment/invoice/upcoming
  upcomingInvoice: null,
  upcomingInvoiceLoaded: false,
  upcomingInvoiceLoading: false,
  upcomingInvoiceError: null,
  upcomingInvoiceParams: { userId: null },

  // Cache metadata
  lastUpdated: null,
  cacheVersion: '1.0.0'
};

const cacheSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    // Icons actions
    fetchIconsStart: (state) => {
      state.iconsLoading = true;
      state.iconsError = null;
    },
    fetchIconsSuccess: (state, action) => {
      state.icons = action.payload;
      state.iconsLoaded = true;
      state.iconsLoading = false;
      state.iconsError = null;
      
      // Build icon map for faster lookup
      const iconMap = {};
      action.payload.forEach(icon => {
        if (icon.iconName && icon.svgContent) {
          iconMap[icon.iconName.toLowerCase()] = icon.svgContent;
        }
      });
      state.iconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchIconsFailure: (state, action) => {
      state.iconsLoading = false;
      state.iconsError = action.payload;
    },

    // Subcategory icons actions
    fetchSubCategoryIconsStart: (state) => {
      state.subCategoryIconsLoading = true;
      state.subCategoryIconsError = null;
    },
    fetchSubCategoryIconsSuccess: (state, action) => {
      state.subCategoryIcons = action.payload;
      state.subCategoryIconsLoaded = true;
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = null;
      
      // Build subcategory icon map
      const iconMap = {};
      action.payload.forEach(dto => {
        if (dto.subCategoryId && dto.base64Icon) {
          try {
            const base64 = dto.base64Icon;
            // Generate data URL for <img src="...">
            iconMap[dto.subCategoryId] = `data:image/png;base64,${base64}`;
          } catch (error) {
            console.error(`Failed to process icon for subcategory ${dto.subCategoryId}:`, error);
          }
        }
      });
      state.subCategoryIconMap = iconMap;
      state.lastUpdated = Date.now();
    },
    fetchSubCategoryIconsFailure: (state, action) => {
      state.subCategoryIconsLoading = false;
      state.subCategoryIconsError = action.payload;
    },

    // Categories actions
    fetchCategoriesStart: (state) => {
      state.categoriesLoading = true;
      state.categoriesError = null;
    },
    fetchCategoriesSuccess: (state, action) => {
      state.categories = action.payload;
      state.categoriesLoaded = true;
      state.categoriesLoading = false;
      state.categoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchCategoriesFailure: (state, action) => {
      state.categoriesLoading = false;
      state.categoriesError = action.payload;
    },

    // Subcategories actions
    fetchSubcategoriesStart: (state) => {
      state.subcategoriesLoading = true;
      state.subcategoriesError = null;
    },
    fetchSubcategoriesSuccess: (state, action) => {
      state.subcategories = action.payload;
      state.subcategoriesLoaded = true;
      state.subcategoriesLoading = false;
      state.subcategoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchSubcategoriesFailure: (state, action) => {
      state.subcategoriesLoading = false;
      state.subcategoriesError = action.payload;
    },

    // Transactions actions
    fetchTransactionsStart: (state) => {
      state.transactionsLoading = true;
      state.transactionsError = null;
    },
    fetchTransactionsSuccess: (state, action) => {
      state.transactions = action.payload.content || action.payload;
      state.transactionsPagination = {
        page: action.payload.page || 0,
        pageSize: action.payload.pageSize || 250,
        totalElements: action.payload.totalElements || 0,
        totalPages: action.payload.totalPages || 0
      };
      state.transactionsLoaded = true;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchTransactionsFailure: (state, action) => {
      state.transactionsLoading = false;
      state.transactionsError = action.payload;
    },

    // Recurring transactions actions
    fetchRecurringTransactionsStart: (state) => {
      state.recurringTransactionsLoading = true;
      state.recurringTransactionsError = null;
    },
    fetchRecurringTransactionsSuccess: (state, action) => {
      state.recurringTransactions = action.payload;
      state.recurringTransactionsLoaded = true;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchRecurringTransactionsFailure: (state, action) => {
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = action.payload;
    },

    // Future recurring transactions actions
    fetchFutureRecurringTransactionsStart: (state) => {
      state.futureRecurringTransactionsLoading = true;
      state.futureRecurringTransactionsError = null;
    },
    fetchFutureRecurringTransactionsSuccess: (state, action) => {
      state.futureRecurringTransactions = action.payload;
      state.futureRecurringTransactionsLoaded = true;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchFutureRecurringTransactionsFailure: (state, action) => {
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = action.payload;
    },

    // Budget summary actions
    fetchBudgetSummaryStart: (state, action) => {
      state.budgetSummaryLoading = true;
      state.budgetSummaryError = null;
      if (action.payload) {
        state.budgetSummaryParams = action.payload;
      }
    },
    fetchBudgetSummarySuccess: (state, action) => {
      state.budgetSummary = action.payload;
      state.budgetSummaryLoaded = true;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.lastUpdated = Date.now();
    },
    fetchBudgetSummaryFailure: (state, action) => {
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = action.payload;
    },

    // Budget data actions
    fetchBudgetDataStart: (state, action) => {
      state.budgetDataLoading = true;
      state.budgetDataError = null;
      if (action.payload) {
        state.budgetDataParams = action.payload;
      }
    },
    fetchBudgetDataSuccess: (state, action) => {
      state.budgetData = action.payload;
      state.budgetDataLoaded = true;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.lastUpdated = Date.now();
    },
    fetchBudgetDataFailure: (state, action) => {
      state.budgetDataLoading = false;
      state.budgetDataError = action.payload;
    },

    // Transaction summary actions
    fetchTransactionSummaryStart: (state, action) => {
      state.transactionSummaryLoading = true;
      state.transactionSummaryError = null;
      if (action.payload) {
        state.transactionSummaryParams = action.payload;
      }
    },
    fetchTransactionSummarySuccess: (state, action) => {
      state.transactionSummary = action.payload;
      state.transactionSummaryLoaded = true;
      state.transactionSummaryLoading = false;
      state.transactionSummaryError = null;
      state.lastUpdated = Date.now();
    },
    fetchTransactionSummaryFailure: (state, action) => {
      state.transactionSummaryLoading = false;
      state.transactionSummaryError = action.payload;
    },

    // Hidden transactions actions
    fetchHiddenTransactionsStart: (state, action) => {
      state.hiddenTransactionsLoading = true;
      state.hiddenTransactionsError = null;
      if (action.payload) {
        state.hiddenTransactionsParams = action.payload;
      }
    },
    fetchHiddenTransactionsSuccess: (state, action) => {
      state.hiddenTransactions = action.payload;
      state.hiddenTransactionsLoaded = true;
      state.hiddenTransactionsLoading = false;
      state.hiddenTransactionsError = null;
      state.lastUpdated = Date.now();
    },
    fetchHiddenTransactionsFailure: (state, action) => {
      state.hiddenTransactionsLoading = false;
      state.hiddenTransactionsError = action.payload;
    },

    // Reconcile data actions
    fetchReconcileDataStart: (state) => {
      state.reconcileDataLoading = true;
      state.reconcileDataError = null;
    },
    fetchReconcileDataSuccess: (state, action) => {
      state.reconcileData = action.payload;
      state.reconcileDataLoaded = true;
      state.reconcileDataLoading = false;
      state.reconcileDataError = null;
      state.lastUpdated = Date.now();
    },
    fetchReconcileDataFailure: (state, action) => {
      state.reconcileDataLoading = false;
      state.reconcileDataError = action.payload;
    },

    // Receipt transaction IDs actions
    fetchReceiptTransactionIdsStart: (state) => {
      state.receiptTransactionIdsLoading = true;
      state.receiptTransactionIdsError = null;
    },
    fetchReceiptTransactionIdsSuccess: (state, action) => {
      state.receiptTransactionIds = action.payload;
      state.receiptTransactionIdsLoaded = true;
      state.receiptTransactionIdsLoading = false;
      state.receiptTransactionIdsError = null;
      state.lastUpdated = Date.now();
    },
    fetchReceiptTransactionIdsFailure: (state, action) => {
      state.receiptTransactionIdsLoading = false;
      state.receiptTransactionIdsError = action.payload;
    },

    // Receipt items actions
    fetchReceiptItemsStart: (state) => {
      state.receiptItemsLoading = true;
      state.receiptItemsError = null;
    },
    fetchReceiptItemsSuccess: (state, action) => {
      state.receiptItems = action.payload;
      state.receiptItemsLoaded = true;
      state.receiptItemsLoading = false;
      state.receiptItemsError = null;
      state.lastUpdated = Date.now();
    },
    fetchReceiptItemsFailure: (state, action) => {
      state.receiptItemsLoading = false;
      state.receiptItemsError = action.payload;
    },

    // Receipt summary actions
    fetchReceiptSummaryStart: (state) => {
      state.receiptSummaryLoading = true;
      state.receiptSummaryError = null;
    },
    fetchReceiptSummarySuccess: (state, action) => {
      state.receiptSummary = action.payload;
      state.receiptSummaryLoaded = true;
      state.receiptSummaryLoading = false;
      state.receiptSummaryError = null;
      state.lastUpdated = Date.now();
    },
    fetchReceiptSummaryFailure: (state, action) => {
      state.receiptSummaryLoading = false;
      state.receiptSummaryError = action.payload;
    },

    // User receipts actions
    fetchUserReceiptsStart: (state, action) => {
      state.userReceiptsLoading = true;
      state.userReceiptsError = null;
      if (action.payload) {
        state.userReceiptsParams = action.payload;
      }
    },
    fetchUserReceiptsSuccess: (state, action) => {
      state.userReceipts = action.payload;
      state.userReceiptsLoaded = true;
      state.userReceiptsLoading = false;
      state.userReceiptsError = null;
      state.lastUpdated = Date.now();
    },
    fetchUserReceiptsFailure: (state, action) => {
      state.userReceiptsLoading = false;
      state.userReceiptsError = action.payload;
    },

    // Chatbot history actions
    fetchChatbotHistoryStart: (state, action) => {
      state.chatbotHistoryLoading = true;
      state.chatbotHistoryError = null;
      if (action.payload) {
        state.chatbotHistoryParams = action.payload;
      }
    },
    fetchChatbotHistorySuccess: (state, action) => {
      state.chatbotHistory = action.payload;
      state.chatbotHistoryLoaded = true;
      state.chatbotHistoryLoading = false;
      state.chatbotHistoryError = null;
      state.lastUpdated = Date.now();
    },
    fetchChatbotHistoryFailure: (state, action) => {
      state.chatbotHistoryLoading = false;
      state.chatbotHistoryError = action.payload;
    },

    // Distinct subcategories actions
    fetchDistinctSubcategoriesStart: (state, action) => {
      state.distinctSubcategoriesLoading = true;
      state.distinctSubcategoriesError = null;
      if (action.payload) {
        state.distinctSubcategoriesParams = action.payload;
      }
    },
    fetchDistinctSubcategoriesSuccess: (state, action) => {
      state.distinctSubcategories = action.payload;
      state.distinctSubcategoriesLoaded = true;
      state.distinctSubcategoriesLoading = false;
      state.distinctSubcategoriesError = null;
      state.lastUpdated = Date.now();
    },
    fetchDistinctSubcategoriesFailure: (state, action) => {
      state.distinctSubcategoriesLoading = false;
      state.distinctSubcategoriesError = action.payload;
    },

    // Account IDs actions
    fetchAccountIdsStart: (state, action) => {
      state.accountIdsLoading = true;
      state.accountIdsError = null;
      if (action.payload) {
        state.accountIdsParams = action.payload;
      }
    },
    fetchAccountIdsSuccess: (state, action) => {
      state.accountIds = action.payload;
      state.accountIdsLoaded = true;
      state.accountIdsLoading = false;
      state.accountIdsError = null;
      state.lastUpdated = Date.now();
    },
    fetchAccountIdsFailure: (state, action) => {
      state.accountIdsLoading = false;
      state.accountIdsError = action.payload;
    },

    // User accounts actions
    fetchUserAccountsStart: (state, action) => {
      state.userAccountsLoading = true;
      state.userAccountsError = null;
      if (action.payload) {
        state.userAccountsParams = action.payload;
      }
    },
    fetchUserAccountsSuccess: (state, action) => {
      state.userAccounts = action.payload;
      state.userAccountsLoaded = true;
      state.userAccountsLoading = false;
      state.userAccountsError = null;
      state.lastUpdated = Date.now();
    },
    fetchUserAccountsFailure: (state, action) => {
      state.userAccountsLoading = false;
      state.userAccountsError = action.payload;
    },

    // Account balances actions
    fetchAccountBalancesInvestmentStart: (state, action) => {
      state.accountBalancesInvestmentLoading = true;
      state.accountBalancesInvestmentError = null;
      if (action.payload) {
        state.accountBalancesInvestmentParams = action.payload;
      }
    },
    fetchAccountBalancesInvestmentSuccess: (state, action) => {
      state.accountBalancesInvestment = action.payload;
      state.accountBalancesInvestmentLoaded = true;
      state.accountBalancesInvestmentLoading = false;
      state.accountBalancesInvestmentError = null;
      state.lastUpdated = Date.now();
    },
    fetchAccountBalancesInvestmentFailure: (state, action) => {
      state.accountBalancesInvestmentLoading = false;
      state.accountBalancesInvestmentError = action.payload;
    },

    fetchAccountBalancesDepositoryStart: (state, action) => {
      state.accountBalancesDepositoryLoading = true;
      state.accountBalancesDepositoryError = null;
      if (action.payload) {
        state.accountBalancesDepositoryParams = action.payload;
      }
    },
    fetchAccountBalancesDepositorySuccess: (state, action) => {
      state.accountBalancesDepository = action.payload;
      state.accountBalancesDepositoryLoaded = true;
      state.accountBalancesDepositoryLoading = false;
      state.accountBalancesDepositoryError = null;
      state.lastUpdated = Date.now();
    },
    fetchAccountBalancesDepositoryFailure: (state, action) => {
      state.accountBalancesDepositoryLoading = false;
      state.accountBalancesDepositoryError = action.payload;
    },

    fetchAccountBalancesLoanStart: (state, action) => {
      state.accountBalancesLoanLoading = true;
      state.accountBalancesLoanError = null;
      if (action.payload) {
        state.accountBalancesLoanParams = action.payload;
      }
    },
    fetchAccountBalancesLoanSuccess: (state, action) => {
      state.accountBalancesLoan = action.payload;
      state.accountBalancesLoanLoaded = true;
      state.accountBalancesLoanLoading = false;
      state.accountBalancesLoanError = null;
      state.lastUpdated = Date.now();
    },
    fetchAccountBalancesLoanFailure: (state, action) => {
      state.accountBalancesLoanLoading = false;
      state.accountBalancesLoanError = action.payload;
    },

    fetchAccountBalancesCreditStart: (state, action) => {
      state.accountBalancesCreditLoading = true;
      state.accountBalancesCreditError = null;
      if (action.payload) {
        state.accountBalancesCreditParams = action.payload;
      }
    },
    fetchAccountBalancesCreditSuccess: (state, action) => {
      state.accountBalancesCredit = action.payload;
      state.accountBalancesCreditLoaded = true;
      state.accountBalancesCreditLoading = false;
      state.accountBalancesCreditError = null;
      state.lastUpdated = Date.now();
    },
    fetchAccountBalancesCreditFailure: (state, action) => {
      state.accountBalancesCreditLoading = false;
      state.accountBalancesCreditError = action.payload;
    },

    // Payment subscription actions
    fetchPaymentSubscriptionStart: (state, action) => {
      state.paymentSubscriptionLoading = true;
      state.paymentSubscriptionError = null;
      if (action.payload) {
        state.paymentSubscriptionParams = action.payload;
      }
    },
    fetchPaymentSubscriptionSuccess: (state, action) => {
      state.paymentSubscription = action.payload;
      state.paymentSubscriptionLoaded = true;
      state.paymentSubscriptionLoading = false;
      state.paymentSubscriptionError = null;
      state.lastUpdated = Date.now();
    },
    fetchPaymentSubscriptionFailure: (state, action) => {
      state.paymentSubscriptionLoading = false;
      state.paymentSubscriptionError = action.payload;
    },

    // Payment methods actions
    fetchPaymentMethodsStart: (state, action) => {
      state.paymentMethodsLoading = true;
      state.paymentMethodsError = null;
      if (action.payload) {
        state.paymentMethodsParams = action.payload;
      }
    },
    fetchPaymentMethodsSuccess: (state, action) => {
      state.paymentMethods = action.payload;
      state.paymentMethodsLoaded = true;
      state.paymentMethodsLoading = false;
      state.paymentMethodsError = null;
      state.lastUpdated = Date.now();
    },
    fetchPaymentMethodsFailure: (state, action) => {
      state.paymentMethodsLoading = false;
      state.paymentMethodsError = action.payload;
    },

    // Payment products actions
    fetchPaymentProductsStart: (state, action) => {
      state.paymentProductsLoading = true;
      state.paymentProductsError = null;
      if (action.payload) {
        state.paymentProductsParams = action.payload;
      }
    },
    fetchPaymentProductsSuccess: (state, action) => {
      state.paymentProducts = action.payload;
      state.paymentProductsLoaded = true;
      state.paymentProductsLoading = false;
      state.paymentProductsError = null;
      state.lastUpdated = Date.now();
    },
    fetchPaymentProductsFailure: (state, action) => {
      state.paymentProductsLoading = false;
      state.paymentProductsError = action.payload;
    },

    // Payment invoices actions
    fetchPaymentInvoicesStart: (state, action) => {
      state.paymentInvoicesLoading = true;
      state.paymentInvoicesError = null;
      if (action.payload) {
        state.paymentInvoicesParams = action.payload;
      }
    },
    fetchPaymentInvoicesSuccess: (state, action) => {
      state.paymentInvoices = action.payload;
      state.paymentInvoicesLoaded = true;
      state.paymentInvoicesLoading = false;
      state.paymentInvoicesError = null;
      state.lastUpdated = Date.now();
    },
    fetchPaymentInvoicesFailure: (state, action) => {
      state.paymentInvoicesLoading = false;
      state.paymentInvoicesError = action.payload;
    },

    // Upcoming invoice actions
    fetchUpcomingInvoiceStart: (state, action) => {
      state.upcomingInvoiceLoading = true;
      state.upcomingInvoiceError = null;
      if (action.payload) {
        state.upcomingInvoiceParams = action.payload;
      }
    },
    fetchUpcomingInvoiceSuccess: (state, action) => {
      state.upcomingInvoice = action.payload;
      state.upcomingInvoiceLoaded = true;
      state.upcomingInvoiceLoading = false;
      state.upcomingInvoiceError = null;
      state.lastUpdated = Date.now();
    },
    fetchUpcomingInvoiceFailure: (state, action) => {
      state.upcomingInvoiceLoading = false;
      state.upcomingInvoiceError = action.payload;
    },

    // Cache invalidation actions
    invalidateSubcategoryCache: (state) => {
      state.distinctSubcategories = [];
      state.distinctSubcategoriesLoaded = false;
      state.distinctSubcategoriesLoading = false;
      state.distinctSubcategoriesError = null;
    },

    invalidateAccountCache: (state) => {
      // Invalidate all account-related caches
      state.accountIds = [];
      state.accountIdsLoaded = false;
      state.accountIdsLoading = false;
      state.accountIdsError = null;

      state.userAccounts = [];
      state.userAccountsLoaded = false;
      state.userAccountsLoading = false;
      state.userAccountsError = null;

      state.accountBalancesInvestment = null;
      state.accountBalancesInvestmentLoaded = false;
      state.accountBalancesInvestmentLoading = false;
      state.accountBalancesInvestmentError = null;

      state.accountBalancesDepository = null;
      state.accountBalancesDepositoryLoaded = false;
      state.accountBalancesDepositoryLoading = false;
      state.accountBalancesDepositoryError = null;

      state.accountBalancesLoan = null;
      state.accountBalancesLoanLoaded = false;
      state.accountBalancesLoanLoading = false;
      state.accountBalancesLoanError = null;

      state.accountBalancesCredit = null;
      state.accountBalancesCreditLoaded = false;
      state.accountBalancesCreditLoading = false;
      state.accountBalancesCreditError = null;
    },

    // Payment cache invalidation actions
    invalidatePaymentSubscriptionCache: (state) => {
      state.paymentSubscription = null;
      state.paymentSubscriptionLoaded = false;
      state.paymentSubscriptionLoading = false;
      state.paymentSubscriptionError = null;

      // Also invalidate invoices and upcoming invoice when subscription changes
      state.paymentInvoices = [];
      state.paymentInvoicesLoaded = false;
      state.paymentInvoicesLoading = false;
      state.paymentInvoicesError = null;

      state.upcomingInvoice = null;
      state.upcomingInvoiceLoaded = false;
      state.upcomingInvoiceLoading = false;
      state.upcomingInvoiceError = null;
    },

    invalidatePaymentMethodsCache: (state) => {
      state.paymentMethods = [];
      state.paymentMethodsLoaded = false;
      state.paymentMethodsLoading = false;
      state.paymentMethodsError = null;
    },

    // Clear cache action
    clearCache: (state) => {
      // Revoke object URLs to prevent memory leaks
      Object.values(state.subCategoryIconMap).forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (error) {
          console.warn('Failed to revoke object URL:', error);
        }
      });

      return initialState;
    },

    // Selective cache invalidation actions
    invalidateTransactionCache: (state) => {
      console.log('🗑️ Invalidating transaction cache');
      state.transactions = [];
      state.transactionsLoaded = false;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.transactionsPagination = { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 };
    },

    invalidateRecurringTransactionCache: (state) => {
      console.log('🗑️ Invalidating recurring transaction cache');
      state.recurringTransactions = [];
      state.recurringTransactionsLoaded = false;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;

      state.futureRecurringTransactions = [];
      state.futureRecurringTransactionsLoaded = false;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;
    },

    invalidateBudgetCache: (state) => {
      console.log('🗑️ Invalidating budget cache');
      state.budgetSummary = null;
      state.budgetSummaryLoaded = false;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.budgetSummaryParams = { userId: null, year: null, month: null };

      state.budgetData = [];
      state.budgetDataLoaded = false;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.budgetDataParams = { userId: null };
    },

    invalidateAllTransactionRelatedCache: (state) => {
      console.log('🗑️ Invalidating all transaction and budget related cache');
      // Invalidate transactions
      state.transactions = [];
      state.transactionsLoaded = false;
      state.transactionsLoading = false;
      state.transactionsError = null;
      state.transactionsPagination = { page: 0, pageSize: 250, totalElements: 0, totalPages: 0 };

      // Invalidate recurring transactions
      state.recurringTransactions = [];
      state.recurringTransactionsLoaded = false;
      state.recurringTransactionsLoading = false;
      state.recurringTransactionsError = null;

      state.futureRecurringTransactions = [];
      state.futureRecurringTransactionsLoaded = false;
      state.futureRecurringTransactionsLoading = false;
      state.futureRecurringTransactionsError = null;

      // Invalidate budget data
      state.budgetSummary = null;
      state.budgetSummaryLoaded = false;
      state.budgetSummaryLoading = false;
      state.budgetSummaryError = null;
      state.budgetSummaryParams = { userId: null, year: null, month: null };

      state.budgetData = [];
      state.budgetDataLoaded = false;
      state.budgetDataLoading = false;
      state.budgetDataError = null;
      state.budgetDataParams = { userId: null };

      // Invalidate transaction-related new APIs
      state.transactionSummary = null;
      state.transactionSummaryLoaded = false;
      state.transactionSummaryLoading = false;
      state.transactionSummaryError = null;
      state.transactionSummaryParams = { userId: null };

      state.hiddenTransactions = [];
      state.hiddenTransactionsLoaded = false;
      state.hiddenTransactionsLoading = false;
      state.hiddenTransactionsError = null;
      state.hiddenTransactionsParams = { userId: null };

      state.reconcileData = [];
      state.reconcileDataLoaded = false;
      state.reconcileDataLoading = false;
      state.reconcileDataError = null;

      // Invalidate subcategories (affected by transaction changes)
      state.distinctSubcategories = [];
      state.distinctSubcategoriesLoaded = false;
      state.distinctSubcategoriesLoading = false;
      state.distinctSubcategoriesError = null;
    },

    // Invalidate receipt cache
    invalidateReceiptCache: (state) => {
      console.log('🗑️ Invalidating receipt cache');
      state.receiptTransactionIds = [];
      state.receiptTransactionIdsLoaded = false;
      state.receiptTransactionIdsLoading = false;
      state.receiptTransactionIdsError = null;

      state.receiptItems = [];
      state.receiptItemsLoaded = false;
      state.receiptItemsLoading = false;
      state.receiptItemsError = null;

      state.receiptSummary = null;
      state.receiptSummaryLoaded = false;
      state.receiptSummaryLoading = false;
      state.receiptSummaryError = null;

      state.userReceipts = [];
      state.userReceiptsLoaded = false;
      state.userReceiptsLoading = false;
      state.userReceiptsError = null;
      state.userReceiptsParams = { userId: null };
    },

    // Invalidate chatbot cache
    invalidateChatbotCache: (state) => {
      console.log('🗑️ Invalidating chatbot cache');
      state.chatbotHistory = [];
      state.chatbotHistoryLoaded = false;
      state.chatbotHistoryLoading = false;
      state.chatbotHistoryError = null;
      state.chatbotHistoryParams = { userId: null };
    },

    // Initialize cache from login
    initializeCacheStart: () => {
      // Don't set loading states here - let individual epics handle their own loading states
      // This action just triggers the cache initialization process
      console.log('Cache initialization triggered');
    }
  }
});

export const {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  fetchTransactionsStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailure,
  fetchFutureRecurringTransactionsStart,
  fetchFutureRecurringTransactionsSuccess,
  fetchFutureRecurringTransactionsFailure,
  fetchBudgetSummaryStart,
  fetchBudgetSummarySuccess,
  fetchBudgetSummaryFailure,
  fetchBudgetDataStart,
  fetchBudgetDataSuccess,
  fetchBudgetDataFailure,
  fetchTransactionSummaryStart,
  fetchTransactionSummarySuccess,
  fetchTransactionSummaryFailure,
  fetchHiddenTransactionsStart,
  fetchHiddenTransactionsSuccess,
  fetchHiddenTransactionsFailure,
  fetchReconcileDataStart,
  fetchReconcileDataSuccess,
  fetchReconcileDataFailure,
  fetchReceiptTransactionIdsStart,
  fetchReceiptTransactionIdsSuccess,
  fetchReceiptTransactionIdsFailure,
  fetchReceiptItemsStart,
  fetchReceiptItemsSuccess,
  fetchReceiptItemsFailure,
  fetchReceiptSummaryStart,
  fetchReceiptSummarySuccess,
  fetchReceiptSummaryFailure,
  fetchUserReceiptsStart,
  fetchUserReceiptsSuccess,
  fetchUserReceiptsFailure,
  fetchChatbotHistoryStart,
  fetchChatbotHistorySuccess,
  fetchChatbotHistoryFailure,
  fetchDistinctSubcategoriesStart,
  fetchDistinctSubcategoriesSuccess,
  fetchDistinctSubcategoriesFailure,
  fetchAccountIdsStart,
  fetchAccountIdsSuccess,
  fetchAccountIdsFailure,
  fetchUserAccountsStart,
  fetchUserAccountsSuccess,
  fetchUserAccountsFailure,
  fetchAccountBalancesInvestmentStart,
  fetchAccountBalancesInvestmentSuccess,
  fetchAccountBalancesInvestmentFailure,
  fetchAccountBalancesDepositoryStart,
  fetchAccountBalancesDepositorySuccess,
  fetchAccountBalancesDepositoryFailure,
  fetchAccountBalancesLoanStart,
  fetchAccountBalancesLoanSuccess,
  fetchAccountBalancesLoanFailure,
  fetchAccountBalancesCreditStart,
  fetchAccountBalancesCreditSuccess,
  fetchAccountBalancesCreditFailure,
  fetchPaymentSubscriptionStart,
  fetchPaymentSubscriptionSuccess,
  fetchPaymentSubscriptionFailure,
  fetchPaymentMethodsStart,
  fetchPaymentMethodsSuccess,
  fetchPaymentMethodsFailure,
  fetchPaymentProductsStart,
  fetchPaymentProductsSuccess,
  fetchPaymentProductsFailure,
  fetchPaymentInvoicesStart,
  fetchPaymentInvoicesSuccess,
  fetchPaymentInvoicesFailure,
  fetchUpcomingInvoiceStart,
  fetchUpcomingInvoiceSuccess,
  fetchUpcomingInvoiceFailure,
  clearCache,
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  invalidateReceiptCache,
  invalidateChatbotCache,
  invalidateSubcategoryCache,
  invalidateAccountCache,
  invalidatePaymentSubscriptionCache,
  invalidatePaymentMethodsCache,
  initializeCacheStart
} = cacheSlice.actions;

export default cacheSlice.reducer;