package com.pennypal.fintech.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CustomChartDto {

    @NotNull(message = "Chat ID is required")
    private Integer chatId;

    @NotNull(message = "User ID is required")
    private Integer userId;

    @NotBlank(message = "Chart Title cannot be blank")
    private String title;

    @NotBlank(message = "Chart Type cannot be blank")
    private String type;

    @NotBlank(message = "Chart Data cannot be blank")
    private String data;
}