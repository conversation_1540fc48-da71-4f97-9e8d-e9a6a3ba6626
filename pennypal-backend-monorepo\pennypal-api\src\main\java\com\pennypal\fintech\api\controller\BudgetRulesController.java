package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.BudgetRuleDto;
import com.pennypal.fintech.service.BudgetRulesService;

import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/budget-rules")
@Tag(name = "Budget Rules", description = "Budget Rules management APIs")
@Slf4j
public class BudgetRulesController {

    private final BudgetRulesService budgetRulesService;



    @Autowired
    public BudgetRulesController(BudgetRulesService budgetRulesService) {
        this.budgetRulesService = budgetRulesService;
    }

    @PostMapping("/create")
    @Operation(summary = "Create a new budget rule")
    public ResponseEntity<BudgetRuleDto> createBudgetRule(@RequestBody BudgetRuleDto budgetRuleDto) {
        try {
            log.info("Received BudgetRuleDto for creating: " + budgetRuleDto);
            BudgetRuleDto createdRule = budgetRulesService.createBudgetRule(budgetRuleDto);
            return new ResponseEntity<>(createdRule, HttpStatus.CREATED);
        } catch (RuntimeException e) {
            log.error("Error creating budget rule: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
        }
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "Get all budget rules for a user")
    public ResponseEntity<List<BudgetRuleDto>> getBudgetRulesByUser(@PathVariable Integer userId) {
        try {
            List<BudgetRuleDto> rules = budgetRulesService.getBudgetRulesByUserId(userId);
            return ResponseEntity.ok(rules);
        } catch (RuntimeException e) {
            log.error("Error fetching budget rules: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    //  Delete Budget Rule API
    @PutMapping("/delete/{ruleId}")
    @Operation(summary = "Delete a budget rule")
    public ResponseEntity<Void> deleteBudgetRule(@PathVariable Integer ruleId) {
        try {
            if (ruleId == null) {
                return ResponseEntity.badRequest().build();
            }
            budgetRulesService.deleteBudgetRule(ruleId);
            return ResponseEntity.ok().build();
        } catch (RuntimeException e) {
            log.error("Error deleting budget rule: " + e.getMessage());
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
    }

    // Edit Budget Rule API
    @PutMapping("/update")
    @Operation(summary = "Edit a budget rule")
    public ResponseEntity<BudgetRuleDto> editBudgetRule(
        @RequestBody BudgetRuleDto budgetRuleDto) {
            log.info("Received BudgetRuleDto for updating: " + budgetRuleDto);
            try {
                log.info("Received BudgetRuleDto: " + budgetRuleDto);
                if (budgetRuleDto.getId() == null) {
                    return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
                }
                BudgetRuleDto editedRule = budgetRulesService.createBudgetRule(budgetRuleDto);
                return new ResponseEntity<>(editedRule, HttpStatus.OK);
            } catch (RuntimeException e) {
                log.error("Error editing budget rule: " + e.getMessage());
                return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
            }
    }
}
