// package com.pennypal.fintech.repository;

// import org.springframework.data.jpa.repository.JpaRepository;
// import org.springframework.data.jpa.repository.Query;
// import org.springframework.data.repository.query.Param;
// import org.springframework.stereotype.Repository;

// import com.pennypal.fintech.entity.DailyInvestmentStock;
// import java.time.LocalDateTime;
// import java.util.List;

// @Repository
// public interface DailyInvestmentStockRepository extends JpaRepository<DailyInvestmentStock, Integer> {
//     List<DailyInvestmentStock> findByInvestment_Id(int investmentId);
    
//     // @Query("SELECT dis FROM DailyInvestmentStock dis WHERE dis.investment.id = :investmentId AND dis.timestamp BETWEEN :startDate AND :endDate ORDER BY dis.timestamp")
//     // List<DailyInvestmentStock> findByInvestmentIdAndDateRange(
//     //     @Param("investmentId") int investmentId, 
//     //     @Param("startDate") LocalDateTime startDate, 
//     //     @Param("endDate") LocalDateTime endDate);
    
//     // @Query("SELECT dis FROM DailyInvestmentStock dis WHERE dis.investment.id = :investmentId ORDER BY dis.timestamp DESC LIMIT 1")
//     // DailyInvestmentStock findLatestByInvestmentId(@Param("investmentId") int investmentId);
    
//     @Query("SELECT dis FROM DailyInvestmentStock dis JOIN dis.investment i WHERE i.user.id = :userId AND dis.timestamp BETWEEN :startDate AND :endDate ORDER BY dis.timestamp")
//     List<DailyInvestmentStock> findByUserIdAndDateRange(
//         @Param("userId") int userId, 
//         @Param("startDate") LocalDateTime startDate, 
//         @Param("endDate") LocalDateTime endDate);
//         /**
//      * Find latest stock record for an investment
//      * @param investmentId The investment ID
//      * @return The latest DailyInvestmentStock record
//      */
//     @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.id = :investmentId ORDER BY d.timestamp DESC LIMIT 1")
//     DailyInvestmentStock findLatestByInvestmentId(@Param("investmentId") int investmentId);
    
//     /**
//      * Find all stock records for an investment
//      * @param investmentId The investment ID
//      * @return List of DailyInvestmentStock records
//      */
//     List<DailyInvestmentStock> findByInvestment_IdOrderByTimestampDesc(int investmentId);
// }

package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.entity.Investment;
import java.util.Optional;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Repository
public interface DailyInvestmentStockRepository extends JpaRepository<DailyInvestmentStock, Integer> {
    List<DailyInvestmentStock> findByInvestment_Id(int investmentId);
     List<DailyInvestmentStock> findByInvestmentId(Integer investmentId);
     List<DailyInvestmentStock> findByInvestment(Investment investment);
    @Query("SELECT dis FROM DailyInvestmentStock dis JOIN dis.investment i WHERE i.user.id = :userId AND dis.timestamp BETWEEN :startDate AND :endDate ORDER BY dis.timestamp")
    List<DailyInvestmentStock> findByUserIdAndDateRange(
        @Param("userId") int userId, 
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.id = :investmentId AND d.timestamp > :timestamp ORDER BY d.timestamp ASC")
    List<DailyInvestmentStock> findByInvestment_IdAndTimestampAfterOrderByTimestampAsc(
        @Param("investmentId") int investmentId,
        @Param("timestamp") LocalDateTime timestamp
    );
    /**
     * Find all stock records for an investment
     * @param investmentId The investment ID
     * @return List of DailyInvestmentStock records
     */
    List<DailyInvestmentStock> findByInvestment_IdOrderByTimestampDesc(int investmentId);
    @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.id = :investmentId AND d.timestamp >= :fromDate AND d.timestamp <= :toDate ORDER BY d.timestamp DESC")
    List<DailyInvestmentStock> findByInvestmentIdAndTimestampBetween(@Param("investmentId") Integer investmentId, 
                                                                    @Param("fromDate") LocalDateTime fromDate, 
                                                                    @Param("toDate") LocalDateTime toDate);
    
    @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.user.id = :userId AND d.timestamp >= :fromDate AND d.timestamp <= :toDate ORDER BY d.timestamp DESC")
    List<DailyInvestmentStock> findByUserIdAndTimestampBetween(@Param("userId") Integer userId, 
                                                              @Param("fromDate") LocalDateTime fromDate, 
                                                              @Param("toDate") LocalDateTime toDate);
    
    Optional<DailyInvestmentStock> findTopByInvestmentIdOrderByTimestampDesc(Integer investmentId);
    
    @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.ticker = :ticker AND d.timestamp >= :fromDate ORDER BY d.timestamp DESC")
    List<DailyInvestmentStock> findByTickerAndTimestampAfter(@Param("ticker") String ticker, 
                                                           @Param("fromDate") LocalDateTime fromDate);
    
    @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.user.id = :userId ORDER BY d.timestamp DESC")
    List<DailyInvestmentStock> findByUserIdOrderByTimestampDesc(@Param("userId") Integer userId);
    
    void deleteByInvestmentId(Integer investmentId);
    
@Query("SELECT dis FROM DailyInvestmentStock dis WHERE dis.investment.id = :investmentId " +
       "AND dis.timestamp < :beforeTimestamp ORDER BY dis.timestamp DESC")
Optional<DailyInvestmentStock> findTopByInvestmentIdAndTimestampBeforeOrderByTimestampDesc(
    @Param("investmentId") Integer investmentId, 
    @Param("beforeTimestamp") LocalDateTime beforeTimestamp
);
// Add this query to DailyInvestmentStockRepository
@Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.id = :investmentId " +
       "AND DATE(d.timestamp) = DATE(:date)")
Optional<DailyInvestmentStock> findByInvestmentIdAndDate(
    @Param("investmentId") Integer investmentId, 
    @Param("date") LocalDateTime date
);
// Add these methods to your DailyInvestmentStockRepository interface

/**
 * Find daily stock record by investment ID and specific date
 */
Optional<DailyInvestmentStock> findByInvestmentIdAndDate(Integer investmentId, LocalDate date);

/**
 * Find the most recent record before a specific date for price comparison
 */
Optional<DailyInvestmentStock> findTopByInvestmentIdAndDateBeforeOrderByDateDesc(
    Integer investmentId, LocalDate date);

/**
 * Find all records for a specific security type (stock/mutual fund)
 */
List<DailyInvestmentStock> findBySecurityTypeAndDateBetween(
    String securityType, LocalDate fromDate, LocalDate toDate);

/**
 * Find records by ticker symbol for tracking specific securities
 */
List<DailyInvestmentStock> findByTickerAndDateBetween(
    String ticker, LocalDate fromDate, LocalDate toDate);

/**
 * Get daily records for a user's investments by security type
 */
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.securityType = :securityType " +
       "AND dis.date BETWEEN :fromDate AND :toDate " +
       "ORDER BY dis.date DESC")
List<DailyInvestmentStock> findByUserIdAndSecurityTypeAndDateBetween(
    @Param("userId") Integer userId,
    @Param("securityType") String securityType,
    @Param("fromDate") LocalDate fromDate,
    @Param("toDate") LocalDate toDate);
 @Query("SELECT d FROM DailyInvestmentStock d WHERE d.investment.id = :investmentId ORDER BY d.timestamp DESC")
    List<DailyInvestmentStock> findTopByInvestmentIdOrderByTimestampDesc(@Param("investmentId") int investmentId);
    
    default DailyInvestmentStock findLatestByInvestmentId(int investmentId) {
        List<DailyInvestmentStock> results = findTopByInvestmentIdOrderByTimestampDesc(investmentId);
        return results.isEmpty() ? null : results.get(0);
    }
/**
 * Delete old records beyond retention period
 */
void deleteByDateBefore(LocalDate cutoffDate);
// Add this to DailyInvestmentStockRepository interface
Optional<DailyInvestmentStock> findTopByInvestmentIdOrderByDateDescTimestampDesc(Integer investmentId);
// Find by userId, date and ticker
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId AND dis.date = :date AND dis.ticker = :ticker")
List<DailyInvestmentStock> findByUserIdAndDateAndTicker(@Param("userId") Integer userId,
                                                        @Param("date") LocalDate date,
                                                        @Param("ticker") String ticker);

// Find by userId and date
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId AND dis.date = :date")
List<DailyInvestmentStock> findByUserIdAndDate(@Param("userId") Integer userId,
                                               @Param("date") LocalDate date);

// Find by userId and ticker
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId AND dis.ticker = :ticker")
List<DailyInvestmentStock> findByUserIdAndTicker(@Param("userId") Integer userId,
                                                 @Param("ticker") String ticker);

// Find all by userId
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId")
List<DailyInvestmentStock> findByUserId(@Param("userId") Integer userId);
// Add these methods to your DailyInvestmentStockRepository interface

/**
 * Find daily stock records by user ID, ticker, and date range for stock history
 */
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.ticker = :ticker " +
       "AND dis.date BETWEEN :fromDate AND :toDate " +
       "ORDER BY dis.date ASC")
List<DailyInvestmentStock> findByUserIdAndTickerAndDateBetween(
    @Param("userId") Integer userId,
    @Param("ticker") String ticker,
    @Param("fromDate") LocalDate fromDate,
    @Param("toDate") LocalDate toDate);

/**
 * Find the latest stock record for a specific ticker and user
 */
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.ticker = :ticker " +
       "ORDER BY dis.date DESC, dis.timestamp DESC " +
       "LIMIT 1")
Optional<DailyInvestmentStock> findLatestByUserIdAndTicker(
    @Param("userId") Integer userId,
    @Param("ticker") String ticker);

/**
 * Find all unique tickers for a user
 */
@Query("SELECT DISTINCT dis.ticker FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.ticker IS NOT NULL " +
       "ORDER BY dis.ticker")
List<String> findDistinctTickersByUserId(@Param("userId") Integer userId);

/**
 * Get stock performance summary for a ticker within date range
 */
@Query("SELECT new map(" +
       "MIN(dis.price) as minPrice, " +
       "MAX(dis.price) as maxPrice, " +
       "AVG(dis.price) as avgPrice, " +
       "COUNT(dis.id) as dataPoints" +
       ") FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.ticker = :ticker " +
       "AND dis.date BETWEEN :fromDate AND :toDate")
Map<String, Object> getStockPerformanceSummary(
    @Param("userId") Integer userId,
    @Param("ticker") String ticker,
    @Param("fromDate") LocalDate fromDate,
    @Param("toDate") LocalDate toDate);

/**
 * Find records for multiple tickers within date range (for portfolio comparison)
 */
@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.ticker IN :tickers " +
       "AND dis.date BETWEEN :fromDate AND :toDate " +
       "ORDER BY dis.ticker, dis.date ASC")
List<DailyInvestmentStock> findByUserIdAndTickersAndDateBetween(
    @Param("userId") Integer userId,
    @Param("tickers") List<String> tickers,
    @Param("fromDate") LocalDate fromDate,
    @Param("toDate") LocalDate toDate);


    // Add these methods to your DailyInvestmentStockRepository interface

/**
 * Get aggregated stock data for a specific ticker with date range and interval grouping
 * Similar to getAggregatedBalancesByAccountType pattern
 */
@Query(value = """
    WITH RECURSIVE DateSeries AS (
        -- Generate date series for the given range with y-day intervals
        SELECT :startDate AS interval_start, 
               DATE_ADD(:startDate, INTERVAL :intervalDays DAY) AS interval_end
        UNION ALL
        SELECT DATE_ADD(interval_start, INTERVAL :intervalDays DAY),
               DATE_ADD(interval_end, INTERVAL :intervalDays DAY)
        FROM DateSeries
        WHERE interval_start < :endDate
    ),
    FilteredStockData AS (
        -- Get stock data within the date range for specific ticker
        SELECT dis.date, dis.price, dis.quantity, dis.ticker,
               i.cost_basis, i.quantity as investment_quantity,
               COALESCE(i.cost_basis / NULLIF(i.quantity, 0), 0) as cost_per_share
        FROM daily_investment_stocks dis
        LEFT JOIN investments i ON dis.investment_id = i.id
        WHERE dis.date >= :startDate 
              AND dis.date <= :endDate
              AND dis.user_id = :userId 
              AND dis.ticker = :ticker
              AND dis.price IS NOT NULL 
              AND dis.quantity IS NOT NULL
    ),
    GroupedStockData AS (
        -- Group stock data by date intervals
        SELECT ds.interval_start,
               ds.interval_end,
               AVG(fsd.price) as avg_price,
               SUM(fsd.quantity) as total_quantity,
               AVG(fsd.cost_per_share) as avg_cost_basis,
               SUM(fsd.cost_basis) as total_cost_basis,
               COUNT(fsd.date) as data_points
        FROM DateSeries ds
        LEFT JOIN FilteredStockData fsd ON fsd.date >= ds.interval_start 
                                       AND fsd.date < ds.interval_end
        WHERE ds.interval_start <= :endDate
        GROUP BY ds.interval_start, ds.interval_end
        HAVING COUNT(fsd.date) > 0  -- Only include intervals with data
    )
    SELECT 
        CAST(interval_end AS DATE) as group_end_date,
        ROUND(avg_price, 4) as avg_price,
        ROUND(total_quantity, 4) as total_quantity,
        ROUND(avg_cost_basis, 4) as avg_cost_basis,
        ROUND(total_cost_basis, 4) as total_cost_basis
    FROM GroupedStockData
    ORDER BY interval_end
    """, nativeQuery = true)
List<Object[]> getAggregatedStockData(@Param("userId") Integer userId, 
                                     @Param("ticker") String ticker, 
                                     @Param("startDate") LocalDate startDate,
                                     @Param("endDate") LocalDate endDate,
                                     @Param("intervalDays") Integer intervalDays);

/**
 * Get aggregated stock data for ALL tickers with date range and interval grouping
 * Returns all user's stocks with their aggregated data
 */

@Query(value = """
    WITH RECURSIVE DateSeries AS (
        -- Generate date series for the given range with y-day intervals
        SELECT :startDate AS interval_start, 
               DATE_ADD(:startDate, INTERVAL :intervalDays DAY) AS interval_end
        UNION ALL
        SELECT DATE_ADD(interval_start, INTERVAL :intervalDays DAY),
               DATE_ADD(interval_end, INTERVAL :intervalDays DAY)
        FROM DateSeries
        WHERE interval_start < :endDate
    ),
    FilteredStockData AS (
        -- Get stock data within the date range for all tickers
        SELECT dis.date, dis.price, dis.quantity, dis.ticker,
               i.cost_basis, i.quantity as investment_quantity,
               COALESCE(i.cost_basis / NULLIF(i.quantity, 0), 0) as cost_per_share
        FROM daily_investment_stocks dis
        LEFT JOIN investments i ON dis.investment_id = i.id
        WHERE dis.date >= :startDate 
              AND dis.date <= :endDate
              AND dis.user_id = :userId
              AND dis.price IS NOT NULL 
              AND dis.quantity IS NOT NULL
    ),
    GroupedStockData AS (
        -- Group stock data by ticker and date intervals
        SELECT ds.interval_start,
               ds.interval_end,
               fsd.ticker,
               AVG(fsd.price) as avg_price,
               SUM(fsd.quantity) as total_quantity,
               AVG(fsd.cost_per_share) as avg_cost_basis,
               SUM(fsd.cost_basis) as total_cost_basis,
               COUNT(fsd.date) as data_points
        FROM DateSeries ds
        CROSS JOIN (SELECT DISTINCT ticker FROM FilteredStockData) tickers
        LEFT JOIN FilteredStockData fsd ON fsd.date >= ds.interval_start 
                                       AND fsd.date < ds.interval_end
                                       AND fsd.ticker = tickers.ticker
        WHERE ds.interval_start <= :endDate
        GROUP BY ds.interval_start, ds.interval_end, fsd.ticker
        HAVING COUNT(fsd.date) > 0  -- Only include intervals with data
    )
    SELECT 
        ticker,
        DATE(interval_end) as group_end_date,  -- Explicit DATE() conversion
        ROUND(avg_price, 4) as avg_price,
        ROUND(total_quantity, 4) as total_quantity,
        ROUND(avg_cost_basis, 4) as avg_cost_basis,
        ROUND(total_cost_basis, 4) as total_cost_basis
    FROM GroupedStockData
    WHERE ticker IS NOT NULL
    ORDER BY ticker, interval_end
    """, nativeQuery = true)
List<Object[]> getAllStocksAggregatedData(@Param("userId") Integer userId, 
                                         @Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate,
                                         @Param("intervalDays") Integer intervalDays);

/**
 * Get current stock holdings summary for a user
 * Useful for getting latest stock positions
 */
@Query(value = """
    SELECT 
        dis.ticker,
        dis.price as current_price,
        SUM(dis.quantity) as total_quantity,
        SUM(dis.value) as total_current_value,
        AVG(i.cost_basis / NULLIF(i.quantity, 0)) as avg_cost_basis,
        SUM(i.cost_basis) as total_invested,
        COUNT(DISTINCT dis.date) as trading_days
    FROM daily_investment_stocks dis
    LEFT JOIN investments i ON dis.investment_id = i.id
    WHERE dis.user_id = :userId
          AND dis.date = (
              SELECT MAX(dis2.date) 
              FROM daily_investment_stocks dis2 
              WHERE dis2.user_id = :userId AND dis2.ticker = dis.ticker
          )
    GROUP BY dis.ticker, dis.price
    ORDER BY total_current_value DESC
    """, nativeQuery = true)
List<Object[]> getCurrentStockHoldings(@Param("userId") Integer userId);

/**
 * Get stock performance summary for a specific period
 * Returns gain/loss data for all stocks in the given period
 */
@Query(value = """
    WITH StockPerformance AS (
        SELECT 
            dis.ticker,
            MIN(dis.date) as start_date,
            MAX(dis.date) as end_date,
            (SELECT dis1.price FROM daily_investment_stocks dis1 
             WHERE dis1.ticker = dis.ticker AND dis1.user_id = :userId 
             AND dis1.date = MIN(dis.date) LIMIT 1) as start_price,
            (SELECT dis2.price FROM daily_investment_stocks dis2 
             WHERE dis2.ticker = dis.ticker AND dis2.user_id = :userId 
             AND dis2.date = MAX(dis.date) LIMIT 1) as end_price,
            SUM(dis.quantity) as total_quantity,
            AVG(i.cost_basis / NULLIF(i.quantity, 0)) as avg_cost_basis,
            SUM(i.cost_basis) as total_invested
        FROM daily_investment_stocks dis
        LEFT JOIN investments i ON dis.investment_id = i.id
        WHERE dis.user_id = :userId
              AND dis.date >= :startDate
              AND dis.date <= :endDate
        GROUP BY dis.ticker
    )
    SELECT 
        ticker,
        start_date,
        end_date,
        start_price,
        end_price,
        total_quantity,
        avg_cost_basis,
        total_invested,
        ROUND(end_price * total_quantity, 2) as current_value,
        ROUND((end_price * total_quantity) - total_invested, 2) as gain_loss,
        ROUND(((end_price * total_quantity) - total_invested) / NULLIF(total_invested, 0) * 100, 2) as gain_loss_percent
    FROM StockPerformance
    WHERE start_price IS NOT NULL AND end_price IS NOT NULL
    ORDER BY gain_loss DESC
    """, nativeQuery = true)
List<Object[]> getStockPerformanceSummary(@Param("userId") Integer userId,
                                         @Param("startDate") LocalDate startDate,
                                         @Param("endDate") LocalDate endDate);

@Query("SELECT dis FROM DailyInvestmentStock dis " +
       "JOIN dis.investment i " +
       "WHERE i.user.id = :userId " +
       "AND dis.date BETWEEN :fromDate AND :toDate " +
       "ORDER BY dis.date ASC")
List<DailyInvestmentStock> findByUserIdAndDateBetween(
    @Param("userId") Integer userId,
    @Param("fromDate") LocalDate fromDate,
    @Param("toDate") LocalDate toDate);


                                        }