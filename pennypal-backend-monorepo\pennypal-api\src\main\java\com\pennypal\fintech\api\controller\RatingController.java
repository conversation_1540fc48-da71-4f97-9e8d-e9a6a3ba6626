package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.UserRatingDto;
import com.pennypal.fintech.dto.RatingResponseDto;
import com.pennypal.fintech.dto.RatingStatsDto;
import com.pennypal.fintech.service.RatingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/ratings")

public class RatingController {
    
    @Autowired
    private RatingService ratingService;
    
    @PostMapping("/submit")
    public ResponseEntity<?> submitRating(@RequestBody UserRatingDto ratingDto) {
        try {
            log.info("Received rating submission request for user: {}", ratingDto.getUserId());
            
            RatingResponseDto response = ratingService.submitRating(ratingDto);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating submitted successfully", 
                response
            ));
            
        } catch (Exception e) {
            log.error("Error submitting rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @GetMapping("/user/{userId}")
    public ResponseEntity<?> getUserRating(@PathVariable Integer userId) {
        try {
            log.info("Received get rating request for user: {}", userId);
            
            RatingResponseDto rating = ratingService.getUserRating(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating retrieved successfully", 
                rating
            ));
            
        } catch (Exception e) {
            log.error("Error getting user rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @GetMapping("/stats/{userId}")
    public ResponseEntity<?> getUserRatingStats(@PathVariable Integer userId) {
        try {
            log.info("Received rating stats request for user: {}", userId);
            
            RatingStatsDto stats = ratingService.getUserRatingStats(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating stats retrieved successfully", 
                stats
            ));
            
        } catch (Exception e) {
            log.error("Error getting rating stats: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @PutMapping("/update")
    public ResponseEntity<?> updateRating(@RequestBody UserRatingDto ratingDto) {
        try {
            log.info("Received rating update request for user: {}", ratingDto.getUserId());
            
            RatingResponseDto response = ratingService.submitRating(ratingDto);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating updated successfully", 
                response
            ));
            
        } catch (Exception e) {
            log.error("Error updating rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    @DeleteMapping("/user/{userId}")
    public ResponseEntity<?> deleteUserRating(@PathVariable Integer userId) {
        try {
            log.info("Received delete rating request for user: {}", userId);
            
            boolean deleted = ratingService.deleteUserRating(userId);
            
            return ResponseEntity.ok().body(new ApiResponse<>(
                true, 
                "Rating deleted successfully", 
                deleted
            ));
            
        } catch (Exception e) {
            log.error("Error deleting rating: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new ApiResponse<>(
                false, 
                e.getMessage(), 
                null
            ));
        }
    }
    
    // Inner class for API Response structure
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        
        public ApiResponse(boolean success, String message, T data) {
            this.success = success;
            this.message = message;
            this.data = data;
        }
        
        // Getters and setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public T getData() {
            return data;
        }
        
        public void setData(T data) {
            this.data = data;
        }
    }
}