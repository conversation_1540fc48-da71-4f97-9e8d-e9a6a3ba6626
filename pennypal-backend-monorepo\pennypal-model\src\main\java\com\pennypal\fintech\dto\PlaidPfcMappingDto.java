package com.pennypal.fintech.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PlaidPfcMappingDto {
    @JsonProperty("legacy_category")
    public List<String> legacyCategory;

    @JsonProperty("legacy_category_id")
    public String legacyCategoryId;

    @JsonProperty("possible_pfcs")
    public List<PfcMappingDto> possiblePfcs;
}