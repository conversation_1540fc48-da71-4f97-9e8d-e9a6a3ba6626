package com.pennypal.fintech.service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;
import org.springframework.transaction.annotation.Transactional;
import java.util.Arrays;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.dto.InvestmentDto;
import com.pennypal.fintech.dto.OverallSummaryDto;
import com.pennypal.fintech.dto.StockDataPointDto;
import com.pennypal.fintech.dto.TickerSummaryDto;
import com.pennypal.fintech.dto.DailyInvestmentStockDto;
import com.pennypal.fintech.dto.InvestmentAggregationResponseDto;
import com.pennypal.fintech.dto.AggregatedStockDataDto;
import com.pennypal.fintech.repository.InvestmentRepository;
import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.pennypal.fintech.entity.MxCustomers;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.MxCustomersRepository;
import com.pennypal.fintech.repository.UserRepository;

// import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class InvestmentMxService {
    private final InvestmentRepository investmentRepository;
    private final DailyInvestmentStockRepository dailyInvestmentStockRepository;
    private final ObjectMapper objectMapper;
    
    @Value("${mx.base-url}")
    private String baseUrl;

    @Value("${mx.client-id}")
    private String clientId;

    @Value("${mx.api-key}")
    private String apiKey;

    private final RestTemplate restTemplate;
    private final MxCustomersRepository mxCustomersRepository;
    private final UserRepository userRepository;

    public InvestmentMxService(RestTemplate restTemplate,
                          MxCustomersRepository mxCustomersRepository,
                          UserRepository userRepository,
                          InvestmentRepository investmentRepository,
                          DailyInvestmentStockRepository dailyInvestmentStockRepository) {
        this.restTemplate = restTemplate;
        this.mxCustomersRepository = mxCustomersRepository;
        this.userRepository = userRepository;
        this.investmentRepository = investmentRepository;
        this.dailyInvestmentStockRepository = dailyInvestmentStockRepository;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Sync investment data - fetch holdings and save to daily_investment_stocks
     */
    @Transactional
    public String syncInvestmentData(Integer userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }

        log.info("Syncing investment data for user ID: {}", userId);

        try {
            // Fetch holdings from MX
            String holdingsData = fetchHoldingsFromMx(userId);
            
            // Save to daily_investment_stocks and update investments table
            saveHoldingsToDatabase(holdingsData, userId);
            
            log.info("Successfully synced investment data for user: {}", userId);
            return "Investment data synced successfully";

        } catch (Exception e) {
            log.error("Error syncing investment data for user ID: {}", userId, e);
            throw new RuntimeException("Failed to sync investment data", e);
        }
    }

    /**
     * Get all investments for a specific user (returns DTOs)
     */
    public List<InvestmentDto> getUserInvestments(Integer userId) {
        List<Investment> investments = investmentRepository.findByUser_Id(userId);
        return investments.stream()
                .map(this::convertToInvestmentDto)
                .collect(Collectors.toList());
    }

    /**
     * Get daily investment stock data for a specific user (returns DTOs)
     */
    public List<DailyInvestmentStockDto> getDailyInvestmentStocks(Integer userId, LocalDate date, String ticker) {
        List<DailyInvestmentStock> dailyStocks;
        
        if (date != null && ticker != null) {
            // Filter by both date and ticker
            dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDateAndTicker(userId, date, ticker);
        } else if (date != null) {
            // Filter by date only
            dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDate(userId, date);
        } else if (ticker != null) {
            // Filter by ticker only
            dailyStocks = dailyInvestmentStockRepository.findByUserIdAndTicker(userId, ticker);
        } else {
            // Get all for user
            dailyStocks = dailyInvestmentStockRepository.findByUserId(userId);
        }
        
        return dailyStocks.stream()
                .map(this::convertToDailyInvestmentStockDto)
                .collect(Collectors.toList());
    }

    /**
     * Get investment portfolio summary for a user (returns DTOs)
     */
    public Map<String, Object> getPortfolioSummary(Integer userId) {
        List<Investment> investments = investmentRepository.findByUser_Id(userId);
        List<InvestmentDto> investmentDtos = investments.stream()
                .map(this::convertToInvestmentDto)
                .collect(Collectors.toList());
        
        // Calculate portfolio summary
        double totalValue = investments.stream()
            .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
            .sum();
            
        double totalCostBasis = investments.stream()
            .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
            .sum();
            
        double totalGainLoss = totalValue - totalCostBasis;
        double totalGainLossPercent = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0.0;
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalValue", totalValue);
        summary.put("totalCostBasis", totalCostBasis);
        summary.put("totalGainLoss", totalGainLoss);
        summary.put("totalGainLossPercent", totalGainLossPercent);
        summary.put("totalPositions", investments.size());
        
        Map<String, Object> result = new HashMap<>();
        result.put("portfolioSummary", summary);
        result.put("investments", investmentDtos);
        
        return result;
    }

    /**
     * Get investment by ticker for a specific user (returns DTO)
     */
    public InvestmentDto getInvestmentByTicker(Integer userId, String ticker) {
        List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
        Investment investment = userInvestments.stream()
            .filter(inv -> ticker.equalsIgnoreCase(inv.getTicker()))
            .findFirst()
            .orElse(null);
        
        return investment != null ? convertToInvestmentDto(investment) : null;
    }


/**
 * Convert Investment entity to InvestmentDto - UPDATED VERSION
 */
private InvestmentDto convertToInvestmentDto(Investment investment) {
    InvestmentDto dto = new InvestmentDto();
    dto.setId(investment.getId());
    dto.setUserId(investment.getUser() != null ? investment.getUser().getId() : null);
    
    // FIX: Get account ID from the account object
    dto.setAccountId(investment.getAccount() != null ? investment.getAccount().getId() : null);
    
    dto.setInvestmentGuid(investment.getInvestmentGuid());
    dto.setSecurityName(investment.getSecurityName());
    dto.setTicker(investment.getTicker());
    dto.setSecurityType(investment.getSecurityType());
    dto.setQuantity(investment.getQuantity());
    dto.setValue(investment.getValue());
    dto.setCostBasis(investment.getCostBasis());
    dto.setCurrentPrice(investment.getCurrentPrice());
    dto.setCurrencyCode(investment.getCurrencyCode());
    dto.setLastUpdated(investment.getLastUpdated());
    
    // Calculate derived fields
    if (investment.getValue() != null && investment.getCostBasis() != null) {
        dto.setTotalGain(investment.getValue() - investment.getCostBasis());
        if (investment.getCostBasis() > 0) {
            dto.setTotalGainPercent((dto.getTotalGain() / investment.getCostBasis()) * 100);
        }
    }
    
    return dto;
}
    /**
     * Convert DailyInvestmentStock entity to DailyInvestmentStockDto
     */
    private DailyInvestmentStockDto convertToDailyInvestmentStockDto(DailyInvestmentStock dailyStock) {
        DailyInvestmentStockDto dto = new DailyInvestmentStockDto();
        dto.setId(dailyStock.getId());
        dto.setInvestmentId(dailyStock.getInvestment() != null ? dailyStock.getInvestment().getId() : 0);
        dto.setUserId(dailyStock.getUserId());
        dto.setSecurityName(dailyStock.getInvestment() != null ? dailyStock.getInvestment().getSecurityName() : null);
        dto.setTicker(dailyStock.getTicker());
        dto.setSecurityType(dailyStock.getSecurityType());
        dto.setPrice(dailyStock.getPrice());
        dto.setQuantity(dailyStock.getQuantity());
        dto.setValue(dailyStock.getValue());
        dto.setPriceChange(dailyStock.getPriceChange());
        dto.setPercentChange(dailyStock.getPercentChange());
        dto.setTimestamp(dailyStock.getTimestamp());
        
        return dto;
    }

    /**
     * Fetch holdings data from MX API
     */
    private String fetchHoldingsFromMx(Integer userId) {
        MxCustomers mxCustomer = mxCustomersRepository.findByUserId(userId);
        if (mxCustomer == null) {
            throw new RuntimeException("MX customer not found for user ID: " + userId);
        }

        String userGuid = mxCustomer.getMxCustomerId();
        String url = baseUrl + "/users/" + userGuid + "/holdings";
        
        HttpHeaders headers = createHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        try {
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, entity, Map.class);
            
            if (!response.getStatusCode().is2xxSuccessful()) {
                throw new RuntimeException("Failed to get holdings: " + response.getStatusCode());
            }

            Map<String, Object> responseBody = response.getBody();
            if (responseBody == null || !responseBody.containsKey("holdings")) {
                throw new RuntimeException("Missing 'holdings' object in MX response");
            }

            return new JSONObject(responseBody).toString();

        } catch (HttpClientErrorException e) {
            log.error("MX API client error: {}", e.getResponseBodyAsString(), e);
            throw new RuntimeException("MX API error: " + e.getStatusCode(), e);
        }
    }

    /**
     * Save holdings data to daily_investment_stocks and update investments table
     */
    @Transactional
    private void saveHoldingsToDatabase(String holdingsJson, Integer userId) {
        try {
            JsonNode rootNode = objectMapper.readTree(holdingsJson);
            JsonNode holdingsArray = rootNode.get("holdings");

            if (holdingsArray != null && holdingsArray.isArray()) {
                LocalDate today = LocalDate.now();
                
                for (JsonNode holdingNode : holdingsArray) {
                    // Extract data from MX response
                    String ticker = getJsonStringValue(holdingNode, "symbol");
                    String securityType = getJsonStringValue(holdingNode, "holding_type");
                    Double currentValue = getJsonDoubleValue(holdingNode, "market_value");
                    Double purchasePrice = getJsonDoubleValue(holdingNode, "purchase_price");
                    Double quantity = getJsonDoubleValue(holdingNode, "shares");
                    Double returnPercent = getJsonDoubleValue(holdingNode, "return_percent");
                    
                    if (ticker == null || currentValue == null) {
                        continue; // Skip invalid records
                    }

                    // Save to daily_investment_stocks
                    DailyInvestmentStock dailyStock = new DailyInvestmentStock();
                    dailyStock.setUserId(userId);
                    dailyStock.setTicker(ticker);
                    dailyStock.setSecurityType(securityType);
                    dailyStock.setPrice(currentValue);
                    dailyStock.setPriceChange(purchasePrice);
                    dailyStock.setPercentChange(returnPercent);
                    dailyStock.setQuantity(quantity);
                    dailyStock.setValue(currentValue * (quantity != null ? quantity : 1));
                    dailyStock.setDate(today);
                    dailyStock.setTimestamp(LocalDateTime.now());

                    // Find existing investment by ticker and user
                    Optional<Investment> existingInvestment = findExistingInvestment(ticker, userId);

                    if (existingInvestment.isPresent()) {
                        // Update existing investment
                        Investment investment = existingInvestment.get();
                        updateInvestment(investment, holdingNode);
                        investmentRepository.save(investment);
                        
                        // Set investment_id in daily stock
                        dailyStock.setInvestment(investment);
                        
                        log.info("Updated existing investment for ticker: {}", ticker);
                    } else {
                        // Create new investment
                        Investment newInvestment = createNewInvestment(holdingNode, userId);
                        if (newInvestment != null) {
                            Investment savedInvestment = investmentRepository.save(newInvestment);
                            
                            // Set investment_id in daily stock
                            dailyStock.setInvestment(savedInvestment);
                            
                            log.info("Created new investment for ticker: {}", ticker);
                        }
                    }

                    // Save daily stock record
                    dailyInvestmentStockRepository.save(dailyStock);
                }
            }
        } catch (Exception e) {
            log.error("Error saving holdings to database", e);
            throw new RuntimeException("Failed to save holdings to database", e);
        }
    }

    /**
     * Create new investment from MX holding data
     */
   /**
 * Create new investment from MX holding data - CORRECTED VERSION
 */
private Investment createNewInvestment(JsonNode holdingNode, Integer userId) {
    try {
        Users user = userRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

        Investment investment = new Investment();
        investment.setUser(user);
        investment.setTicker(getJsonStringValue(holdingNode, "symbol"));
        investment.setSecurityName(getJsonStringValue(holdingNode, "description"));
        investment.setSecurityType(getJsonStringValue(holdingNode, "holding_type"));
        
        // Get quantity and purchase price from MX API
        Double quantity = getJsonDoubleValue(holdingNode, "shares");
        Double purchasePrice = getJsonDoubleValue(holdingNode, "purchase_price");
        Double marketValue = getJsonDoubleValue(holdingNode, "market_value");
        
        investment.setQuantity(quantity);
        investment.setAveragePurchasePrice(purchasePrice);
        investment.setCurrencyCode(getJsonStringValue(holdingNode, "currency_code"));
        investment.setCusip(getJsonStringValue(holdingNode, "cusip"));
        
        // ✅ CORRECT FORMULA: quantity * average_purchase_price = cost_basis
        if (quantity != null && purchasePrice != null) {
            investment.setCostBasis(quantity * purchasePrice);
        }
        
        // Set current market value (this is the current total value of the holding)
        investment.setValue(marketValue);
        
        // Calculate current price per share if we have market value and quantity
        if (marketValue != null && quantity != null && quantity > 0) {
            investment.setCurrentPrice(marketValue / quantity);
        }
        
        investment.setInsertDateTime(LocalDateTime.now());
        investment.setLastUpdated(LocalDateTime.now());
        
        // Calculate derived values (gain/loss, return percentage)
        investment.calculateDerivedValues();
        
        return investment;
        
    } catch (Exception e) {
        log.error("Error creating new investment", e);
        return null;
    }
}
    /**
     * Update existing investment with latest data
     */
   /**
 * Update existing investment with latest data - CORRECTED VERSION
 */
private void updateInvestment(Investment investment, JsonNode holdingNode) {
    Double quantity = getJsonDoubleValue(holdingNode, "shares");
    Double purchasePrice = getJsonDoubleValue(holdingNode, "purchase_price");
    Double marketValue = getJsonDoubleValue(holdingNode, "market_value");
    
    investment.setQuantity(quantity);
    investment.setAveragePurchasePrice(purchasePrice);
    investment.setValue(marketValue); // Current total market value
    investment.setSecurityName(getJsonStringValue(holdingNode, "description"));
    investment.setSecurityType(getJsonStringValue(holdingNode, "holding_type"));
    investment.setCurrencyCode(getJsonStringValue(holdingNode, "currency_code"));
    investment.setCusip(getJsonStringValue(holdingNode, "cusip"));
    investment.setLastUpdated(LocalDateTime.now());
    
    // ✅ CORRECT: Update cost basis = quantity * average_purchase_price
    if (quantity != null && purchasePrice != null) {
        investment.setCostBasis(quantity * purchasePrice);
    }
    
    // Calculate current price per share
    if (marketValue != null && quantity != null && quantity > 0) {
        investment.setCurrentPrice(marketValue / quantity);
    }
    
    // Calculate derived values (gain/loss, return percentage)
    investment.calculateDerivedValues();
}
    /**
     * Helper method to safely get string value from JSON node
     */
    private String getJsonStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        return (fieldNode != null && !fieldNode.isNull()) ? fieldNode.asText() : null;
    }

    /**
     * Helper method to safely get double value from JSON node
     */
    private Double getJsonDoubleValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.get(fieldName);
        if (fieldNode != null && !fieldNode.isNull()) {
            if (fieldNode.isNumber()) {
                return fieldNode.asDouble();
            } else if (fieldNode.isTextual()) {
                try {
                    return Double.parseDouble(fieldNode.asText());
                } catch (NumberFormatException e) {
                    log.warn("Could not parse double value for field {}: {}", fieldName, fieldNode.asText());
                    return null;
                }
            }
        }
        return null;
    }

    /**
     * Find existing investment by ticker and user ID
     */
    private Optional<Investment> findExistingInvestment(String ticker, Integer userId) {
        List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
        return userInvestments.stream()
            .filter(investment -> ticker.equals(investment.getTicker()))
            .findFirst();
    }

    /**
     * Create HTTP headers for MX API requests
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setBasicAuth(clientId, apiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(List.of(MediaType.valueOf("application/vnd.mx.api.v1+json")));
        return headers;
    }

    
/**
 * Get stock history for a specific ticker and time period
 */
// public Map<String, Object> getStockHistory(Integer userId, String ticker, String period) {
//     if (userId == null || ticker == null || period == null) {
//         throw new IllegalArgumentException("User ID, ticker, and period cannot be null");
//     }

//     // Calculate date range based on period
//     LocalDate toDate = LocalDate.now();
//     LocalDate fromDate = calculateFromDate(toDate, period);
    
//     log.info("Fetching stock history for user: {}, ticker: {}, period: {}, from: {} to: {}", 
//              userId, ticker, period, fromDate, toDate);

//     // Get historical data from database
//     List<DailyInvestmentStock> historicalData = dailyInvestmentStockRepository
//         .findByUserIdAndTickerAndDateBetween(userId, ticker, fromDate, toDate);
    
//     if (historicalData.isEmpty()) {
//         log.warn("No historical data found for ticker: {} and user: {}", ticker, userId);
//         return createEmptyHistoryResponse(ticker, period, fromDate, toDate);
//     }

//     // Sort by date ascending (oldest first)
//     historicalData.sort((a, b) -> a.getDate().compareTo(b.getDate()));
    
//     // Build response
//     Map<String, Object> response = new HashMap<>();
//     response.put("ticker", ticker);
//     response.put("period", period);
//     response.put("fromDate", fromDate);
//     response.put("toDate", toDate);
    
//     List<Map<String, Object>> prices = new ArrayList<>();
    
//     for (int i = 0; i < historicalData.size(); i++) {
//         DailyInvestmentStock currentData = historicalData.get(i);
//         Map<String, Object> priceData = new HashMap<>();
        
//         priceData.put("date", currentData.getDate());
//         priceData.put("price", currentData.getPrice());
//         priceData.put("value", currentData.getValue());
//         priceData.put("quantity", currentData.getQuantity());
        
//         // Calculate day-over-day percent change
//         if (i > 0) {
//             DailyInvestmentStock previousData = historicalData.get(i - 1);
//             Double percentChange = calculatePercentChange(previousData.getPrice(), currentData.getPrice());
//             priceData.put("percentChange", percentChange);
//         } else {
//             priceData.put("percentChange", null); // First day has no previous day to compare
//         }
        
//         prices.add(priceData);
//     }
    
//     response.put("prices", prices);
    
//     // Add summary statistics
//     response.put("summary", calculateSummaryStats(historicalData));
    
//     return response;
// }

/**
 * Get stock history with custom date range
 */
// public Map<String, Object> getStockHistoryByDateRange(Integer userId, String ticker, 
//                                                      LocalDate fromDate, LocalDate toDate) {
//     if (userId == null || ticker == null || fromDate == null || toDate == null) {
//         throw new IllegalArgumentException("All parameters are required");
//     }
    
//     if (fromDate.isAfter(toDate)) {
//         throw new IllegalArgumentException("From date cannot be after to date");
//     }

//     log.info("Fetching custom stock history for user: {}, ticker: {}, from: {} to: {}", 
//              userId, ticker, fromDate, toDate);

//     List<DailyInvestmentStock> historicalData = dailyInvestmentStockRepository
//         .findByUserIdAndTickerAndDateBetween(userId, ticker, fromDate, toDate);
    
//     if (historicalData.isEmpty()) {
//         return createEmptyHistoryResponse(ticker, "CUSTOM", fromDate, toDate);
//     }

//     // Sort by date ascending
//     historicalData.sort((a, b) -> a.getDate().compareTo(b.getDate()));
    
//     Map<String, Object> response = new HashMap<>();
//     response.put("ticker", ticker);
//     response.put("period", "CUSTOM");
//     response.put("fromDate", fromDate);
//     response.put("toDate", toDate);
    
//     List<Map<String, Object>> prices = new ArrayList<>();
    
//     for (int i = 0; i < historicalData.size(); i++) {
//         DailyInvestmentStock currentData = historicalData.get(i);
//         Map<String, Object> priceData = new HashMap<>();
        
//         priceData.put("date", currentData.getDate());
//         priceData.put("price", currentData.getPrice());
//         priceData.put("value", currentData.getValue());
//         priceData.put("quantity", currentData.getQuantity());
        
//         if (i > 0) {
//             DailyInvestmentStock previousData = historicalData.get(i - 1);
//             Double percentChange = calculatePercentChange(previousData.getPrice(), currentData.getPrice());
//             priceData.put("percentChange", percentChange);
//         } else {
//             priceData.put("percentChange", null);
//         }
        
//         prices.add(priceData);
//     }
    
//     response.put("prices", prices);
//     response.put("summary", calculateSummaryStats(historicalData));
    
//     return response;
// }

/**
 * Get all available stock tickers for a user
 */
// public List<String> getUserStockTickers(Integer userId) {
//     List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
//     return userInvestments.stream()
//         .map(Investment::getTicker)
//         .filter(ticker -> ticker != null && !ticker.trim().isEmpty())
//         .distinct()
//         .sorted()
//         .collect(Collectors.toList());
// }

/**
 * Calculate from date based on period
 */
// private LocalDate calculateFromDate(LocalDate toDate, String period) {
//     switch (period.toUpperCase()) {
//         case "1W":
//         case "1WEEK":
//             return toDate.minusWeeks(1);
//         case "1M":
//         case "1MONTH":
//             return toDate.minusMonths(1);
//         case "3M":
//         case "3MONTHS":
//             return toDate.minusMonths(3);
//         case "6M":
//         case "6MONTHS":
//             return toDate.minusMonths(6);
//         case "1Y":
//         case "1YEAR":
//             return toDate.minusYears(1);
//         case "YTD":
//             return LocalDate.of(toDate.getYear(), 1, 1);
//         default:
//             throw new IllegalArgumentException("Invalid period: " + period + 
//                 ". Valid periods: 1W, 1M, 3M, 6M, 1Y, YTD");
//     }
// }

/**
 * Calculate percent change between two prices
 */
// private Double calculatePercentChange(Double oldPrice, Double newPrice) {
//     if (oldPrice == null || newPrice == null || oldPrice == 0) {
//         return null;
//     }
//     return ((newPrice - oldPrice) / oldPrice) * 100;
// }

/**
 * Create empty response when no data found
 */
// private Map<String, Object> createEmptyHistoryResponse(String ticker, String period, 
//                                                       LocalDate fromDate, LocalDate toDate) {
//     Map<String, Object> response = new HashMap<>();
//     response.put("ticker", ticker);
//     response.put("period", period);
//     response.put("fromDate", fromDate);
//     response.put("toDate", toDate);
//     response.put("prices", new ArrayList<>());
//     response.put("message", "No historical data found for the specified period");
//     return response;
// }

/**
 * Calculate summary statistics for the historical data
 */
// private Map<String, Object> calculateSummaryStats(List<DailyInvestmentStock> historicalData) {
//     if (historicalData.isEmpty()) {
//         return new HashMap<>();
//     }
    
//     // Sort by date to get first and last
//     historicalData.sort((a, b) -> a.getDate().compareTo(b.getDate()));
    
//     DailyInvestmentStock firstRecord = historicalData.get(0);
//     DailyInvestmentStock lastRecord = historicalData.get(historicalData.size() - 1);
    
//     Map<String, Object> summary = new HashMap<>();
//     summary.put("startDate", firstRecord.getDate());
//     summary.put("endDate", lastRecord.getDate());
//     summary.put("startPrice", firstRecord.getPrice());
//     summary.put("endPrice", lastRecord.getPrice());
    
//     // Calculate total return percentage
//     if (firstRecord.getPrice() != null && lastRecord.getPrice() != null && firstRecord.getPrice() > 0) {
//         Double totalReturn = calculatePercentChange(firstRecord.getPrice(), lastRecord.getPrice());
//         summary.put("totalReturn", totalReturn);
//     }
    
//     // Calculate min/max prices
//     Double minPrice = historicalData.stream()
//         .map(DailyInvestmentStock::getPrice)
//         .filter(price -> price != null)
//         .min(Double::compareTo)
//         .orElse(null);
        
//     Double maxPrice = historicalData.stream()
//         .map(DailyInvestmentStock::getPrice)
//         .filter(price -> price != null)
//         .max(Double::compareTo)
//         .orElse(null);
    
//     summary.put("minPrice", minPrice);
//     summary.put("maxPrice", maxPrice);
//     summary.put("totalDataPoints", historicalData.size());
    
//     return summary;
// }


/**
 * Get aggregated investment data for a specific stock ticker with x months back and y-day intervals
 * @param userId - User ID
 * @param ticker - Stock ticker symbol
 * @param x - Number of months to look back from current date
 * @param y - Interval in days for grouping (e.g., 3 = every 3 days)
 * @return Map containing aggregated stock data with gain/loss calculations
 */
@Transactional(readOnly = true)
public Map<String, Object> getStockAggregatedData(Integer userId, String ticker, Integer x, Integer y) {
    if (userId == null || ticker == null || x == null || y == null) {
        throw new IllegalArgumentException("All parameters are required");
    }

    log.info("Fetching aggregated stock data for user: {}, ticker: {}, {} months back, {}-day intervals", 
             userId, ticker, x, y);

    LocalDate endDate = LocalDate.now();
    LocalDate startDate = endDate.minusMonths(x);

    List<Object[]> aggregatedData = dailyInvestmentStockRepository.getAggregatedStockData(userId, ticker, startDate, endDate, y);
    
    List<Map<String, Object>> stockDataPoints = new ArrayList<>();
    double totalInvestedAmount = 0.0;
    double totalCurrentValue = 0.0;
    
    for (Object[] row : aggregatedData) {
        try {
            AggregatedStockDataDto dto = new AggregatedStockDataDto(
                row[0], (Double) row[1], (Double) row[2], (Double) row[3], (Double) row[4]
            );
            
            Map<String, Object> dataPoint = new HashMap<>();
            
            Double totalValue = (dto.getAvgPrice() != null && dto.getTotalQuantity() != null) 
                ? dto.getAvgPrice() * dto.getTotalQuantity() : 0.0;
            
            dataPoint.put("date", dto.getGroupEndDate());
            dataPoint.put("avgPrice", dto.getAvgPrice());
            dataPoint.put("totalQuantity", dto.getTotalQuantity());
            dataPoint.put("totalValue", totalValue);
            dataPoint.put("avgCostBasis", dto.getAvgCostBasis());
            dataPoint.put("totalCostBasis", dto.getTotalCostBasis());
            
            // Calculate gain/loss for this period
            if (totalValue != null && dto.getTotalCostBasis() != null && dto.getTotalCostBasis() > 0) {
                double gainLoss = totalValue - dto.getTotalCostBasis();
                double gainLossPercent = (gainLoss / dto.getTotalCostBasis()) * 100;
                
                dataPoint.put("gainLoss", gainLoss);
                dataPoint.put("gainLossPercent", gainLossPercent);
                dataPoint.put("status", gainLoss >= 0 ? "GAIN" : "LOSS");
            } else {
                dataPoint.put("gainLoss", 0.0);
                dataPoint.put("gainLossPercent", 0.0);
                dataPoint.put("status", "NEUTRAL");
            }
            
            if (totalValue != null && dto.getTotalCostBasis() != null) {
                totalCurrentValue += totalValue;
                totalInvestedAmount += dto.getTotalCostBasis();
            }
            
            stockDataPoints.add(dataPoint);
            
        } catch (Exception e) {
            log.error("Error processing row data: {}", Arrays.toString(row), e);
            // Skip this row and continue processing
            continue;
        }
    }
    
    // Rest of the method remains the same...
    Map<String, Object> summary = new HashMap<>();
    summary.put("totalInvestedAmount", totalInvestedAmount);
    summary.put("totalCurrentValue", totalCurrentValue);
    
    double overallGainLoss = totalCurrentValue - totalInvestedAmount;
    double overallGainLossPercent = totalInvestedAmount > 0 ? (overallGainLoss / totalInvestedAmount) * 100 : 0.0;
    
    summary.put("overallGainLoss", overallGainLoss);
    summary.put("overallGainLossPercent", overallGainLossPercent);
    summary.put("overallStatus", overallGainLoss >= 0 ? "GAIN" : "LOSS");
    
    Map<String, Object> response = new HashMap<>();
    response.put("ticker", ticker);
    response.put("lookbackMonths", x);
    response.put("intervalDays", y);
    response.put("startDate", startDate);
    response.put("endDate", endDate);
    response.put("dataPoints", stockDataPoints);
    response.put("summary", summary);
    response.put("totalDataPoints", stockDataPoints.size());
    
    log.info("Completed aggregation for ticker: {}, found {} data points", ticker, stockDataPoints.size());
    
    return response;
}
/**
 * Helper method to parse date from various types returned by native queries
 */
private LocalDate parseDate(Object dateObj) {
    if (dateObj == null) {
        return null;
    }
    
    if (dateObj instanceof java.sql.Date) {
        return ((java.sql.Date) dateObj).toLocalDate();
    } else if (dateObj instanceof java.util.Date) {
        return ((java.util.Date) dateObj).toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate();
    } else if (dateObj instanceof String) {
        try {
            // Try parsing as ISO date first (YYYY-MM-DD)
            return LocalDate.parse((String) dateObj);
        } catch (DateTimeParseException e) {
            try {
                // Try parsing as SQL date format
                return LocalDate.parse((String) dateObj, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            } catch (DateTimeParseException e2) {
                log.error("Failed to parse date string: {}", dateObj);
                throw new IllegalArgumentException("Invalid date format: " + dateObj);
            }
        }
    } else if (dateObj instanceof LocalDate) {
        return (LocalDate) dateObj;
    } else {
        log.error("Unexpected date type: {} with value: {}", dateObj.getClass().getName(), dateObj);
        throw new IllegalArgumentException("Unsupported date type: " + dateObj.getClass().getName());
    }
}
/**
 * Get aggregated investment data for ALL stocks for a user with x months back and y-day intervals
 * @param userId - User ID
 * @param x - Number of months to look back from current date
 * @param y - Interval in days for grouping (e.g., 3 = every 3 days)
 * @return Map containing aggregated data for all user's stocks with total portfolio gain/loss
 */

@Transactional(readOnly = true)
public InvestmentAggregationResponseDto getAllStocksAggregatedData(Integer userId, Integer x, Integer y) {
    if (userId == null || x == null || y == null) {
        throw new IllegalArgumentException("All parameters are required");
    }

    log.info("Fetching aggregated data for all stocks for user: {}, {} months back, {}-day intervals", 
             userId, x, y);

    // Calculate date range: current date to x months back
    LocalDate endDate = LocalDate.now();
    LocalDate startDate = endDate.minusMonths(x);

    List<Object[]> aggregatedData = dailyInvestmentStockRepository.getAllStocksAggregatedData(userId, startDate, endDate, y);
    
    InvestmentAggregationResponseDto response = new InvestmentAggregationResponseDto(x, y, startDate, endDate);
    
    Map<String, List<StockDataPointDto>> stockDataByTicker = new HashMap<>();
    Map<String, Double> totalInvestedByTicker = new HashMap<>();
    Map<String, Double> totalCurrentValueByTicker = new HashMap<>();
    
    double overallTotalInvested = 0.0;
    double overallTotalCurrentValue = 0.0;
    
    for (Object[] row : aggregatedData) {
        String tickerSymbol = (String) row[0];
        
        // FIX: Handle the date conversion properly
        LocalDate groupEndDate;
        if (row[1] instanceof java.sql.Date) {
            groupEndDate = ((java.sql.Date) row[1]).toLocalDate();
        } else if (row[1] instanceof String) {
            groupEndDate = LocalDate.parse((String) row[1]);
        } else {
            log.warn("Unexpected date type: {}", row[1].getClass());
            continue; // Skip this row
        }
        
        Double avgPrice = (Double) row[2];
        Double totalQuantity = (Double) row[3];
        Double avgCostBasis = (Double) row[4];
        Double totalCostBasis = (Double) row[5];
        
        StockDataPointDto dataPoint = new StockDataPointDto(groupEndDate, avgPrice, totalQuantity, avgCostBasis, totalCostBasis);
        
        stockDataByTicker.computeIfAbsent(tickerSymbol, k -> new ArrayList<>()).add(dataPoint);
        
        // Accumulate totals per ticker and overall
        if (dataPoint.getTotalValue() != null && totalCostBasis != null) {
            totalCurrentValueByTicker.merge(tickerSymbol, dataPoint.getTotalValue(), Double::sum);
            totalInvestedByTicker.merge(tickerSymbol, totalCostBasis, Double::sum);
            
            overallTotalCurrentValue += dataPoint.getTotalValue();
            overallTotalInvested += totalCostBasis;
        }
    }
    
    // Create summary for each ticker
    Map<String, TickerSummaryDto> tickerSummaries = new HashMap<>();
    for (String ticker : stockDataByTicker.keySet()) {
        double invested = totalInvestedByTicker.getOrDefault(ticker, 0.0);
        double currentValue = totalCurrentValueByTicker.getOrDefault(ticker, 0.0);
        int dataPoints = stockDataByTicker.get(ticker).size();
        
        TickerSummaryDto tickerSummary = new TickerSummaryDto(invested, currentValue, dataPoints);
        tickerSummaries.put(ticker, tickerSummary);
    }
    
    // Overall portfolio summary
    OverallSummaryDto overallSummary = new OverallSummaryDto(overallTotalInvested, overallTotalCurrentValue, stockDataByTicker.size());
    
    response.setStockData(stockDataByTicker);
    response.setTickerSummaries(tickerSummaries);
    response.setOverallSummary(overallSummary);
    
    log.info("Completed aggregation for user: {}, found {} unique stocks with overall gain/loss: {}", 
             userId, stockDataByTicker.size(), overallSummary.getOverallGainLoss());
    
    return response;
}

/**
 * Get portfolio performance comparison between current period and past period
 * @param userId - User ID
 * @param currentPeriodMonths - Current period months back (e.g., 1 for last month)
 * @param pastPeriodMonths - Past period months back (e.g., 2 for 2 months ago)
 * @param intervalDays - Interval in days
 * @return Comparison data between two periods
 */
@Transactional(readOnly = true)
public Map<String, Object> getPortfolioPerformanceComparison(Integer userId, Integer currentPeriodMonths, 
                                                            Integer pastPeriodMonths, Integer intervalDays) {
    if (userId == null || currentPeriodMonths == null || pastPeriodMonths == null || intervalDays == null) {
        throw new IllegalArgumentException("All parameters are required");
    }
    
    if (pastPeriodMonths <= currentPeriodMonths) {
        throw new IllegalArgumentException("Past period months should be greater than current period months");
    }
    
    log.info("Comparing portfolio performance for user: {}, current: {} months, past: {} months", 
             userId, currentPeriodMonths, pastPeriodMonths);
 InvestmentAggregationResponseDto currentPeriodData = getAllStocksAggregatedData(userId, currentPeriodMonths, intervalDays);
    InvestmentAggregationResponseDto pastPeriodData = getAllStocksAggregatedData(userId, pastPeriodMonths, intervalDays);
    
    OverallSummaryDto currentSummary = currentPeriodData.getOverallSummary();
    OverallSummaryDto pastSummary = pastPeriodData.getOverallSummary();
    
   double currentValue = currentSummary.getTotalCurrentValue(); // assuming this method exists
    double pastValue = pastSummary.getTotalCurrentValue();
    double currentInvested = currentSummary.getTotalInvestedAmount(); // assuming this method exists
    double pastInvested = pastSummary.getTotalInvestedAmount();
    
    double valueChange = currentValue - pastValue;
    double investmentChange = currentInvested - pastInvested;
    
    Map<String, Object> comparison = new HashMap<>();
    comparison.put("currentPeriod", currentSummary);
    comparison.put("pastPeriod", pastSummary);
    comparison.put("valueChange", valueChange);
    comparison.put("investmentChange", investmentChange);
    comparison.put("valueChangePercent", pastValue > 0 ? (valueChange / pastValue) * 100 : 0.0);
    comparison.put("investmentChangePercent", pastInvested > 0 ? (investmentChange / pastInvested) * 100 : 0.0);
    comparison.put("performanceStatus", valueChange >= 0 ? "IMPROVED" : "DECLINED");
    
    log.info("Portfolio comparison completed. Value change: {}, Investment change: {}", valueChange, investmentChange);
    
    return comparison;
}



/**
 * Get stock performance summary (gain/loss) for a given period
 * @param userId - User ID
 * @param startDate - Start date of the period
 * @param endDate - End date of the period
 * @return Map containing performance summary with gain/loss calculations
 */
@Transactional(readOnly = true)
public Map<String, Object> getStockPerformanceSummary(Integer userId, LocalDate startDate, LocalDate endDate) {
    if (userId == null || startDate == null || endDate == null) {
        throw new IllegalArgumentException("All parameters are required");
    }
    
    if (startDate.isAfter(endDate)) {
        throw new IllegalArgumentException("Start date cannot be after end date");
    }

    log.info("Fetching stock performance summary for user: {} from {} to {}", userId, startDate, endDate);

    // Get all daily investment stocks for the user within the date range
    List<DailyInvestmentStock> dailyStocks = dailyInvestmentStockRepository
        .findByUserIdAndDateBetween(userId, startDate, endDate);
    
    if (dailyStocks.isEmpty()) {
        Map<String, Object> emptyResponse = new HashMap<>();
        emptyResponse.put("userId", userId);
        emptyResponse.put("startDate", startDate);
        emptyResponse.put("endDate", endDate);
        emptyResponse.put("message", "No investment data found for the specified period");
        emptyResponse.put("totalStocks", 0);
        emptyResponse.put("performanceSummary", new HashMap<>());
        return emptyResponse;
    }

    // Group by ticker for analysis
    Map<String, List<DailyInvestmentStock>> stocksByTicker = dailyStocks.stream()
        .collect(Collectors.groupingBy(DailyInvestmentStock::getTicker));
    
    List<Map<String, Object>> stockPerformances = new ArrayList<>();
    double totalStartValue = 0.0;
    double totalEndValue = 0.0;
    double totalGainLoss = 0.0;
    
    for (Map.Entry<String, List<DailyInvestmentStock>> entry : stocksByTicker.entrySet()) {
        String ticker = entry.getKey();
        List<DailyInvestmentStock> stockData = entry.getValue();
        
        // Sort by date to get start and end values
        stockData.sort((a, b) -> a.getDate().compareTo(b.getDate()));
        
        DailyInvestmentStock startRecord = stockData.get(0);
        DailyInvestmentStock endRecord = stockData.get(stockData.size() - 1);
        
        Double startValue = startRecord.getValue();
        Double endValue = endRecord.getValue();
        Double startPrice = startRecord.getPrice();
        Double endPrice = endRecord.getPrice();
        Double quantity = endRecord.getQuantity();
        
        if (startValue != null && endValue != null) {
            double gainLoss = endValue - startValue;
            double gainLossPercent = startValue > 0 ? (gainLoss / startValue) * 100 : 0.0;
            
            Map<String, Object> stockPerformance = new HashMap<>();
            stockPerformance.put("ticker", ticker);
            stockPerformance.put("startDate", startRecord.getDate());
            stockPerformance.put("endDate", endRecord.getDate());
            stockPerformance.put("startValue", startValue);
            stockPerformance.put("endValue", endValue);
            stockPerformance.put("startPrice", startPrice);
            stockPerformance.put("endPrice", endPrice);
            stockPerformance.put("quantity", quantity);
            stockPerformance.put("gainLoss", gainLoss);
            stockPerformance.put("gainLossPercent", gainLossPercent);
            stockPerformance.put("status", gainLoss >= 0 ? "GAIN" : "LOSS");
            stockPerformance.put("dataPoints", stockData.size());
            
            stockPerformances.add(stockPerformance);
            
            totalStartValue += startValue;
            totalEndValue += endValue;
            totalGainLoss += gainLoss;
        }
    }
    
    // Calculate overall portfolio performance
    double overallGainLossPercent = totalStartValue > 0 ? (totalGainLoss / totalStartValue) * 100 : 0.0;
    
    Map<String, Object> overallSummary = new HashMap<>();
    overallSummary.put("totalStartValue", totalStartValue);
    overallSummary.put("totalEndValue", totalEndValue);
    overallSummary.put("totalGainLoss", totalGainLoss);
    overallSummary.put("totalGainLossPercent", overallGainLossPercent);
    overallSummary.put("overallStatus", totalGainLoss >= 0 ? "GAIN" : "LOSS");
    overallSummary.put("totalStocks", stocksByTicker.size());
    
    Map<String, Object> response = new HashMap<>();
    response.put("userId", userId);
    response.put("startDate", startDate);
    response.put("endDate", endDate);
    response.put("stockPerformances", stockPerformances);
    response.put("overallSummary", overallSummary);
    response.put("totalStocks", stocksByTicker.size());
    
    log.info("Performance summary completed for user: {}, {} stocks analyzed, overall gain/loss: {}", 
             userId, stocksByTicker.size(), totalGainLoss);
    
    return response;
}
}