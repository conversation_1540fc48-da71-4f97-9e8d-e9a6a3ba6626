package com.pennypal.fintech.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FamilyMemberDto {
    private int relationshipId;
    private Integer secondaryUserId;
    private String name;
    private String emailId;
    private String phoneNumber;
    private String relationshipType;
    private String permissionType;
    private String status;
    private LocalDateTime createdAt;
}