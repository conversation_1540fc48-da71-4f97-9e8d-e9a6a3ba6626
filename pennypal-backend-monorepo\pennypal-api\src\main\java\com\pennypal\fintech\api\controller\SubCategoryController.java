package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.SubCategoryDto;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.service.SubCategoryService;
import com.pennypal.fintech.dto.SubCategoryIconDto;
import com.pennypal.fintech.repository.SubCategoryRepository;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.web.multipart.MultipartFile;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.Base64;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/api/v1/subCategory")
public class SubCategoryController {

    @Autowired
    private SubCategoryService subCategoryService;

     @Autowired
    private SubCategoryRepository subCategoryRepository;

    // Create a new SubCategory
    @PostMapping
    public ResponseEntity<SubCategory> createSubCategory(@RequestBody SubCategoryDto subCategoryDto) {
        SubCategory subCategory = subCategoryService.saveSubCategory(subCategoryDto);
        return new ResponseEntity<>(subCategory, HttpStatus.CREATED);
    }

    // Get all SubCategories via "/api/subcategories/all"
    @GetMapping("/all")
    public ResponseEntity<List<SubCategoryDto>> getAllSubCategories() {
        List<SubCategoryDto> subCategories = subCategoryService.getAllSubCategoriesDto();
        return ResponseEntity.ok(subCategories);
    }

    // Alternatively, if you want the default GET ("/api/subcategories") to return all, you can do:
    // @GetMapping
    // public List<SubCategory> getAllSubCategories() {
    //     return subCategoryService.getAllSubCategories();
    // }

    // Get a SubCategory by ID
    @GetMapping("/{id}")
    public ResponseEntity<SubCategory> getSubCategoryById(@PathVariable int id) {
        Optional<SubCategory> subCategory = subCategoryService.getSubCategoryById(id);
        return subCategory.map(ResponseEntity::ok)
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).build());
    }

    // Delete a SubCategory by ID
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteSubCategory(@PathVariable int id) {
        subCategoryService.deleteSubCategory(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    // Optional: Get SubCategories by Category ID
    @GetMapping("/byCategory")
    public ResponseEntity<List<SubCategory>> getSubCategoriesByCategory(@RequestParam int categoryId) {
        List<SubCategory> subCategories = subCategoryService.getSubCategoriesByCategoryId(categoryId);
        return ResponseEntity.ok(subCategories);
    }

    // Get distinct SubCategories by User ID
    @Operation(summary = "Get distinct subcategories by user ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved distinct subcategories"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/byUser/distinct/{userId}")
    public ResponseEntity<List<Map<String, Object>>> getDistinctSubCategoriesByUser(
        @Parameter(description = "ID of the user to get distinct subcategories for", required = true)
        @PathVariable Integer userId) {
        try {
            List<Map<String, Object>> subCategories = subCategoryService.getDistinctSubCategoriesByUserId(userId);
            return ResponseEntity.ok(subCategories);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

     @PutMapping("/{id}/icon")
public ResponseEntity<String> uploadSubCategoryIcon(
        @PathVariable int id,
        @RequestParam("file") MultipartFile file) {
    try {
        subCategoryService.saveSubCategoryIconBlob(id, file);
        return ResponseEntity.ok("Icon uploaded successfully");
    } catch (Exception e) {
        e.printStackTrace(); // Show exact error in logs
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error uploading icon: " + e.getMessage());
    }
}


@GetMapping("/{id}/icon")
public ResponseEntity<byte[]> getSubCategoryIcon(@PathVariable int id) {
    System.out.println("GET /subcategory/" + id + "/icon");

    Optional<SubCategory> subCategoryOpt = subCategoryService.getSubCategoryById(id);
    
    if (subCategoryOpt.isPresent()) {
        SubCategory subCategory = subCategoryOpt.get();
        System.out.println("Found subcategory with ID: " + id);

        byte[] iconBlob = subCategory.getIconBlob();
        if (iconBlob != null) {
            System.out.println("Icon blob size: " + iconBlob.length);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentLength(iconBlob.length); //  Important

            return new ResponseEntity<>(iconBlob, headers, HttpStatus.OK);
        } else {
            System.out.println("Subcategory has no icon blob");
        }
    } else {
        System.out.println("Subcategory not found with ID: " + id);
    }

    return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
}

@GetMapping("/icons")
public ResponseEntity<List<SubCategoryIconDto>> getAllSubCategoryIcons() {
    System.out.println("Fetching all subcategory icons...");

    List<SubCategory> subCategories = subCategoryService.findAllWithIcons();

    List<SubCategoryIconDto> iconDtos = subCategories.stream()
        .filter(sc -> sc.getIconBlob() != null)
        .map(sc -> {
            String base64 = Base64.getEncoder().encodeToString(sc.getIconBlob());
            return new SubCategoryIconDto(
                sc.getId(),
                sc.getSubCategory(),
                base64
            );
        })
        .collect(Collectors.toList());

    return ResponseEntity.ok(iconDtos);
}
}
