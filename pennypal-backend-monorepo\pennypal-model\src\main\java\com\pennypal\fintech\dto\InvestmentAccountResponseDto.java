package com.pennypal.fintech.dto;
import lombok.Data;
import java.util.List;
@Data
public class InvestmentAccountResponseDto {
    private String id;
    private String name;
    private String type;
    private String accountType;
    private Double balance;
    private String accountNumberDisplay;
    private String displayName;
    private String status;
    private String currency;
    private Long lastUpdated;
    private Long createdDate;
    private List<HoldingDto> holdings; // Changed to generic HoldingDto
}