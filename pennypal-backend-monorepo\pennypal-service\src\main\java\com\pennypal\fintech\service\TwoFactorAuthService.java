package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.TwoFactorAuthDto;
import com.pennypal.fintech.entity.TwoFactorAuth;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.TwoFactorAuthRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class TwoFactorAuthService {
    
    @Autowired
    private TwoFactorAuthRepository twoFactorAuthRepository;
    
    @Autowired
    private UserRepository userRepository;
    
    private static final String ISSUER = "PennyPal";
    private static final int SECRET_LENGTH = 32;
    private static final int BACKUP_CODES_COUNT = 10;
    
    public TwoFactorAuthDto setupTwoFactorAuth(Integer userId) {
        log.info("Setting up 2FA for user: {}", userId);
        
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Check if 2FA already exists
        Optional<TwoFactorAuth> existing = twoFactorAuthRepository.findByUserId(userId);
        if (existing.isPresent() && existing.get().getIsEnabled()) {
            throw new RuntimeException("Two-factor authentication is already enabled");
        }
        
        // Generate secret key
        String secretKey = generateSecretKey();
        
        // Generate backup codes
        List<String> backupCodes = generateBackupCodes();
        
        // Create or update 2FA record
        TwoFactorAuth twoFactorAuth;
        if (existing.isPresent()) {
            twoFactorAuth = existing.get();
            twoFactorAuth.setSecretKey(secretKey);
            twoFactorAuth.setBackupCodes(String.join(",", backupCodes));
        } else {
            twoFactorAuth = new TwoFactorAuth();
            twoFactorAuth.setUserId(userId);
            twoFactorAuth.setSecretKey(secretKey);
            twoFactorAuth.setBackupCodes(String.join(",", backupCodes));
            twoFactorAuth.setIsEnabled(false);
        }
        
        twoFactorAuth = twoFactorAuthRepository.save(twoFactorAuth);
        
        // Generate QR code URL
        String qrCodeUrl = generateQRCodeUrl(user.getEmailId(), secretKey);
        
        TwoFactorAuthDto dto = new TwoFactorAuthDto();
        dto.setUserId(userId);
        dto.setSecret(twoFactorAuth.getSecretKey());
        dto.setQrCodeUrl(qrCodeUrl);
        dto.setBackupCodes(String.join(",", backupCodes)); // Fixed: removed duplicate line
        dto.setIsEnabled(false);
        
        return dto;
    }
    
    @Transactional
    public boolean enableTwoFactorAuth(Integer userId, String verificationCode) {
        log.info("Enabling 2FA for user: {}", userId);
        
        TwoFactorAuth twoFactorAuth = twoFactorAuthRepository.findByUserId(userId)
                .orElseThrow(() -> new RuntimeException("2FA setup not found. Please setup 2FA first."));
        
        if (verifyTOTP(twoFactorAuth.getSecretKey(), verificationCode)) {
            twoFactorAuth.setIsEnabled(true);
            twoFactorAuth.setEnabledAt(LocalDateTime.now());
            twoFactorAuthRepository.save(twoFactorAuth);
            return true;
        }
        return false;
    }
    
    @Transactional
    public boolean disableTwoFactorAuth(Integer userId, String verificationCode) {
        log.info("Disabling 2FA for user: {}", userId);
        
        TwoFactorAuth twoFactorAuth = twoFactorAuthRepository.findEnabledByUserId(userId)
                .orElseThrow(() -> new RuntimeException("2FA is not enabled"));
        
        if (verifyTOTP(twoFactorAuth.getSecretKey(), verificationCode) || 
            verifyBackupCode(twoFactorAuth, verificationCode)) {
            twoFactorAuth.setIsEnabled(false);
            twoFactorAuthRepository.save(twoFactorAuth);
            return true;
        }
        return false;
    }
    
    public boolean verifyTwoFactorCode(Integer userId, String code) {
        Optional<TwoFactorAuth> twoFactorAuth = twoFactorAuthRepository.findEnabledByUserId(userId);
        if (twoFactorAuth.isEmpty()) {
            return true; // 2FA not enabled, skip verification
        }
        
        return verifyTOTP(twoFactorAuth.get().getSecretKey(), code) ||
               verifyBackupCode(twoFactorAuth.get(), code);
    }
    
    public boolean isTwoFactorEnabled(Integer userId) {
        return twoFactorAuthRepository.findEnabledByUserId(userId).isPresent();
    }
    
    // FIXED: Updated getTwoFactorStatus method to include proper values
    public TwoFactorAuthDto getTwoFactorStatus(Integer userId) {
        Optional<TwoFactorAuth> twoFactorAuthOpt = twoFactorAuthRepository.findByUserId(userId);
        
        TwoFactorAuthDto dto = new TwoFactorAuthDto();
        dto.setUserId(userId);
        
        if (twoFactorAuthOpt.isPresent()) {
            TwoFactorAuth tfa = twoFactorAuthOpt.get();
            dto.setIsEnabled(tfa.getIsEnabled());
            dto.setAuthMethod(tfa.getAuthMethod());
            dto.setEnabledAt(tfa.getEnabledAt());
            
            // Only include sensitive information if 2FA is not enabled yet (setup phase)
            if (!tfa.getIsEnabled()) {
                // Get user for QR code generation
                Users user = userRepository.findById(userId)
                        .orElseThrow(() -> new RuntimeException("User not found"));
                
                dto.setSecret(tfa.getSecretKey());
                dto.setQrCodeUrl(generateQRCodeUrl(user.getEmailId(), tfa.getSecretKey()));
                dto.setBackupCodes(tfa.getBackupCodes());
            } else {
                // For security, don't expose secret key when 2FA is enabled
                dto.setSecret(null);
                dto.setQrCodeUrl(null);
                // Show backup codes count instead of actual codes
                if (tfa.getBackupCodes() != null && !tfa.getBackupCodes().trim().isEmpty()) {
                    String[] codes = tfa.getBackupCodes().split(",");
                    long activeCodesCount = Arrays.stream(codes)
                            .filter(code -> !code.trim().isEmpty())
                            .count();
                    dto.setBackupCodes("Active backup codes: " + activeCodesCount);
                } else {
                    dto.setBackupCodes("No backup codes available");
                }
            }
        } else {
            dto.setIsEnabled(false);
            dto.setSecret(null);
            dto.setQrCodeUrl(null);
            dto.setBackupCodes(null);
            dto.setEnabledAt(null);
            dto.setAuthMethod(null);
        }
        
        return dto;
    }
    
    // Alternative method to get setup information (for initial setup only)
    public TwoFactorAuthDto getSetupInformation(Integer userId) {
        Optional<TwoFactorAuth> twoFactorAuthOpt = twoFactorAuthRepository.findByUserId(userId);
        
        if (twoFactorAuthOpt.isEmpty() || twoFactorAuthOpt.get().getIsEnabled()) {
            throw new RuntimeException("No setup information available");
        }
        
        TwoFactorAuth tfa = twoFactorAuthOpt.get();
        Users user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        TwoFactorAuthDto dto = new TwoFactorAuthDto();
        dto.setUserId(userId);
        dto.setSecret(tfa.getSecretKey());
        dto.setQrCodeUrl(generateQRCodeUrl(user.getEmailId(), tfa.getSecretKey()));
        dto.setBackupCodes(tfa.getBackupCodes());
        dto.setIsEnabled(false);
        dto.setAuthMethod(tfa.getAuthMethod());
        
        return dto;
    }
    
    private String generateSecretKey() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[20]; // 160 bits for TOTP
        random.nextBytes(bytes);
        
        // Convert to Base32 (RFC 4648)
        return encodeBase32(bytes);
    }

    // Add this helper method for Base32 encoding
    private String encodeBase32(byte[] bytes) {
        final String BASE32_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < bytes.length; i += 5) {
            long buffer = 0;
            int bytesInBuffer = Math.min(5, bytes.length - i);
            
            for (int j = 0; j < bytesInBuffer; j++) {
                buffer = (buffer << 8) | (bytes[i + j] & 0xFF);
            }
            
            // Pad with zeros if needed
            buffer <<= (5 - bytesInBuffer) * 8;
            
            // Extract 5-bit groups
            for (int j = 0; j < Math.ceil(bytesInBuffer * 8.0 / 5); j++) {
                int index = (int) ((buffer >> (35 - 5 * j)) & 0x1F);
                result.append(BASE32_CHARS.charAt(index));
            }
        }
        
        return result.toString();
    }

    // Add this helper method for Base32 decoding
    private byte[] decodeBase32(String base32) {
        final String BASE32_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";
        base32 = base32.toUpperCase().replaceAll("[^A-Z2-7]", "");
        
        if (base32.length() == 0) {
            return new byte[0];
        }
        
        // Remove padding
        base32 = base32.replaceAll("=", "");
        
        int length = base32.length();
        byte[] result = new byte[length * 5 / 8];
        
        long buffer = 0;
        int bitsInBuffer = 0;
        int resultIndex = 0;
        
        for (char c : base32.toCharArray()) {
            int value = BASE32_CHARS.indexOf(c);
            if (value == -1) {
                throw new IllegalArgumentException("Invalid Base32 character: " + c);
            }
            
            buffer = (buffer << 5) | value;
            bitsInBuffer += 5;
            
            if (bitsInBuffer >= 8) {
                result[resultIndex++] = (byte) (buffer >> (bitsInBuffer - 8));
                bitsInBuffer -= 8;
            }
        }
        
        return java.util.Arrays.copyOf(result, resultIndex);
    }

    // Replace your generateTOTP() method with this:
    private String generateTOTP(String secret, long timeWindow) throws Exception {
        byte[] key = decodeBase32(secret); // Use Base32 decoding instead of Base64
        byte[] data = new byte[8];
        long value = timeWindow;
        
        for (int i = 8; i-- > 0; value >>>= 8) {
            data[i] = (byte) value;
        }
        
        SecretKeySpec signKey = new SecretKeySpec(key, "HmacSHA1");
        Mac mac = Mac.getInstance("HmacSHA1");
        mac.init(signKey);
        byte[] hash = mac.doFinal(data);
        
        int offset = hash[20 - 1] & 0xF;
        long truncatedHash = 0;
        
        for (int i = 0; i < 4; ++i) {
            truncatedHash <<= 8;
            truncatedHash |= (hash[offset + i] & 0xFF);
        }
        
        truncatedHash &= 0x7FFFFFFF;
        truncatedHash %= 1000000;
        
        return String.format("%06d", truncatedHash);
    }
    
    private List<String> generateBackupCodes() {
        List<String> codes = new ArrayList<>();
        SecureRandom random = new SecureRandom();
        
        for (int i = 0; i < BACKUP_CODES_COUNT; i++) {
            int code = 100000 + random.nextInt(900000); // 6-digit codes
            codes.add(String.valueOf(code));
        }
        
        return codes;
    }
    
    private String generateQRCodeUrl(String email, String secret) {
        return String.format("otpauth://totp/%s:%s?secret=%s&issuer=%s",
                ISSUER, email, secret, ISSUER);
    }
    
    private boolean verifyTOTP(String secret, String code) {
        try {
            long timeWindow = System.currentTimeMillis() / 30000;
            
            // Check current time window and previous one (for clock skew)
            for (int i = -1; i <= 1; i++) {
                if (generateTOTP(secret, timeWindow + i).equals(code)) {
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Error verifying TOTP", e);
            return false;
        }
    }
    
    private boolean verifyBackupCode(TwoFactorAuth twoFactorAuth, String code) {
        if (twoFactorAuth.getBackupCodes() == null) {
            return false;
        }
        
        String[] codes = twoFactorAuth.getBackupCodes().split(",");
        for (String backupCode : codes) {
            if (backupCode.trim().equals(code)) {
                // Remove used backup code
                String updatedCodes = twoFactorAuth.getBackupCodes().replace(code, "");
                twoFactorAuth.setBackupCodes(updatedCodes);
                twoFactorAuthRepository.save(twoFactorAuth);
                return true;
            }
        }
        return false;
    }
}