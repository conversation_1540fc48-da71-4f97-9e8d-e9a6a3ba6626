package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;

import jakarta.persistence.Column;

import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

import java.math.BigDecimal;

@Entity
@Data
@Table(name = "receipt_items")
public class ReceiptItems {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @Column(name = "receipt_id")
    private int receiptId;

    private String item;

    @Column(name = "item_type")
    private String itemType;

     @Column(name = "category")  // <-- new column
    private String category;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "weight")
    private BigDecimal weight;

    @Column(name = "price")
    private BigDecimal price;

   

    @Override
    public String toString() {
        return "ReceiptItem{" +
                "id=" + id +
                ", receiptId=" + receiptId +
                ", item='" + item + '\'' +
                ", itemType='" + itemType + '\'' +
                ", category='" + category + '\'' + 
                ", quantity=" + quantity +
                ", weight=" + weight +
                ", price=" + price +
                '}';
    }
    
}
