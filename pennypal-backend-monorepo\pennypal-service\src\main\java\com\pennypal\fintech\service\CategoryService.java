package com.pennypal.fintech.service;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

import com.pennypal.fintech.dto.CategoryMappingDto;
import com.pennypal.fintech.dto.PlaidPfcMappingDto;
import com.pennypal.fintech.entity.CategoryMapping;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.SubCategory;
import com.pennypal.fintech.repository.CategoryMappingRepository;
import com.pennypal.fintech.repository.CategoryRepository;
import com.pennypal.fintech.repository.SubCategoryRepository;

import java.net.URL;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Transactional
@Slf4j
public class CategoryService {

    private static final String URL = "https://plaid.com/documents/transactions-personal-finance-category-mapping.json";

    @Autowired
    private final CategoryRepository categoryRepository;

    @Autowired
    private CategoryMappingRepository categoryMappingRepository;

    @Autowired
    private SubCategoryRepository subCategoryRepository;

    @Autowired
    private ObjectMapper mapper;

    @Autowired
    private RestTemplate restTemplate;
   
    public CategoryService(CategoryRepository categoryRepository) {
        this.categoryRepository = categoryRepository;
    }

   
   
    /**
     * Get category by ID
     */
    @Cacheable(value = "categoryCache",
               key = "#root.methodName" + "_" + "#id",
               unless = "#result == null")
    public Category getCategoryById(Integer id) {
        return categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Category not found with id: " + id));
    }

    /**
     * Get all categories
     */
    @Cacheable(value = "categoryCache",
               key = "#root.methodName",
               unless = "#result.size() == 0")
    public List<Category> getAllCategories() {
        return categoryRepository.findAllByOrderByCategoryAsc();
    }

    @Cacheable(value = "categoryCache",
               key = "#root.methodName",
               unless = "#result.size() == 0")
    public List<SubCategory> getAllCategoriesByCategory(String category) {
        return categoryRepository.getCategoryListByCategory(category);
    }
    /**
     * Get category by name
     */
    @Cacheable(value = "categoryCache",
               key = "#root.methodName",
               unless = "#result == null")
    public Optional<Category> getCategoryByName(String categoryName) {
        return categoryRepository.findByCategoryIgnoreCase(categoryName);
    }

    /**
     * Search categories by partial name match
     */
    @Cacheable(value = "categoryCache",
               key = "#root.methodName",
               unless = "#result.size() == 0")
    public List<Category> searchCategories(String searchTerm) {
        return categoryRepository.findByCategoryContainingIgnoreCase(searchTerm);
    }

    /**
     * Get all distinct category names
     */
    @Cacheable(value = "categoryCache",
               key = "#root.methodName",
               unless = "#result.size() == 0")
    public List<String> getDistinctCategories() {
        return categoryRepository.findDistinctCategories();
    }

    @Caching(evict = {
        @CacheEvict(value = "categoryCache", key = "getCategoryById" + "_" + "#categoryId"),
        @CacheEvict(value = "categoryCache", key = "getAllCategories"),
        @CacheEvict(value = "categoryCache", key = "getDistinctCategories")
    })
    public void updateCategoryIconKey(Integer categoryId, String iconKey) {
        Category category = categoryRepository.findById(categoryId)
            .orElseThrow(() -> new RuntimeException("Category not found with id: " + categoryId));
        category.setCategoryIconKey(iconKey);
        categoryRepository.save(category);
    }

    /**
     * Delete category by ID
     */
    @Caching(evict = {
        @CacheEvict(value = "categoryCache", key = "getCategoryById" + "_" + "#id"),
        @CacheEvict(value = "categoryCache", key = "getAllCategories"),
        @CacheEvict(value = "categoryCache", key = "getDistinctCategories")
    })
    public void deleteCategory(Integer id) {
        Category category = categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Category not found with id: " + id));
        categoryRepository.delete(category);
    }

    private String formatCategory(String category) {
        String[] words = category.replace("_", " ").toLowerCase().split(" ");
        StringBuilder sb = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                sb.append(Character.toUpperCase(word.charAt(0)))
                  .append(word.substring(1))
                  .append(" ");
            }
        }
        return sb.toString().trim();
    }

    @Caching(evict = {
        @CacheEvict(value = "categoryCache", key = "getAllCategories"),
        @CacheEvict(value = "categoryCache", key = "getDistinctCategories")
    })
    public void processAndStorePlaidPFC() throws Exception {
        List<PlaidPfcMappingDto> entries = mapper.readValue(new URL(URL), new TypeReference<>() {});

        Map<String, CategoryMapping> existingMappings = categoryMappingRepository.findAll().stream()
                .collect(Collectors.toMap(CategoryMapping::getLegacyCategoryId, c -> c));

        // Fetch existing categories and subcategories
        Map<String, Category> existingCategories = categoryRepository.findAll().stream()
                .collect(Collectors.toMap(Category::getCategory, c -> c));

        Map<String, SubCategory> existingSubCategories = subCategoryRepository.findAll().stream()
                .collect(Collectors.toMap(SubCategory::getSubCategory, sc -> sc));

        Set<String> uniqueFormattedPrimaries = new HashSet<>();
        Map<String, String> formattedDetailedToPrimary = new HashMap<>();

        List<CategoryMapping> toSave = new ArrayList<>();

        for (PlaidPfcMappingDto entry : entries) {
            if (entry.possiblePfcs == null || entry.possiblePfcs.isEmpty()) continue;

            String latestLegacyCategory = entry.legacyCategory.get(entry.legacyCategory.size() - 1);
            String firstLegacyCategory = entry.legacyCategory.get(0);
            
            String primaryRaw = entry.possiblePfcs.get(0).primary;
            String detailedRaw = entry.possiblePfcs.get(0).detailed;

            String primary = formatCategory(primaryRaw);
            String detailed = formatCategory(detailedRaw.replaceFirst(primaryRaw + "_", ""));

            uniqueFormattedPrimaries.add(primary);
            formattedDetailedToPrimary.put(detailed, primary);

            CategoryMapping mapping = existingMappings.getOrDefault(entry.legacyCategoryId, new CategoryMapping());
            mapping.setLegacyCategoryId(entry.legacyCategoryId);
            mapping.setLegacyCategory(latestLegacyCategory);
            mapping.setLegacyCategoryParent(firstLegacyCategory);
            mapping.setPrimaryPfc(primaryRaw);
            mapping.setDetailedPfc(detailedRaw);
            mapping.setPrimaryPfcClean(primary);
            mapping.setDetailedPfcClean(detailed);

            toSave.add(mapping);
        }

        LocalDateTime now = LocalDateTime.now();

        // Save new categories
        List<Category> newCategories = uniqueFormattedPrimaries.stream()
                .filter(category -> !existingCategories.containsKey(category))
                .map(category -> {
                    Category c = new Category();
                    c.setCategory(category);
                    c.setInsertDatetime(now);
                    c.setUpdateDatetime(now);
                    return c;
                })
                .collect(Collectors.toList());

        categoryRepository.saveAll(newCategories);
        // Update the existing categories map with the new categories
        newCategories.forEach(c -> existingCategories.put(c.getCategory(), c));

        // Save new subcategories
        List<SubCategory> newSubCategories = formattedDetailedToPrimary.entrySet().stream()
                .filter(e -> !existingSubCategories.containsKey(e.getKey()))
                .map(e -> {
                    SubCategory sc = new SubCategory();
                    sc.setSubCategory(e.getKey());
                    sc.setCategory(existingCategories.get(e.getValue()));
                    return sc;
                })
                .collect(Collectors.toList());

        subCategoryRepository.saveAll(newSubCategories);

        // Save all mappings
        categoryMappingRepository.saveAll(toSave);

        log.info("Plaid PFC mappings processed and stored successfully!");

        // Call Python and save
        try {
            String url = "http://localhost:5000/store-category";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    String.class
            );
            log.info("Python response: {}", response.getBody());
        } catch (Exception e) {
            log.error("Error calling Python: {}", e.getMessage(), e);
        }
    }

    public List<CategoryMappingDto> getAllMappings() {
        return categoryMappingRepository.findAll().stream()
                .map(c -> new CategoryMappingDto(c.getLegacyCategoryId(), c.getLegacyCategory(), c.getLegacyCategoryParent(), c.getPrimaryPfc(), c.getDetailedPfc(), c.getPrimaryPfcClean(), c.getDetailedPfcClean()))
                .collect(Collectors.toList());
    }

    public Map<String, String> fetchPfcMappings(Set<String> finicityCategories, String source) {
        if (finicityCategories == null || finicityCategories.isEmpty()) {
            return Collections.emptyMap();
        }
        String url = "http://localhost:5000/search-category";

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // Request body
            Map<String, Object> body = Map.of("queries", finicityCategories);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

            ResponseEntity<Map<String, String>> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    entity,
                    new ParameterizedTypeReference<Map<String, String>>() {}
            );

            // Save finicity_category
            if (response.getBody() != null && source.equals("finicity")) {
                for (Map.Entry<String, String> entry : response.getBody().entrySet()) {
                    String finicityCategory = entry.getKey();
                    String pfcCategory = entry.getValue();
                    Optional<SubCategory> subCategory = subCategoryRepository.findBySubCategory(pfcCategory);
                    if (subCategory.isPresent()) {
                        subCategory.get().setFinicityCategory(finicityCategory);
                        subCategoryRepository.save(subCategory.get());
                    }
                }
            }

            // Save mx_category
            if (response.getBody() != null && source.equals("mx")) {
                for (Map.Entry<String, String> entry : response.getBody().entrySet()) {
                    String mxCategory = entry.getKey();
                    String pfcCategory = entry.getValue();
                    Optional<SubCategory> subCategory = subCategoryRepository.findBySubCategory(pfcCategory);
                    if (subCategory.isPresent()) {
                        subCategory.get().setMxCategory(mxCategory);
                        subCategoryRepository.save(subCategory.get());
                    }
                }
            }

            return response.getBody() != null ?
                response.getBody() : Collections.emptyMap();
        } catch (Exception e) {
            log.error("Error fetching PFC mappings: {}", e.getMessage(), e);
            return Collections.emptyMap();
        }
    }

}