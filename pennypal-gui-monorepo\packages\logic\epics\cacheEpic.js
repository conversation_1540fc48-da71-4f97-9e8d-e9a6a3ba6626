import { ofType, combineEpics } from 'redux-observable';
import { from, of, merge } from 'rxjs';
import { catchError, map, mergeMap, filter, switchMap, delay } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure,
  fetchSubCategoryIconsStart,
  fetchSubCategoryIconsSuccess,
  fetchSubCategoryIconsFailure,
  fetchCategoriesStart,
  fetchCategoriesSuccess,
  fetchCategoriesFailure,
  fetchSubcategoriesStart,
  fetchSubcategoriesSuccess,
  fetchSubcategoriesFailure,
  fetchTransactionsStart,
  fetchTransactionsSuccess,
  fetchTransactionsFailure,
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailure,
  fetchFutureRecurringTransactionsStart,
  fetchFutureRecurringTransactionsSuccess,
  fetchFutureRecurringTransactionsFailure,
  fetchBudgetSummaryStart,
  fetchBudgetSummarySuccess,
  fetchBudgetSummaryFailure,
  fetchBudgetDataStart,
  fetchBudgetDataSuccess,
  fetchBudgetDataFailure,
  fetchTransactionSummaryStart,
  fetchTransactionSummarySuccess,
  fetchTransactionSummaryFailure,
  fetchHiddenTransactionsStart,
  fetchHiddenTransactionsSuccess,
  fetchHiddenTransactionsFailure,
  fetchReconcileDataStart,
  fetchReconcileDataSuccess,
  fetchReconcileDataFailure,
  fetchReceiptTransactionIdsStart,
  fetchReceiptTransactionIdsSuccess,
  fetchReceiptTransactionIdsFailure,
  fetchReceiptItemsStart,
  fetchReceiptItemsSuccess,
  fetchReceiptItemsFailure,
  fetchReceiptSummaryStart,
  fetchReceiptSummarySuccess,
  fetchReceiptSummaryFailure,
  fetchUserReceiptsStart,
  fetchUserReceiptsSuccess,
  fetchUserReceiptsFailure,
  fetchChatbotHistoryStart,
  fetchChatbotHistorySuccess,
  fetchChatbotHistoryFailure,
  fetchDistinctSubcategoriesStart,
  fetchDistinctSubcategoriesSuccess,
  fetchDistinctSubcategoriesFailure,
  fetchAccountIdsStart,
  fetchAccountIdsSuccess,
  fetchAccountIdsFailure,
  fetchUserAccountsStart,
  fetchUserAccountsSuccess,
  fetchUserAccountsFailure,
  fetchAccountBalancesInvestmentStart,
  fetchAccountBalancesInvestmentSuccess,
  fetchAccountBalancesInvestmentFailure,
  fetchAccountBalancesDepositoryStart,
  fetchAccountBalancesDepositorySuccess,
  fetchAccountBalancesDepositoryFailure,
  fetchAccountBalancesLoanStart,
  fetchAccountBalancesLoanSuccess,
  fetchAccountBalancesLoanFailure,
  fetchAccountBalancesCreditStart,
  fetchAccountBalancesCreditSuccess,
  fetchAccountBalancesCreditFailure,
  fetchPaymentSubscriptionStart,
  fetchPaymentSubscriptionSuccess,
  fetchPaymentSubscriptionFailure,
  fetchPaymentMethodsStart,
  fetchPaymentMethodsSuccess,
  fetchPaymentMethodsFailure,
  fetchPaymentProductsStart,
  fetchPaymentProductsSuccess,
  fetchPaymentProductsFailure,
  fetchPaymentInvoicesStart,
  fetchPaymentInvoicesSuccess,
  fetchPaymentInvoicesFailure,
  fetchUpcomingInvoiceStart,
  fetchUpcomingInvoiceSuccess,
  fetchUpcomingInvoiceFailure
} from '../redux/cacheSlice';
import {
  signInSuccess,
  googleSignInSuccess,
  otpVerifySuccess,
  registrationSuccess
} from '../redux/authSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

// Epic to initialize cache on login success
export const initializeCacheOnLoginEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      signInSuccess.type,
      googleSignInSuccess.type,
      otpVerifySuccess.type,
      registrationSuccess.type
    ),
    filter((action) => {
      // Only initialize if user is authenticated and has tokens
      return action.payload?.jwtToken || action.payload?.user?.jwtToken;
    }),
    switchMap(() => {
      try {
        const state = state$.value;
        const cache = state?.cache;

        // Safety check - ensure cache state exists
        if (!cache) {
          console.log('⚠️ Cache state not initialized yet, skipping cache initialization');
          return of({ type: 'cache/initializeSkipped' });
        }

        // Check if we already have all data cached
        const allDataCached = cache.iconsLoaded &&
                             cache.subCategoryIconsLoaded &&
                             cache.categoriesLoaded &&
                             cache.subcategoriesLoaded;

        if (allDataCached) {
          console.log('✅ All cache data already loaded, skipping initialization');
          return of({ type: 'cache/initializeComplete' });
        }

        // Get user ID using the same method as other epics
        const userId = getCurrentUserId();
        if (!userId) {
          return of({ type: 'cache/initializeSkipped' });
        }

        // Get current date for budget summary
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1; // JavaScript months are 0-based

        // Dispatch individual start actions for each API
        return merge(
          of(fetchIconsStart()).pipe(delay(500)),
          of(fetchSubCategoryIconsStart()).pipe(delay(500)),
          of(fetchCategoriesStart()).pipe(delay(500)),
          of(fetchSubcategoriesStart()).pipe(delay(500)),
          of(fetchTransactionsStart()).pipe(delay(500)),
          of(fetchRecurringTransactionsStart()).pipe(delay(500)),
          of(fetchFutureRecurringTransactionsStart()).pipe(delay(500)),
          of(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth })).pipe(delay(500)),
          of(fetchBudgetDataStart({ userId })).pipe(delay(500)),
          of(fetchTransactionSummaryStart({ userId })).pipe(delay(500)),
          of(fetchHiddenTransactionsStart({ userId })).pipe(delay(500)),
          of(fetchReconcileDataStart()).pipe(delay(500)),
          of(fetchReceiptTransactionIdsStart()).pipe(delay(500)),
          of(fetchReceiptItemsStart()).pipe(delay(500)),
          of(fetchReceiptSummaryStart()).pipe(delay(500)),
          of(fetchUserReceiptsStart({ userId })).pipe(delay(500)),
          of(fetchChatbotHistoryStart({ userId })).pipe(delay(500)),
          of(fetchDistinctSubcategoriesStart({ userId })).pipe(delay(500)),
          of(fetchAccountIdsStart({ userId })).pipe(delay(500)),
          of(fetchUserAccountsStart({ userId })).pipe(delay(500)),
          of(fetchAccountBalancesInvestmentStart({ userId, x: 12, y: 30 })).pipe(delay(500)),
          of(fetchAccountBalancesDepositoryStart({ userId, x: 12, y: 30 })).pipe(delay(500)),
          of(fetchAccountBalancesLoanStart({ userId, x: 12, y: 30 })).pipe(delay(500)),
          of(fetchAccountBalancesCreditStart({ userId, x: 12, y: 30 })).pipe(delay(500)),
          of(fetchPaymentSubscriptionStart({ userId })).pipe(delay(500)),
          of(fetchPaymentMethodsStart({ userId })).pipe(delay(500)),
          of(fetchPaymentProductsStart({})).pipe(delay(500)),
          of(fetchPaymentInvoicesStart({ userId })).pipe(delay(500))
          // Note: fetchUpcomingInvoiceStart requires customerId and subscriptionId from subscription data
          // It will be called separately after subscription is fetched
        );
      } catch (error) {
        console.error('❌ Error in cache initialization:', error);
        return of({ type: 'cache/initializeError', payload: error.message });
      }
    })
  );

// Epic for fetching icons
export const fetchIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchIconsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.iconsLoaded;
        console.log('🔍 Icons epic filter check:', {
          cacheExists: !!cache,
          iconsLoaded: cache?.iconsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for icons:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting icons API call...');
      return from(axiosInstance.get('/pennypal/api/icons/list')).pipe(
        map((response) => {
          console.log('✅ Icons fetched successfully:', response.data?.length || 0, 'items');
          return fetchIconsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch icons';
          return of(fetchIconsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching subcategory icons
export const fetchSubCategoryIconsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubCategoryIconsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.subCategoryIconsLoaded;
        console.log('🔍 SubCategory icons epic filter check:', {
          cacheExists: !!cache,
          subCategoryIconsLoaded: cache?.subCategoryIconsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for subcategory icons:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting subcategory icons API call...');
      return from(axiosInstance.get('/pennypal/api/v1/subCategory/icons')).pipe(
        map((response) => {
          console.log('✅ Subcategory icons fetched successfully:', response.data?.length || 0, 'items');
          // Process the icons to ensure they have the correct format
          const processedIcons = (response.data || []).filter(dto =>
            dto.subCategoryId && dto.base64Icon
          );
          return fetchSubCategoryIconsSuccess(processedIcons);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategory icons:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategory icons';
          return of(fetchSubCategoryIconsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching categories
export const fetchCategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchCategoriesStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.categoriesLoaded;
        console.log('🔍 Categories epic filter check:', {
          cacheExists: !!cache,
          categoriesLoaded: cache?.categoriesLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for categories:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting categories API call...');
      return from(axiosInstance.get('/pennypal/api/v1/category/all')).pipe(
        map((response) => {
          console.log('✅ Categories fetched successfully:', response.data?.length || 0, 'items');
          return fetchCategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch categories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch categories';
          return of(fetchCategoriesFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching subcategories
export const fetchSubcategoriesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchSubcategoriesStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        // Only check if data is already loaded, don't check loading state
        const shouldFetch = cache && !cache.subcategoriesLoaded;
        console.log('🔍 Subcategories epic filter check:', {
          cacheExists: !!cache,
          subcategoriesLoaded: cache?.subcategoriesLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for subcategories:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting subcategories API call...');
      return from(axiosInstance.get('/pennypal/api/v1/subCategory/all')).pipe(
        map((response) => {
          console.log('✅ Subcategories fetched successfully:', response.data?.length || 0, 'items');
          return fetchSubcategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch subcategories:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch subcategories';
          return of(fetchSubcategoriesFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching transactions
export const fetchTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.transactionsLoaded;
        console.log('🔍 Transactions epic filter check:', {
          cacheExists: !!cache,
          transactionsLoaded: cache?.transactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for transactions API');
        return of(fetchTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/transactions/user/${userId}?page=0&pageSize=250`)).pipe(
        map((response) => {
          console.log('✅ Transactions fetched successfully:', response.data?.content?.length || 0, 'items');
          return fetchTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch transactions';
          return of(fetchTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching recurring transactions
export const fetchRecurringTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchRecurringTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.recurringTransactionsLoaded;
        console.log('🔍 Recurring transactions epic filter check:', {
          cacheExists: !!cache,
          recurringTransactionsLoaded: cache?.recurringTransactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for recurring transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting recurring transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for recurring transactions API');
        return of(fetchRecurringTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch2/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Recurring transactions fetched successfully:', response.data?.length || 0, 'items');
          return fetchRecurringTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch recurring transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch recurring transactions';
          return of(fetchRecurringTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching future recurring transactions
export const fetchFutureRecurringTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchFutureRecurringTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.futureRecurringTransactionsLoaded;
        console.log('🔍 Future recurring transactions epic filter check:', {
          cacheExists: !!cache,
          futureRecurringTransactionsLoaded: cache?.futureRecurringTransactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for future recurring transactions:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting future recurring transactions API call...');
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.error('❌ User ID not found for future recurring transactions API');
        return of(fetchFutureRecurringTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch_future/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Future recurring transactions fetched successfully:', response.data?.length || 0, 'items');
          return fetchFutureRecurringTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch future recurring transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch future recurring transactions';
          return of(fetchFutureRecurringTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching budget summary
export const fetchBudgetSummaryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchBudgetSummaryStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.budgetSummaryLoaded;
        console.log('🔍 Budget summary epic filter check:', {
          cacheExists: !!cache,
          budgetSummaryLoaded: cache?.budgetSummaryLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for budget summary:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting budget summary API call...');
      const { userId, year, month } = action.payload || {};

      if (!userId || !year || !month) {
        console.error('❌ Missing parameters for budget summary API:', { userId, year, month });
        return of(fetchBudgetSummaryFailure('Missing required parameters'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/summary_by_month/${userId}/${year}/${month}`)).pipe(
        map((response) => {
          console.log('✅ Budget summary fetched successfully');
          return fetchBudgetSummarySuccess(response.data);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch budget summary:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch budget summary';
          return of(fetchBudgetSummaryFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching budget data
export const fetchBudgetDataEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchBudgetDataStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.budgetDataLoaded;
        console.log('🔍 Budget data epic filter check:', {
          cacheExists: !!cache,
          budgetDataLoaded: cache?.budgetDataLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for budget data:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting budget data API call...');
      const { userId } = action.payload || {};

      if (!userId) {
        console.error('❌ User ID not found for budget data API');
        return of(fetchBudgetDataFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/user/${userId}/month`)).pipe(
        map((response) => {
          console.log('✅ Budget data fetched successfully:', response.data?.length || 0, 'items');
          return fetchBudgetDataSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch budget data:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch budget data';
          return of(fetchBudgetDataFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching transaction summary
export const fetchTransactionSummaryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchTransactionSummaryStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.transactionSummaryLoaded;
        console.log('🔍 Transaction summary epic filter check:', {
          cacheExists: !!cache,
          transactionSummaryLoaded: cache?.transactionSummaryLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for transaction summary:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting transaction summary API call...');
      const { userId } = action.payload || {};

      if (!userId) {
        console.error('❌ User ID not found for transaction summary API');
        return of(fetchTransactionSummaryFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/summary/user/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Transaction summary fetched successfully');
          return fetchTransactionSummarySuccess(response.data);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch transaction summary:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch transaction summary';
          return of(fetchTransactionSummaryFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching hidden transactions
export const fetchHiddenTransactionsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchHiddenTransactionsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.hiddenTransactionsLoaded;
        console.log('🔍 Hidden transactions epic filter check:', {
          cacheExists: !!cache,
          hiddenTransactionsLoaded: cache?.hiddenTransactionsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for hidden transactions:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting hidden transactions API call...');
      const { userId } = action.payload || {};

      if (!userId) {
        console.error('❌ User ID not found for hidden transactions API');
        return of(fetchHiddenTransactionsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/transaction/hidden/user/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Hidden transactions fetched successfully:', response.data?.length || 0, 'items');
          return fetchHiddenTransactionsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch hidden transactions:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch hidden transactions';
          return of(fetchHiddenTransactionsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching reconcile data
export const fetchReconcileDataEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReconcileDataStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.reconcileDataLoaded;
        console.log('🔍 Reconcile data epic filter check:', {
          cacheExists: !!cache,
          reconcileDataLoaded: cache?.reconcileDataLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for reconcile data:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting reconcile data API call...');

      return from(axiosInstance.get('/pennypal/api/v1/reconcile/all')).pipe(
        map((response) => {
          console.log('✅ Reconcile data fetched successfully:', response.data?.length || 0, 'items');
          return fetchReconcileDataSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch reconcile data:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch reconcile data';
          return of(fetchReconcileDataFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching receipt transaction IDs
export const fetchReceiptTransactionIdsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReceiptTransactionIdsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.receiptTransactionIdsLoaded;
        console.log('🔍 Receipt transaction IDs epic filter check:', {
          cacheExists: !!cache,
          receiptTransactionIdsLoaded: cache?.receiptTransactionIdsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for receipt transaction IDs:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting receipt transaction IDs API call...');

      return from(axiosInstance.get('/pennypal/api/receipts/getReceiptTransactionIds')).pipe(
        map((response) => {
          console.log('✅ Receipt transaction IDs fetched successfully:', response.data?.length || 0, 'items');
          return fetchReceiptTransactionIdsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch receipt transaction IDs:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch receipt transaction IDs';
          return of(fetchReceiptTransactionIdsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching receipt items
export const fetchReceiptItemsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReceiptItemsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.receiptItemsLoaded;
        console.log('🔍 Receipt items epic filter check:', {
          cacheExists: !!cache,
          receiptItemsLoaded: cache?.receiptItemsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for receipt items:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting receipt items API call...');

      return from(axiosInstance.get('/pennypal/api/v1/receipt-items/all')).pipe(
        map((response) => {
          console.log('✅ Receipt items fetched successfully:', response.data?.length || 0, 'items');
          return fetchReceiptItemsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch receipt items:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch receipt items';
          return of(fetchReceiptItemsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching receipt summary
export const fetchReceiptSummaryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReceiptSummaryStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.receiptSummaryLoaded;
        console.log('🔍 Receipt summary epic filter check:', {
          cacheExists: !!cache,
          receiptSummaryLoaded: cache?.receiptSummaryLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for receipt summary:', error);
        return false;
      }
    }),
    mergeMap(() => {
      console.log('🚀 Starting receipt summary API call...');

      return from(axiosInstance.get('/pennypal/api/v1/receipt/summary')).pipe(
        map((response) => {
          console.log('✅ Receipt summary fetched successfully');
          return fetchReceiptSummarySuccess(response.data);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch receipt summary:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch receipt summary';
          return of(fetchReceiptSummaryFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching user receipts
export const fetchUserReceiptsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchUserReceiptsStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.userReceiptsLoaded;
        console.log('🔍 User receipts epic filter check:', {
          cacheExists: !!cache,
          userReceiptsLoaded: cache?.userReceiptsLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for user receipts:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchUserReceiptsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/receipts/user/${userId}`)).pipe(
        map((response) => {
          return fetchUserReceiptsSuccess(response.data || []);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch user receipts';
          return of(fetchUserReceiptsFailure(errorMessage));
        })
      );
    })
  );

// Epic for fetching chatbot history
export const fetchChatbotHistoryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchChatbotHistoryStart.type),
    filter(() => {
      try {
        const cache = state$.value?.cache;
        const shouldFetch = cache && !cache.chatbotHistoryLoaded;
        console.log('🔍 Chatbot history epic filter check:', {
          cacheExists: !!cache,
          chatbotHistoryLoaded: cache?.chatbotHistoryLoaded,
          shouldFetch
        });
        return shouldFetch;
      } catch (error) {
        console.error('Error checking cache state for chatbot history:', error);
        return false;
      }
    }),
    mergeMap((action) => {
      console.log('🚀 Starting chatbot history API call...');
      const { userId } = action.payload || {};

      if (!userId) {
        console.error('❌ User ID not found for chatbot history API');
        return of(fetchChatbotHistoryFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/chatbot/history/${userId}`)).pipe(
        map((response) => {
          console.log('✅ Chatbot history fetched successfully:', response.data?.length || 0, 'items');
          return fetchChatbotHistorySuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch chatbot history:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch chatbot history';
          return of(fetchChatbotHistoryFailure(errorMessage));
        })
      );
    })
  );

// Distinct subcategories epic
export const fetchDistinctSubcategoriesEpic = (action$) =>
  action$.pipe(
    ofType(fetchDistinctSubcategoriesStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchDistinctSubcategoriesFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/subCategory/byUser/distinct/${userId}`)).pipe(
        map((response) => {
          return fetchDistinctSubcategoriesSuccess(response.data || []);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch distinct subcategories';
          return of(fetchDistinctSubcategoriesFailure(errorMessage));
        })
      );
    })
  );

// Account IDs epic
export const fetchAccountIdsEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountIdsStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchAccountIdsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/account/account/ids/${userId}`)).pipe(
        map((response) => {
          return fetchAccountIdsSuccess(response.data || []);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch account IDs';
          return of(fetchAccountIdsFailure(errorMessage));
        })
      );
    })
  );

// User accounts epic
export const fetchUserAccountsEpic = (action$) =>
  action$.pipe(
    ofType(fetchUserAccountsStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchUserAccountsFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/accounts/${userId}`)).pipe(
        map((response) => {
          return fetchUserAccountsSuccess(response.data || []);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch user accounts';
          return of(fetchUserAccountsFailure(errorMessage));
        })
      );
    })
  );

// Account balances epics
export const fetchAccountBalancesInvestmentEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalancesInvestmentStart.type),
    mergeMap((action) => {
      const { userId, x = 12, y = 30 } = action.payload || {};

      if (!userId) {
        return of(fetchAccountBalancesInvestmentFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/investment`)).pipe(
        map((response) => {
          return fetchAccountBalancesInvestmentSuccess(response.data);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch investment account balances';
          return of(fetchAccountBalancesInvestmentFailure(errorMessage));
        })
      );
    })
  );

export const fetchAccountBalancesDepositoryEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalancesDepositoryStart.type),
    mergeMap((action) => {
      const { userId, x = 12, y = 30 } = action.payload || {};

      if (!userId) {
        return of(fetchAccountBalancesDepositoryFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/depository`)).pipe(
        map((response) => {
          return fetchAccountBalancesDepositorySuccess(response.data);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch depository account balances';
          return of(fetchAccountBalancesDepositoryFailure(errorMessage));
        })
      );
    })
  );

export const fetchAccountBalancesLoanEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalancesLoanStart.type),
    mergeMap((action) => {
      const { userId, x = 12, y = 30 } = action.payload || {};

      if (!userId) {
        return of(fetchAccountBalancesLoanFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/loan`)).pipe(
        map((response) => {
          return fetchAccountBalancesLoanSuccess(response.data);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch loan account balances';
          return of(fetchAccountBalancesLoanFailure(errorMessage));
        })
      );
    })
  );

export const fetchAccountBalancesCreditEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalancesCreditStart.type),
    mergeMap((action) => {
      const { userId, x = 12, y = 30 } = action.payload || {};

      if (!userId) {
        return of(fetchAccountBalancesCreditFailure('User ID not found'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/account/balances/agg/${userId}/${x}/${y}/credit`)).pipe(
        map((response) => {
          return fetchAccountBalancesCreditSuccess(response.data);
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch credit account balances';
          return of(fetchAccountBalancesCreditFailure(errorMessage));
        })
      );
    })
  );

// Payment subscription epic
export const fetchPaymentSubscriptionEpic = (action$) =>
  action$.pipe(
    ofType(fetchPaymentSubscriptionStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchPaymentSubscriptionFailure('User ID not found'));
      }

      console.log('🚀 Starting payment subscription API call for user:', userId);
      return from(axiosInstance.get(`/pennypal/api/v1/payment/subscription?userId=${userId}`)).pipe(
        map((response) => {
          console.log('✅ Payment subscription fetched successfully:', response.data);
          // Include userId in the response data for upcoming invoice epic
          const subscriptionData = response.data ? { ...response.data, userId } : null;
          return fetchPaymentSubscriptionSuccess(subscriptionData);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch payment subscription:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch payment subscription';
          return of(fetchPaymentSubscriptionFailure(errorMessage));
        })
      );
    })
  );

// Payment methods epic
export const fetchPaymentMethodsEpic = (action$) =>
  action$.pipe(
    ofType(fetchPaymentMethodsStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchPaymentMethodsFailure('User ID not found'));
      }

      console.log('🚀 Starting payment methods API call for user:', userId);
      return from(axiosInstance.get(`/pennypal/api/v1/payment/payment-methods?userId=${userId}`)).pipe(
        map((response) => {
          console.log('✅ Payment methods fetched successfully:', response.data?.length || 0, 'items');
          return fetchPaymentMethodsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch payment methods:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch payment methods';
          return of(fetchPaymentMethodsFailure(errorMessage));
        })
      );
    })
  );

// Payment products epic
export const fetchPaymentProductsEpic = (action$) =>
  action$.pipe(
    ofType(fetchPaymentProductsStart.type),
    mergeMap((action) => {
      console.log('🚀 Starting payment products API call...');
      return from(axiosInstance.get('/pennypal/api/v1/payment/products')).pipe(
        map((response) => {
          console.log('✅ Payment products fetched successfully:', response.data?.length || 0, 'items');
          return fetchPaymentProductsSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch payment products:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch payment products';
          return of(fetchPaymentProductsFailure(errorMessage));
        })
      );
    })
  );

// Payment invoices epic
export const fetchPaymentInvoicesEpic = (action$) =>
  action$.pipe(
    ofType(fetchPaymentInvoicesStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchPaymentInvoicesFailure('User ID not found'));
      }

      console.log('🚀 Starting payment invoices API call for user:', userId);
      return from(axiosInstance.get(`/pennypal/api/v1/payment/invoices?userId=${userId}`)).pipe(
        map((response) => {
          console.log('✅ Payment invoices fetched successfully:', response.data?.length || 0, 'items');
          return fetchPaymentInvoicesSuccess(response.data || []);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch payment invoices:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch payment invoices';
          return of(fetchPaymentInvoicesFailure(errorMessage));
        })
      );
    })
  );

// Upcoming invoice epic
export const fetchUpcomingInvoiceEpic = (action$) =>
  action$.pipe(
    ofType(fetchUpcomingInvoiceStart.type),
    mergeMap((action) => {
      const { userId } = action.payload || {};

      if (!userId) {
        return of(fetchUpcomingInvoiceFailure('User ID not found'));
      }

      console.log('🚀 Starting upcoming invoice API call for user:', userId);
      // Note: This API requires additional parameters (customerId, subscriptionId)
      // which should be provided in the payload when called
      const { customerId, subscriptionId } = action.payload;

      if (!customerId || !subscriptionId) {
        console.warn('⚠️ Missing customerId or subscriptionId for upcoming invoice, marking as loaded with null data');
        return of(fetchUpcomingInvoiceSuccess(null));
      }

      return from(axiosInstance.post('/pennypal/api/v1/payment/invoice/upcoming', {
        userId,
        customerId,
        subscriptionId
      })).pipe(
        map((response) => {
          console.log('✅ Upcoming invoice fetched successfully:', response.data);
          return fetchUpcomingInvoiceSuccess(response.data || null);
        }),
        catchError((error) => {
          console.error('❌ Failed to fetch upcoming invoice:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to fetch upcoming invoice';
          return of(fetchUpcomingInvoiceFailure(errorMessage));
        })
      );
    })
  );

// Epic to automatically fetch upcoming invoice after subscription is fetched
export const fetchUpcomingInvoiceAfterSubscriptionEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchPaymentSubscriptionSuccess.type),
    mergeMap((action) => {
      const subscriptionData = action.payload;
      const cache = state$.value.cache;

      // Check if upcoming invoice is already cached for this user with matching parameters
      const isCached = cache?.upcomingInvoiceLoaded &&
                      cache?.upcomingInvoiceParams?.userId == subscriptionData?.userId &&
                      cache?.upcomingInvoiceParams?.customerId == subscriptionData?.customerId &&
                      cache?.upcomingInvoiceParams?.subscriptionId == subscriptionData?.subscriptionId;

      if (isCached) {
        console.log('✅ Upcoming invoice already cached with matching parameters, skipping fetch');
        return of({ type: 'cache/upcomingInvoiceAlreadyCached' });
      }

      console.log('🔍 Cache check for upcoming invoice:', {
        loaded: cache?.upcomingInvoiceLoaded,
        cachedUserId: cache?.upcomingInvoiceParams?.userId,
        cachedCustomerId: cache?.upcomingInvoiceParams?.customerId,
        cachedSubscriptionId: cache?.upcomingInvoiceParams?.subscriptionId,
        subscriptionUserId: subscriptionData?.userId,
        subscriptionCustomerId: subscriptionData?.customerId,
        subscriptionSubscriptionId: subscriptionData?.subscriptionId
      });

      if (subscriptionData?.customerId && subscriptionData?.subscriptionId && subscriptionData?.userId) {
        console.log('🔄 Subscription fetched, now fetching upcoming invoice');
        return of(fetchUpcomingInvoiceStart({
          userId: subscriptionData.userId,
          customerId: subscriptionData.customerId,
          subscriptionId: subscriptionData.subscriptionId
        }));
      } else {
        console.log('⚠️ Subscription data incomplete, skipping upcoming invoice fetch');
        return of({ type: 'cache/upcomingInvoiceSkipped' });
      }
    })
  );

// Combined cache epic
export const cacheEpic = combineEpics(
  initializeCacheOnLoginEpic,
  fetchIconsEpic,
  fetchSubCategoryIconsEpic,
  fetchCategoriesEpic,
  fetchSubcategoriesEpic,
  fetchTransactionsEpic,
  fetchRecurringTransactionsEpic,
  fetchFutureRecurringTransactionsEpic,
  fetchBudgetSummaryEpic,
  fetchBudgetDataEpic,
  fetchTransactionSummaryEpic,
  fetchHiddenTransactionsEpic,
  fetchReconcileDataEpic,
  fetchReceiptTransactionIdsEpic,
  fetchReceiptItemsEpic,
  fetchReceiptSummaryEpic,
  fetchUserReceiptsEpic,
  fetchChatbotHistoryEpic,
  fetchDistinctSubcategoriesEpic,
  fetchAccountIdsEpic,
  fetchUserAccountsEpic,
  fetchAccountBalancesInvestmentEpic,
  fetchAccountBalancesDepositoryEpic,
  fetchAccountBalancesLoanEpic,
  fetchAccountBalancesCreditEpic,
  fetchPaymentSubscriptionEpic,
  fetchPaymentMethodsEpic,
  fetchPaymentProductsEpic,
  fetchPaymentInvoicesEpic,
  fetchUpcomingInvoiceEpic,
  fetchUpcomingInvoiceAfterSubscriptionEpic
);

export default cacheEpic;