package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.StripeCustomer;

@Repository
public interface StripeCustomerRepository extends JpaRepository<StripeCustomer, Integer> {
    StripeCustomer findByUserId(Integer userId);
    StripeCustomer findByStripeCustomerId(String customerId);
}
