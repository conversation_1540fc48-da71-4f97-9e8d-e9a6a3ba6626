package com.pennypal.fintech.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pennypal.fintech.dto.DailyInvestmentStockDto;
import com.pennypal.fintech.dto.InvestmentDto;
import com.pennypal.fintech.dto.InvestmentDto;
import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
import com.pennypal.fintech.repository.InvestmentRepository;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class MockInvestmentService {

    private final InvestmentRepository investmentRepository;
    private final DailyInvestmentStockRepository dailyInvestmentStockRepository;
    private final ObjectMapper objectMapper;
    private final OkHttpClient httpClient = new OkHttpClient();
    
    @Value("${plaid.client_id}")
    private String clientId;
    
    @Value("${plaid.secret}")
    private String secret;
    
    // Using the access token directly as requested
    private final String accessToken = "access-sandbox-c3ee34e3-1fe7-40e4-a03c-2c9c27ae3bb2";

    @Autowired
    public MockInvestmentService(
            InvestmentRepository investmentRepository,
            DailyInvestmentStockRepository dailyInvestmentStockRepository) {
        this.investmentRepository = investmentRepository;
        this.dailyInvestmentStockRepository = dailyInvestmentStockRepository;
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Get all investments for a user
     * @param userId User ID
     * @return List of investments
     */
    public List<InvestmentDto> getAllInvestmentsForUser(int userId) {
        List<Investment> investments = investmentRepository.findByUser_Id(userId);
        return investments.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    /**
     * Get investment by ID
     * @param id Investment ID
     * @return Investment details
     */
    public InvestmentDto getInvestmentById(int id) {
        Investment investment = investmentRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Investment not found with id: " + id));
        return convertToDto(investment);
    }
/**
 * Get investment performance metrics for a user
 * @param userId User ID
 * @return Performance metrics
 */
public Map<String, Object> getPerformanceMetrics(int userId) {
    List<Investment> investments = investmentRepository.findByUser_Id(userId);
    
    // Calculate total portfolio value with null safety
    double totalValue = investments.stream()
            .filter(Objects::nonNull) // Filter out null investments
            .mapToDouble(investment -> investment.getValue() != null ? investment.getValue() : 0.0)
            .sum();
    
    // Calculate total cost basis with null safety
    double totalCost = investments.stream()
            .filter(Objects::nonNull) // Filter out null investments
            .mapToDouble(investment -> investment.getCostBasis() != null ? investment.getCostBasis() : 0.0)
            .sum();
    
    System.out.println("Database investments total value: " + totalValue);
    
    // Calculate total gain/loss
    double totalGain = totalValue - totalCost;
    double totalGainPercent = (totalCost > 0) ? (totalGain / totalCost) * 100 : 0;
    
    // Calculate daily change (mock data)
    double dailyChange = totalValue * 0.01; // 1% daily change for demo
    double dailyChangePercent = 1.0; // 1% daily change for demo
    
    Map<String, Object> metrics = new HashMap<>();
    metrics.put("totalValue", totalValue);
    metrics.put("totalCost", totalCost);
    metrics.put("totalGain", totalGain);
    metrics.put("totalGainPercent", totalGainPercent);
    metrics.put("dailyChange", dailyChange);
    metrics.put("dailyChangePercent", dailyChangePercent);
    
    return metrics;
}

/**
     * Fetch investment holdings from Plaid for a user
     * @param userId User ID
     * @return Fetched investments
     */
    public Map<String, Object> fetchInvestmentsFromPlaid(int userId) {
        try {
            String holdingsData = getHoldingsData();
            JsonNode holdingsJson = objectMapper.readTree(holdingsData);
            
            // In a real implementation, we would process the data and save to database
            // For this mock, we'll return a processed version of the data
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "Successfully fetched investments");
            result.put("rawData", holdingsJson);
            
            return result;
        } catch (IOException e) {
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "Failed to fetch investments: " + e.getMessage());
            return errorResult;
        }
    }

    /**
     * Get investment history for a specific investment
     * @param id Investment ID
     * @param days Number of days of history to retrieve (default 30)
     * @return Historical data
     */
    public List<DailyInvestmentStockDto> getInvestmentHistory(int id, int days) {
        LocalDateTime startDate = LocalDateTime.now().minusDays(days);
        // Using the findByInvestment_IdAndTimestampAfterOrderByTimestampAsc method instead
        List<DailyInvestmentStock> historicalData = 
                dailyInvestmentStockRepository.findByInvestment_IdAndTimestampAfterOrderByTimestampAsc(id, startDate);
        
        return historicalData.stream()
                .map(this::convertToDailyDto)
                .collect(Collectors.toList());
    }

    /**
     * Get security details by ticker symbol
     * @param ticker Ticker symbol
     * @param accountId Account ID
     * @return Security details
     */
    public Map<String, Object> getSecurityDetails(String ticker, int accountId) {
        // Updated to use the available methods from the repository
        List<Investment> investments = investmentRepository.findByTicker(ticker);
        Investment investment = investments.stream()
                .filter(inv -> inv.getAccount().getId() == accountId)
                .findFirst()
                .orElse(null);
        
        if (investment == null) {
            try {
                // If not in our database, try to get from Plaid
                String securityData = getSecurityInfo(ticker);
                JsonNode securityJson = objectMapper.readTree(securityData);
                
                // Create a result with the data
                Map<String, Object> result = new HashMap<>();
                result.put("ticker", ticker);
                result.put("plaidData", securityJson);
                return result;
            } catch (IOException e) {
                throw new RuntimeException("Failed to get security details: " + e.getMessage());
            }
        }
        
        // Convert investment to a map of details
        Map<String, Object> details = new HashMap<>();
        details.put("securityId", investment.getSecurityId());
        details.put("securityName", investment.getSecurityName());
        details.put("ticker", investment.getTicker());
        details.put("securityType", investment.getSecurityType());
        details.put("currentPrice", investment.getCurrentPrice());
        details.put("currencyCode", investment.getCurrencyCode());
        details.put("lastUpdated", investment.getLastUpdated());
        
        return details;
    }

    /**
     * Get investment accounts for a user with Plaid connection status
     * @param userId User ID
     * @return Investment accounts with status
     */
    public List<Map<String, Object>> getInvestmentAccounts(int userId) {
        List<Investment> investments = investmentRepository.findByUser_Id(userId);
        
        // Group investments by account
        Map<Integer, List<Investment>> investmentsByAccount = investments.stream()
                .collect(Collectors.groupingBy(inv -> inv.getAccount().getId()));
        
        List<Map<String, Object>> accounts = new ArrayList<>();
        
        investmentsByAccount.forEach((accountId, accountInvestments) -> {
            Map<String, Object> accountData = new HashMap<>();
            accountData.put("accountId", accountId);
            accountData.put("accountName", accountInvestments.get(0).getAccount().getAccountName());
            accountData.put("institution", accountInvestments.get(0).getAccount().getInstitutionId());
            accountData.put("plaidConnected", true); // Mock status
            accountData.put("lastSync", LocalDateTime.now().minusHours(2)); // Mock last sync time
            accountData.put("totalValue", accountInvestments.stream()
                    .mapToDouble(Investment::getValue)
                    .sum());
            accountData.put("investmentCount", accountInvestments.size());
            
            accounts.add(accountData);
        });
        
        return accounts;
    }

    /**
     * Get portfolio diversity metrics
     * @param userId User ID
     * @return Diversity metrics
     */
   /**
 * Get portfolio diversity metrics (Fixed version with null safety)
 */
public Map<String, Object> getPortfolioDiversity(int userId) {
    List<Investment> investments = investmentRepository.findByUser_Id(userId);
    
    // Total portfolio value with null safety
    double totalValue = investments.stream()
            .filter(Objects::nonNull)
            .mapToDouble(investment -> investment.getValue() != null ? investment.getValue() : 0.0)
            .sum();
    
    // Group by security type with null safety
    Map<String, Double> securityTypeBreakdown = new HashMap<>();
    
    investments.stream()
            .filter(Objects::nonNull)
            .filter(investment -> investment.getSecurityType() != null)
            .forEach(investment -> {
                String securityType = investment.getSecurityType();
                double value = investment.getValue() != null ? investment.getValue() : 0.0;
                securityTypeBreakdown.put(
                        securityType, 
                        securityTypeBreakdown.getOrDefault(securityType, 0.0) + value
                );
            });
    
    // Calculate percentages
    Map<String, Double> securityTypePercentages = new HashMap<>();
    if (totalValue > 0) {
        securityTypeBreakdown.forEach((type, value) -> 
                securityTypePercentages.put(type, (value / totalValue) * 100)
        );
    }
    
    // Geographic breakdown (mock data for demonstration)
    Map<String, Double> geographicBreakdown = new HashMap<>();
    geographicBreakdown.put("US", 65.0);
    geographicBreakdown.put("Europe", 15.0);
    geographicBreakdown.put("Asia", 12.0);
    geographicBreakdown.put("Other", 8.0);
    
    // Sector breakdown (mock data for demonstration)
    Map<String, Double> sectorBreakdown = new HashMap<>();
    sectorBreakdown.put("Technology", 30.0);
    sectorBreakdown.put("Healthcare", 20.0);
    sectorBreakdown.put("Financial", 15.0);
    sectorBreakdown.put("Consumer", 15.0);
    sectorBreakdown.put("Energy", 10.0);
    sectorBreakdown.put("Other", 10.0);
    
    // Assemble the result
    Map<String, Object> diversityMetrics = new HashMap<>();
    diversityMetrics.put("securityTypeBreakdown", securityTypePercentages);
    diversityMetrics.put("geographicBreakdown", geographicBreakdown);
    diversityMetrics.put("sectorBreakdown", sectorBreakdown);
    diversityMetrics.put("topHoldings", getTopHoldings(investments, 5));
    
    return diversityMetrics;
}

/**
 * Fixed getTopHoldings method with null safety
 */
private List<Map<String, Object>> getTopHoldings(List<Investment> investments, int count) {
    double totalPortfolioValue = investments.stream()
            .filter(Objects::nonNull)
            .mapToDouble(investment -> investment.getValue() != null ? investment.getValue() : 0.0)
            .sum();
            
    return investments.stream()
            .filter(Objects::nonNull)
            .filter(inv -> inv.getValue() != null && inv.getValue() > 0)
            .sorted(Comparator.comparing(Investment::getValue).reversed())
            .limit(count)
            .map(inv -> {
                Map<String, Object> holding = new HashMap<>();
                holding.put("securityName", inv.getSecurityName());
                holding.put("ticker", inv.getTicker());
                holding.put("value", inv.getValue());
                holding.put("percentOfPortfolio", 
                    totalPortfolioValue > 0 ? (inv.getValue() / totalPortfolioValue) * 100 : 0.0);
                return holding;
            })
            .collect(Collectors.toList());
}
   
    /**
     * Get portfolio optimization recommendations
     * @param userId User ID
     * @return Recommendations
     */
    public Map<String, Object> getPortfolioOptimization(int userId) {
        // This would typically involve complex calculations
        // For mock purposes, we'll return sample recommendations
        
        List<Map<String, Object>> recommendations = new ArrayList<>();
        
        Map<String, Object> diversification = new HashMap<>();
        diversification.put("type", "DIVERSIFICATION");
        diversification.put("message", "Your portfolio is heavily weighted in Technology. Consider adding more exposure to other sectors.");
        diversification.put("severity", "MEDIUM");
        recommendations.add(diversification);
        
        Map<String, Object> feesReduction = new HashMap<>();
        feesReduction.put("type", "FEES");
        feesReduction.put("message", "You could save approximately $120 annually by switching to lower-fee ETFs.");
        feesReduction.put("severity", "LOW");
        recommendations.add(feesReduction);
        
        Map<String, Object> rebalancing = new HashMap<>();
        rebalancing.put("type", "REBALANCING");
        rebalancing.put("message", "Your portfolio has drifted from your target allocation. Consider rebalancing to maintain your risk profile.");
        rebalancing.put("severity", "HIGH");
        recommendations.add(rebalancing);
        
        Map<String, Object> result = new HashMap<>();
        result.put("recommendations", recommendations);
        result.put("riskScore", 65); // Out of 100
        result.put("diversificationScore", 72); // Out of 100
        result.put("feeEfficiencyScore", 85); // Out of 100
        
        return result;
    }
    
    /**
     * Helper method to get holdings data from Plaid API
     */
    private String getHoldingsData() throws IOException {
        String url = "https://sandbox.plaid.com/investments/holdings/get";
        String payload = "{"
                + "\"client_id\":\"" + clientId + "\","
                + "\"secret\":\"" + secret + "\","
                + "\"access_token\":\"" + accessToken + "\""
                + "}";
        
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), payload);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get holdings: " + response.message());
            }
            return response.body().string();
        }
    }
    
    /**
     * Helper method to get security information from Plaid API
     */
    private String getSecurityInfo(String ticker) throws IOException {
        String url = "https://sandbox.plaid.com/investments/securities/get";
        String payload = "{"
                + "\"client_id\":\"" + clientId + "\","
                + "\"secret\":\"" + secret + "\","
                + "\"access_token\":\"" + accessToken + "\""
                + "}";
        
        RequestBody body = RequestBody.create(MediaType.parse("application/json"), payload);
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();
        
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to get security info: " + response.message());
            }
            return response.body().string();
        }
    }

    // Utility methods for entity/DTO conversion
    private InvestmentDto convertToDto(Investment investment) {
        InvestmentDto dto = new InvestmentDto();
        dto.setId(investment.getId());
        dto.setUserId(investment.getUser().getId());
        dto.setAccountId(investment.getAccount().getId());
        dto.setSecurityName(investment.getSecurityName());
        dto.setTicker(investment.getTicker());
        dto.setSecurityType(investment.getSecurityType());
        dto.setQuantity(investment.getQuantity());
        dto.setValue(investment.getValue());
        dto.setCostBasis(investment.getCostBasis());
        dto.setCurrentPrice(investment.getCurrentPrice());
        dto.setCurrencyCode(investment.getCurrencyCode());
        dto.setLastUpdated(investment.getLastUpdated());
        
        // Calculate gains and today's changes (mock data for demo)
        double todayChange = investment.getValue() * 0.01; // 1% daily change
        double todayChangePercent = 1.0;
        double totalGain = investment.getValue() - investment.getCostBasis();
        double totalGainPercent = (investment.getCostBasis() > 0) ? 
                (totalGain / investment.getCostBasis()) * 100 : 0;
        
        dto.setTodayChange(todayChange);
        dto.setTodayChangePercent(todayChangePercent);
        dto.setTotalGain(totalGain);
        dto.setTotalGainPercent(totalGainPercent);
        
        return dto;
    }

    private DailyInvestmentStockDto convertToDailyDto(DailyInvestmentStock dailyStock) {
        DailyInvestmentStockDto dto = new DailyInvestmentStockDto();
        dto.setId(dailyStock.getId());
        dto.setInvestmentId(dailyStock.getInvestment().getId());
        dto.setSecurityName(dailyStock.getInvestment().getSecurityName());
        dto.setTicker(dailyStock.getInvestment().getTicker());
        dto.setPrice(dailyStock.getPrice());
        dto.setQuantity(dailyStock.getQuantity());
        dto.setValue(dailyStock.getValue());
        dto.setPriceChange(dailyStock.getPriceChange());
        dto.setPercentChange(dailyStock.getPercentChange());
        dto.setTimestamp(dailyStock.getTimestamp());
        return dto;
    }
}