package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.MxService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/mx")
public class MxController {

    @Autowired
    private MxService mxService;

    @PostMapping("/user")
    public String createUser(@RequestParam Integer userId) {
        try {
            return mxService.createMxUser(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/connect-url")
    public String getConnectWidget(@RequestParam Integer userId) {
        try {
            return mxService.getConnectWidgetUrl(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/accounts")
    public String getAccounts(@RequestParam Integer userId) {
        try {
            return mxService.getAccounts(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/transactions")
    public String getTransactions(@RequestParam Integer userId) {
        try {
            return mxService.getTransactions(userId);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}