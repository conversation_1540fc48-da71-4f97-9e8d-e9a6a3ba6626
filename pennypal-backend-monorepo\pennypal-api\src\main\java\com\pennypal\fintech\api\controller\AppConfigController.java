package com.pennypal.fintech.api.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;

import com.pennypal.fintech.service.AppConfigService;

import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/api/v1/app-config")
public class AppConfigController {
    @Autowired
    private AppConfigService appConfigService;

    @Operation(summary = "Reload application configuration",
               description = "Reloads the application configuration from the database")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Configuration reloaded successfully"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/reload")
    // @PreAuthorize("hasAuthority('ADMIN')")
    public ResponseEntity<String> reloadConfigs() {
        appConfigService.reload();
        return ResponseEntity.ok("Configuration reloaded");
    }
}
