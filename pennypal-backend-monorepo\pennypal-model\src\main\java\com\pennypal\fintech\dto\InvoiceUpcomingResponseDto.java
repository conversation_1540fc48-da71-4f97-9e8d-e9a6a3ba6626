package com.pennypal.fintech.dto;

import java.util.List;

import lombok.Data;

@Data
public class InvoiceUpcomingResponseDto {
    private List<InvoiceLineItemDto> items;
    private String totalAmount;
    private String currency;
    private String nextPaymentDate;

    public InvoiceUpcomingResponseDto(List<InvoiceLineItemDto> items, String totalAmount, String currency, String nextPaymentDate) {
        this.items = items;
        this.totalAmount = totalAmount;
        this.currency = currency;
        this.nextPaymentDate = nextPaymentDate;
    }
}