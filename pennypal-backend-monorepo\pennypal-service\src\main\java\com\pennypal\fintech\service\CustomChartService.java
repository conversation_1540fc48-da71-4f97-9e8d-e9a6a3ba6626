package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.CustomChartDto;
import com.pennypal.fintech.entity.AiQueryLogs;
import com.pennypal.fintech.entity.CustomChart;
import com.pennypal.fintech.repository.AiQueryLogsRepository;
import com.pennypal.fintech.repository.CustomChartRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Caching;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CustomChartService {

    @Autowired
    private AiQueryLogsRepository aiQueryLogsRepository;

    @Autowired
    private CustomChartRepository customChartRepository;

    @Caching(evict = {
        @CacheEvict(value = "customChartCache",
                    key = "'getByUserId' + '_' + #dto.userId",
                    allEntries = true),
        @CacheEvict(value = "chatbotCache",
                    key = "'getHistory' + '_' + #dto.userId",
                    allEntries = true)
    })
    public CustomChart create(CustomChartDto dto) {
        log.info("Inside create method of CustomChartService");
        log.info("Received CustomChartDto: " + dto);
        if (dto == null) {
            throw new IllegalArgumentException("Chart data is required");
        }
        CustomChart chart = new CustomChart();
        chart.setChatId(dto.getChatId());
        chart.setUserId(dto.getUserId());
        chart.setTitle(dto.getTitle());
        chart.setType(dto.getType());
        chart.setData(dto.getData());

        // Update the chat log to mark it as saved
        AiQueryLogs chatLog = aiQueryLogsRepository.findById(dto.getChatId());
        if (chatLog != null) {
            chatLog.setIsSaved(true);
            aiQueryLogsRepository.save(chatLog);
        }
        log.info("Updated chat log to mark it as saved");

        return customChartRepository.save(chart);
    }

    @Cacheable(value = "customChartCache",
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    public List<CustomChart> getByUserId(Integer userId) {
        log.info("Inside getByUserId method of CustomChartService");
        log.info("User ID: " + userId);
        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (!customChartRepository.existsByUserId(userId)) {
            throw new RuntimeException("User not found");
        }
        return customChartRepository.findByUserId(userId);
    }

    public List<CustomChart> getByChatId(Integer chatId) {
        log.info("Inside getByChatId method of CustomChartService");
        log.info("Chat ID: " + chatId);
        if (chatId == null) {
            throw new IllegalArgumentException("Chat ID is required");
        }
        if (!customChartRepository.existsByChatId(chatId)) {
            throw new RuntimeException("Chat not found");
        }
        return customChartRepository.findByChatId(chatId);
    }

    public List<CustomChart> getByUserAndChat(Integer userId, Integer chatId) {
        log.info("Inside getByUserAndChat method of CustomChartService");
        log.info("User ID: " + userId);
        log.info("Chat ID: " + chatId);
        if (userId == null) {
            throw new IllegalArgumentException("User ID is required");
        }
        if (chatId == null) {
            throw new IllegalArgumentException("Chat ID is required");
        }
        if (!customChartRepository.existsByUserIdAndChatId(userId, chatId)) {
            throw new RuntimeException("User and chat not found");
        }
        return customChartRepository.findByUserIdAndChatId(userId, chatId);
    }

    @Caching(evict = {
        @CacheEvict(value = "customChartCache",
                    key = "'getByUserId' + '_' + #id",
                    allEntries = true),
        @CacheEvict(value = "chatbotCache",
                    key = "'getHistory' + '_' + #id",
                    allEntries = true)
    })
    public void deleteById(Long id) {
        log.info("Inside deleteById method of CustomChartService");
        log.info("Chart ID: " + id);
        if (id == null) {
            throw new IllegalArgumentException("Chart ID is required");
        }
        if (!customChartRepository.existsById(id)) {
            throw new RuntimeException("Chart not found with id: " + id);
        }
        // Fetch chat ID
        Integer chatId = null;
        CustomChart chart = customChartRepository.findById(id).orElse(null);
        if (chart != null) {
            chatId = chart.getChatId();
            log.info("Chat ID: " + chatId);
        }

        customChartRepository.deleteById(id);

        // Update the chat log to mark it as not saved
        AiQueryLogs chatLog = aiQueryLogsRepository.findById(chatId);
        if (chatLog != null) {
            chatLog.setIsSaved(false);
            aiQueryLogsRepository.save(chatLog);
        }
        log.info("Updated chat log to mark it as not saved");
    }

    @Caching(evict = {
        @CacheEvict(value = "customChartCache",
                    key = "'getByUserId' + '_' + #id",
                    allEntries = true),
        @CacheEvict(value = "chatbotCache",
                    key = "'getHistory' + '_' + #id",
                    allEntries = true)
    })
    public CustomChart update(Long id, CustomChartDto dto) {
        log.info("Inside update method of CustomChartService");
        log.info("Chart ID: " + id);
        log.info("Updated chart data: " + dto);
        if (id == null) {
            throw new IllegalArgumentException("Chart ID is required");
        }
        if (dto == null) {
            throw new IllegalArgumentException("Chart data is required");
        }
        if (!customChartRepository.existsById(id)) {
            throw new RuntimeException("Chart not found with id: " + id);
        }
        CustomChart chart = customChartRepository.findById(id).orElse(null);
        if (chart != null) {
            chart.setTitle(dto.getTitle());
            return customChartRepository.save(chart);
        }
        return null;
    }
}