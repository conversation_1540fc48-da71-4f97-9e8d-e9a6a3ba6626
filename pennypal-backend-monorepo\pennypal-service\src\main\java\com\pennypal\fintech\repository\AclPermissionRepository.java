package com.pennypal.fintech.repository;

import com.pennypal.fintech.dto.UserPermissionDto;
import com.pennypal.fintech.entity.AclPermissions;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface AclPermissionRepository extends JpaRepository<AclPermissions, Long> {
    @Query("""
        SELECT new com.pennypal.fintech.dto.UserPermissionDto(p.name, a.name)
        FROM UsersGroup ug
        JOIN AclPermissions ap ON ug.group = ap.group
        JOIN AclPages p ON ap.page = p
        JOIN AclActions a ON ap.action = a
        WHERE ug.userId = :userId
        AND ug.status = 'ACTIVE'
        AND (ug.expiresAt IS NULL OR ug.expiresAt > CURRENT_TIMESTAMP)
        UNION
        SELECT new com.pennypal.fintech.dto.UserPermissionDto(p.name, a.name)
        FROM AclPermissions ap
        JOIN AclPages p ON ap.page = p
        JOIN AclActions a ON ap.action = a
        JOIN AclGroups g ON ap.group = g
        WHERE g.name = 'FREE'
        AND NOT EXISTS (
            SELECT 1 FROM UsersGroup ug
            WHERE ug.userId = :userId
            AND ug.status = 'ACTIVE'
            AND (ug.expiresAt IS NULL OR ug.expiresAt > CURRENT_TIMESTAMP)
        )
    """)
    List<UserPermissionDto> findPermissionsByUserId(Integer userId);
}