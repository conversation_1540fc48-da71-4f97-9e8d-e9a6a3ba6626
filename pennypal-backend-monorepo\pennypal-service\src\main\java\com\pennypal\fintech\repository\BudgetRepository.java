package com.pennypal.fintech.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;
import com.pennypal.fintech.entity.Budget;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface BudgetRepository extends JpaRepository<Budget, Integer> {
    List<Budget> findByUserId(Integer userId);
    List<Budget> findByCategoryId(Integer categoryId);
    List<Budget> findByUserIdAndCategoryId(Integer userId, Integer categoryId);
	Optional<Budget> findById(Integer id);
    // Modified query to select budgets for a specific user and month
    @Query("SELECT b FROM Budget b " +
    "JOIN FETCH b.category c " +
    "LEFT JOIN FETCH b.subCategory s " +
    "JOIN FETCH b.user u " +
    "WHERE u.id = :userId AND FUNCTION('MONTH', b.date) = :month")
List<Budget> findByUserWithMonth(@Param("userId") Integer userId, @Param("month") Integer month);

    // Additional query to handle year-based filtering if needed (optional)
    @Query("SELECT b FROM Budget b WHERE b.user.id = :userId AND MONTH(b.date) = :month AND YEAR(b.date) = :year")
    List<Budget> findByUserWithMonthAndYear(@Param("userId") Integer userId, @Param("month") int month, @Param("year") int year);
  
    @Query("SELECT b FROM Budget b JOIN FETCH b.subCategory WHERE b.id = :id")
Budget findByIdWithSubCategoryAndIconKey(@Param("id") Integer id);
@Query("SELECT b FROM Budget b JOIN FETCH b.category WHERE b.id = :id")
Budget findByIdWithCategory(@Param("id") Integer id);

    @Query(value = """
            WITH CurrentLastDayExpenses AS (
                SELECT 
                    c.category, b.category_id, b.sub_category_id, b.actual, b.date,
                    ROW_NUMBER() OVER (PARTITION BY b.category_id, b.sub_category_id ORDER BY b.date DESC) AS rn
                FROM budget b
                JOIN category c ON b.category_id = c.id
                WHERE b.user_id = :userId AND
                    c.category <> 'income' AND
                    YEAR(b.date) = :year AND
                    MONTH(b.date) = :month
            ),
            CurrentMonthExpenses AS (
                SELECT 
                    category, SUM(actual) AS current_expense
                FROM CurrentLastDayExpenses
                WHERE rn = 1
            ),
            PreviousLastDayExpenses AS (
                SELECT 
                    c.category, b.category_id, b.sub_category_id, b.actual, b.date,
                    ROW_NUMBER() OVER (PARTITION BY b.category_id, b.sub_category_id ORDER BY b.date DESC) AS rn
                FROM budget b
                JOIN category c ON b.category_id = c.id
                WHERE b.user_id = :userId AND
                    c.category <> 'income' AND
                    ((MONTH(b.date) = :month - 1 AND YEAR(b.date) = :year) OR
                    (MONTH(b.date) = 12 AND YEAR(b.date) = :year - 1 AND :month = 1))
            ),
            PreviousMonthExpenses AS (
                SELECT 
                    category, SUM(actual) AS previous_expense
                FROM PreviousLastDayExpenses
                WHERE rn = 1
                -- GROUP BY category
            )
            SELECT 
                'expense' as category, c.current_expense,
                COALESCE(p.previous_expense, 0) AS previous_expense,
                c.current_expense - COALESCE(p.previous_expense, 0) AS delta_amount,
                CASE 
                    WHEN COALESCE(p.previous_expense, 0) = 0 THEN NULL 
                    ELSE ROUND((c.current_expense - COALESCE(p.previous_expense, 0)) / COALESCE(p.previous_expense, 1) * 100, 2)
                END AS delta_percentage
            FROM CurrentMonthExpenses c
            LEFT JOIN PreviousMonthExpenses p ON c.category = p.category;
            """, nativeQuery = true)
    List<Object[]> getBudgetExpenseDeltas(Integer userId, Integer year, Integer month);

    @Query(value = """
            WITH CurrentLastDayIncome AS (
                SELECT 
                    c.category, b.category_id, b.sub_category_id, b.actual, b.date,
                    ROW_NUMBER() OVER (PARTITION BY b.category_id, b.sub_category_id ORDER BY b.date DESC) AS rn
                FROM budget b
                JOIN category c ON b.category_id = c.id
                WHERE b.user_id = :userId AND
                    c.category = 'income' AND
                    YEAR(b.date) = :year AND
                    MONTH(b.date) = :month
            ),
            CurrentMonthIncome AS (
                SELECT 
                    category, SUM(actual) AS current_income
                FROM CurrentLastDayIncome
                WHERE rn = 1
            ),
            PreviousLastDayIncome AS (
                SELECT 
                    c.category, b.category_id, b.sub_category_id, b.actual, b.date,
                    ROW_NUMBER() OVER (PARTITION BY b.category_id, b.sub_category_id ORDER BY b.date DESC) AS rn
                FROM budget b
                JOIN category c ON b.category_id = c.id
                WHERE b.user_id = :userId AND
                    c.category = 'income' AND
                    ((MONTH(b.date) = :month - 1 AND YEAR(b.date) = :year) OR
                    (MONTH(b.date) = 12 AND YEAR(b.date) = :year - 1 AND :month = 1))
            ),
            PreviousMonthIncome AS (
                SELECT 
                    category, SUM(actual) AS previous_income
                FROM PreviousLastDayIncome
                WHERE rn = 1
                -- GROUP BY category
            )
            SELECT 
                'income' as category, c.current_income,
                COALESCE(p.previous_income, 0) AS previous_income,
                c.current_income - COALESCE(p.previous_income, 0) AS delta_amount,
                CASE 
                    WHEN COALESCE(p.previous_income, 0) = 0 THEN NULL
                    ELSE ROUND((c.current_income - COALESCE(p.previous_income, 0)) / COALESCE(p.previous_income, 1) * 100, 2)
                END AS delta_percentage
            FROM CurrentMonthIncome c
            LEFT JOIN PreviousMonthIncome p ON c.category = p.category;
            """, nativeQuery = true)
    List<Object[]> getBudgetIncomeDeltas(Integer userId, Integer year, Integer month);

    @Query(value = """
        WITH budget_temp AS(
            SELECT *, ROW_NUMBER() OVER(PARTITION BY category_id, sub_category_id ORDER BY date DESC) as rn
            FROM budget
            WHERE YEAR(date) = YEAR(CURDATE()) AND
                  MONTH(date) = MONTH(CURDATE()) AND
                  user_id = :userId
            )
            SELECT actual, allocated, category_id, date, dynamic_allocated, exclude, id,
                   is_dynamic, remaining, rollover, sub_category_id, user_id,
                   custom_sub_category, icon
            FROM budget_temp WHERE rn = 1;
        """, nativeQuery = true)
    List<Budget> getCurrMonthBudgets(@Param("userId") Integer userId);

    @Query(value = """
        WITH budget_temp AS (
            SELECT *, ROW_NUMBER() OVER(PARTITION BY category_id, sub_category_id ORDER BY date DESC) AS rn
            FROM budget
            WHERE user_id = :userId AND
                  category_id = :categoryId AND
                  sub_category_id = :subCategoryId AND
                  YEAR(date) = YEAR(:date) AND
                  MONTH(date) = MONTH(:date)
            )
            SELECT actual, allocated, category_id, date, dynamic_allocated, exclude, id,
                is_dynamic, remaining, rollover, sub_category_id, user_id,
                custom_sub_category, icon, custom_sub_category_id
            FROM budget_temp WHERE rn = 1;
        """, nativeQuery = true)
    Optional<Budget> findLatestBudgetByCategoryIdAndSubCategoryId(
        @Param("userId") Integer userId,
        @Param("categoryId") Integer categoryId,
        @Param("subCategoryId") Integer subCategoryId,
        @Param("date") LocalDate date
    );

    @Query(value = """
            WITH MonthlyBudget AS (
                SELECT c.category, SUM(b.allocated) as total_allocated,
                       SUM(b.actual) as total_actual, SUM(b.remaining) as total_remaining
                FROM budget b
                JOIN category c ON b.category_id = c.id
                WHERE b.user_id = :userId AND
                    YEAR(b.date) = :year AND
                    MONTH(b.date) = :month
                GROUP BY c.category
            )
            SELECT 
                (SELECT COALESCE(SUM(total_actual), 0) 
                 FROM MonthlyBudget 
                 WHERE LOWER(category) LIKE '%income%') as income,
                (SELECT COALESCE(SUM(total_allocated), 0) 
                 FROM MonthlyBudget 
                 WHERE LOWER(category) NOT LIKE '%income%') as budget,
                (SELECT COALESCE(SUM(total_actual), 0) 
                 FROM MonthlyBudget 
                 WHERE LOWER(category) NOT LIKE '%income%') as actual,
                (SELECT COALESCE(SUM(total_remaining), 0) 
                 FROM MonthlyBudget 
                 WHERE LOWER(category) NOT LIKE '%income%') as remaining
            """, nativeQuery = true)
    List<Object[]> getBudgetSummaryByMonth(
        @Param("userId") Integer userId, 
        @Param("year") Integer year, 
        @Param("month") Integer month);

        @Query(value = """
            WITH LatestBudgets AS (
                SELECT b.*,
                    ROW_NUMBER() OVER (
                        PARTITION BY b.category_id, 
                                    COALESCE(b.custom_sub_category, b.sub_category_id)
                        ORDER BY b.date DESC
                    ) as rn
                FROM budget b
                WHERE b.user_id = :userId AND
                    YEAR(b.date) = :year AND
                    MONTH(b.date) = :month
            ),
            CategoryTotals AS (
                SELECT 
                    CASE 
                        WHEN LOWER(c.category) LIKE '%income%' THEN 'Income'
                        ELSE 'Expenses'
                    END as flow_type,
                    c.category as category,
                    COALESCE(b.custom_sub_category, sc.sub_category, 'Undefined') as subcategory,
                    SUM(b.actual) as value
                FROM LatestBudgets b
                JOIN category c ON b.category_id = c.id
                LEFT JOIN sub_category sc ON b.sub_category_id = sc.id
                WHERE b.rn = 1 AND
                    b.actual > 0
                GROUP BY 
                    CASE 
                        WHEN LOWER(c.category) LIKE '%income%' THEN 'Income'
                        ELSE 'Expenses'
                    END,
                    c.category,
                    COALESCE(b.custom_sub_category, sc.sub_category)
            ),
            Totals AS (
                SELECT 
                    SUM(CASE WHEN flow_type = 'Income' THEN value ELSE 0 END) as total_income,
                    SUM(CASE WHEN flow_type = 'Expenses' THEN value ELSE 0 END) as total_expenses
                FROM CategoryTotals
            ),
            DeficitSurplus AS (
                SELECT 
                    CASE 
                        WHEN total_income > total_expenses THEN 'Surplus'
                        ELSE 'Deficit'
                    END as type,
                    ABS(total_income - total_expenses) as value
                FROM Totals
            )

            -- Layer 0: Income subcategories → Total Income
            SELECT 
                subcategory AS source,
                'Total Income' AS target,
                value,
                0 AS level,
                'Income' AS category
            FROM CategoryTotals
            WHERE flow_type = 'Income'

            UNION ALL

            -- Layer 1: Total Income → Total Expenses
            SELECT 
                'Total Income' AS source,
                'Total Expenses' AS target,
                LEAST(t.total_income, t.total_expenses) AS value,
                1 AS level,
                'Buffer' AS category
            FROM Totals t

            UNION ALL

            -- Layer 1: Deficit → Total Expenses (only if deficit exists)
            SELECT 
                'Deficit' AS source,
                'Total Expenses' AS target,
                ds.value AS value,
                1 AS level,
                'Buffer' AS category
            FROM DeficitSurplus ds
            JOIN Totals t ON 1 = 1
            WHERE ds.type = 'Deficit'

            UNION ALL

            -- Layer 1: Total Income → Surplus (only if surplus exists)
            SELECT 
                'Total Income' AS source,
                'Surplus' AS target,
                ds.value AS value,
                1 AS level,
                'Buffer' AS category
            FROM DeficitSurplus ds
            WHERE ds.type = 'Surplus'

            UNION ALL

            -- Layer 2: Total Expenses → Expense Categories
            SELECT 
                'Total Expenses' AS source,
                category AS target,
                SUM(value) AS value,
                2 AS level,
                'Buffer' AS category
            FROM CategoryTotals
            WHERE flow_type = 'Expenses'
            GROUP BY category

            ORDER BY level, value DESC;

        """, nativeQuery = true)
    List<Object[]> getSankeyData(Integer userId, Integer year, Integer month);

    @Query(value = """
            WITH LatestBudgets AS (
                SELECT b.*,
                    ROW_NUMBER() OVER (
                        PARTITION BY b.category_id, 
                                    COALESCE(b.custom_sub_category, b.sub_category_id)
                        ORDER BY b.date DESC
                    ) as rn
                FROM budget b
                WHERE b.user_id = :userId
                AND YEAR(b.date) = :year
                AND MONTH(b.date) = :month
            ),
            CategoryTotals AS (
                SELECT 
                    CASE 
                        WHEN LOWER(c.category) LIKE '%income%' THEN 'Income'
                        ELSE 'Expenses'
                    END AS flow_type,
                    c.category AS category,
                    COALESCE(b.custom_sub_category, sc.sub_category, 'Undefined') AS subcategory,
                    SUM(b.actual) AS value
                FROM LatestBudgets b
                JOIN category c ON b.category_id = c.id
                LEFT JOIN sub_category sc ON b.sub_category_id = sc.id
                WHERE b.rn = 1
                AND b.actual > 0
                GROUP BY 
                    CASE 
                        WHEN LOWER(c.category) LIKE '%income%' THEN 'Income'
                        ELSE 'Expenses'
                    END,
                    c.category,
                    COALESCE(b.custom_sub_category, sc.sub_category)
            ),
            Totals AS (
                SELECT 
                    SUM(CASE WHEN flow_type = 'Income' THEN value ELSE 0 END) AS total_income,
                    SUM(CASE WHEN flow_type = 'Expenses' THEN value ELSE 0 END) AS total_expenses
                FROM CategoryTotals
            ),
            DeficitSurplus AS (
                SELECT 
                    CASE 
                        WHEN total_income > total_expenses THEN 'Surplus'
                        ELSE 'Deficit'
                    END AS type,
                    ABS(total_income - total_expenses) AS value
                FROM Totals
            ),
            ExpenseSubcategoriesWithGrouping AS (
                SELECT 
                    category,
                    CASE 
                        WHEN (value / category_total) < 0.05 THEN CONCAT('Other ', category)
                        ELSE subcategory
                    END AS subcategory_grouped,
                    value
                FROM (
                    SELECT 
                        c.category,
                        COALESCE(b.custom_sub_category, sc.sub_category, 'Undefined') AS subcategory,
                        SUM(b.actual) AS value,
                        SUM(SUM(b.actual)) OVER (PARTITION BY c.category) AS category_total
                    FROM LatestBudgets b
                    JOIN category c ON b.category_id = c.id
                    LEFT JOIN sub_category sc ON b.sub_category_id = sc.id
                    WHERE b.rn = 1
                    AND b.actual > 0
                    AND LOWER(c.category) NOT LIKE '%income%'
                    GROUP BY c.category, COALESCE(b.custom_sub_category, sc.sub_category)
                ) raw
            )

            -- Layer 0: Income subcategories → Total Income
            SELECT 
                subcategory AS source,
                'Total Income' AS target,
                value,
                0 AS level,
                'Income' AS category
            FROM CategoryTotals
            WHERE flow_type = 'Income'

            UNION ALL

            -- Layer 1: Total Income → Total Expenses
            SELECT 
                'Total Income' AS source,
                'Total Expenses' AS target,
                LEAST(t.total_income, t.total_expenses) AS value,
                1 AS level,
                'Buffer' AS category
            FROM Totals t

            UNION ALL

            -- Layer 1: Deficit → Total Expenses (if deficit exists)
            SELECT 
                'Deficit' AS source,
                'Total Expenses' AS target,
                ds.value AS value,
                1 AS level,
                'Buffer' AS category
            FROM DeficitSurplus ds
            JOIN Totals t ON 1=1
            WHERE ds.type = 'Deficit'

            UNION ALL

            -- Layer 1: Total Income → Surplus (if surplus exists)
            SELECT 
                'Total Income' AS source,
                'Surplus' AS target,
                ds.value AS value,
                1 AS level,
                'Buffer' AS category
            FROM DeficitSurplus ds
            WHERE ds.type = 'Surplus'

            UNION ALL

            -- Layer 2: Total Expenses → Expense Categories
            SELECT 
                'Total Expenses' AS source,
                category AS target,
                SUM(value) AS value,
                2 AS level,
                'Expenses' AS category
            FROM CategoryTotals
            WHERE flow_type = 'Expenses'
            GROUP BY category

            UNION ALL

            -- Layer 3: Expense Categories → Subcategories
            SELECT 
                category AS source,
                subcategory_grouped AS target,
                SUM(value) AS value,
                3 AS level,
                'Expenses' AS category
            FROM ExpenseSubcategoriesWithGrouping
            GROUP BY category, subcategory_grouped

            ORDER BY level, value DESC;
            """, nativeQuery = true)
    List<Object[]> getSankeyDataWithSubcategories(Integer userId, Integer year, Integer month);
    
    @Query(value = """
        WITH current_month_expenses AS (
            SELECT 
                c.id AS category_id,
                c.category AS category_name,
                COALESCE(s.id, 0) AS subcategory_id,
                COALESCE(s.sub_category, b.custom_sub_category) AS subcategory_name,
                COALESCE(s.icon_key, c.category_icon_key, 'FaMiscellaneous') AS icon,
                SUM(b.actual) AS actual_expense
            FROM budget b
            JOIN category c ON b.category_id = c.id
            LEFT JOIN sub_category s ON b.sub_category_id = s.id
            WHERE b.user_id = :userId 
              AND LOWER(c.category) NOT LIKE '%income%'
              AND YEAR(b.date) = YEAR(CURDATE())
              AND MONTH(b.date) = MONTH(CURDATE())
            GROUP BY 
                c.id, c.category, 
                COALESCE(s.id, 0), 
                COALESCE(s.sub_category, b.custom_sub_category),
                COALESCE(s.icon_key, c.category_icon_key, 'FaMiscellaneous')
        )
        SELECT 
            category_id, category_name, 
            subcategory_id, subcategory_name, 
            icon, actual_expense
        FROM current_month_expenses
        ORDER BY category_name, subcategory_name;
        """, nativeQuery = true)
    List<Object[]> getCurrentMonthBudgetExpenses(@Param("userId") Integer userId);
    
    @Query(value = """
        WITH monthly_expenses AS (
            SELECT
                c.id AS category_id,
                c.category AS category_name,
                COALESCE(s.id, 0) AS subcategory_id,
                COALESCE(s.sub_category, b.custom_sub_category) AS subcategory_name,
                COALESCE(s.icon_key, c.category_icon_key, 'FaMiscellaneous') AS icon,
                YEAR(b.date) AS year,
                MONTH(b.date) AS month,
                SUM(b.actual) AS actual_expense
            FROM budget b
            JOIN category c ON b.category_id = c.id
            LEFT JOIN sub_category s ON b.sub_category_id = s.id
            WHERE b.user_id = :userId
               AND LOWER(c.category) NOT LIKE '%income%'
              AND b.date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
              AND b.date <= CURDATE()
            GROUP BY
                c.id, c.category,
                COALESCE(s.id, 0),
                COALESCE(s.sub_category, b.custom_sub_category),
                COALESCE(s.icon_key, c.category_icon_key, 'FaMiscellaneous'),
                YEAR(b.date), MONTH(b.date)
        )
        SELECT
            category_id, category_name,
            subcategory_id, subcategory_name,
            icon, year, month, actual_expense
        FROM monthly_expenses
        ORDER BY year DESC, month DESC, category_name, subcategory_name;
        """, nativeQuery = true)
    List<Object[]> getPast12MonthsExpenses(@Param("userId") Integer userId);

    @Query(value = """
        WITH budget_temp AS (
            SELECT *, ROW_NUMBER() OVER(PARTITION BY category_id, sub_category_id ORDER BY date DESC) AS rn
            FROM budget
            WHERE user_id = :userId AND
                  category_id = :categoryId AND
                  custom_sub_category_id = :customSubCategoryId AND
                  YEAR(date) = YEAR(:date) AND
                  MONTH(date) = MONTH(:date)
            )
            SELECT actual, allocated, category_id, date, dynamic_allocated, exclude, id,
                is_dynamic, remaining, rollover, sub_category_id, user_id,
                custom_sub_category, icon, custom_sub_category_id
            FROM budget_temp WHERE rn = 1;
        """, nativeQuery = true)
    Optional<Budget> findLatestBudgetByCategoryIdAndCustomSubCategoryId(
        @Param("userId") Integer userId,
        @Param("categoryId") Integer categoryId,
        @Param("customSubCategoryId") Integer customSubCategoryId,
        @Param("date") LocalDate date
    );

    @Query("""
        SELECT b FROM Budget b
        JOIN FETCH b.category c
        LEFT JOIN FETCH b.subCategory s
        JOIN FETCH b.user u
        WHERE u.id = :userId AND
            b.date >= :startDate AND b.date <= :endDate AND
            b.rollover = true AND
            b.date = (
                SELECT MAX(b2.date) FROM Budget b2
                WHERE b2.user.id = :userId
                    AND b2.rollover = true
                    AND b2.date >= :startDate AND b2.date <= :endDate
                    AND b2.category.id = b.category.id
                    AND COALESCE(b2.customSubCategoryId, b2.subCategory.id) = COALESCE(b.customSubCategoryId, b.subCategory.id)
            )
    """)
    List<Budget> findRolloverBudgetsInRange(
        @Param("userId") Integer userId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );

    @Query("""
        SELECT b FROM Budget b
        JOIN FETCH b.category c
        LEFT JOIN FETCH b.subCategory s
        JOIN FETCH b.user u
        WHERE u.id = :userId AND
            b.date > :startDate AND
            b.category.id = :categoryId AND
            b.subCategory.id = :subCategoryId
    """)
    List<Budget> findFutureBudgetsByCategoryIdAndSubCategoryId(
        @Param("userId") Integer userId,
        @Param("categoryId") Integer categoryId,
        @Param("subCategoryId") Integer subCategoryId,
        @Param("startDate") LocalDate startDate
    );

    @Query("""
        SELECT b FROM Budget b
        JOIN FETCH b.category c
        LEFT JOIN FETCH b.subCategory s
        JOIN FETCH b.user u
        WHERE u.id = :userId AND
            b.date > :startDate AND
            b.category.id = :categoryId AND
            b.customSubCategoryId = :customSubCategoryId
    """)
    List<Budget> findFutureBudgetsByCategoryIdAndCustomSubCategoryId(
        @Param("userId") Integer userId,
        @Param("categoryId") Integer categoryId,
        @Param("customSubCategoryId") Integer customSubCategoryId,
        @Param("startDate") LocalDate startDate
    );

    @Query("""
        SELECT b FROM Budget b
        JOIN FETCH b.category c
        LEFT JOIN FETCH b.subCategory s
        JOIN FETCH b.user u
        WHERE u.id = :userId AND
            b.date = :nonRolloverDate AND
            b.rollover = false
    """)
    List<Budget> findNonRolloverBudgetsByUserAndMonth(
        @Param("userId") Integer userId,
        @Param("nonRolloverDate") LocalDate nonRolloverDate
    );

    @Query("""
        SELECT b FROM Budget b
        WHERE b.user.id = :userId AND
        MONTH(b.date) = :month AND YEAR(b.date) = :year AND
        (b.subCategory.id IS NOT NULL OR b.customSubCategoryId IS NOT NULL)
    """)
    List<Budget> findByUserWithMonthAndYearv1(Integer userId, Integer month, Integer year);
}

