package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.SvgIconDto;
import com.pennypal.fintech.dto.SvgInfoDto;
import com.pennypal.fintech.entity.SvgIcon;
import com.pennypal.fintech.service.SvgIconService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/icons")
public class SvgIconController {

    private final SvgIconService svgIconService;

    @Autowired
    public SvgIconController(SvgIconService svgIconService) {
        this.svgIconService = svgIconService;
    }

    // ✅ Returns icons with SVG content as string in DTO
    @GetMapping("/list")
    public ResponseEntity<List<SvgIconDto>> getAllIcons() {
        List<SvgIcon> icons = svgIconService.getAllIcons();
        List<SvgIconDto> iconDtos = icons.stream()
                .map(icon -> new SvgIconDto(
                        icon.getId(),
                        icon.getDescription(),
                        icon.getIconName(),
                        new String(icon.getSvgContent(), StandardCharsets.UTF_8)
                ))
                .collect(Collectors.toList());

        return ResponseEntity.ok(iconDtos);
    }

    @GetMapping("/{id}")
    public ResponseEntity<SvgIconDto> getIconById(@PathVariable Integer id) {
        return svgIconService.getIconById(id)
                .map(icon -> ResponseEntity.ok(new SvgIconDto(
                        icon.getId(),
                        icon.getDescription(),
                        icon.getIconName(),
                        new String(icon.getSvgContent(), StandardCharsets.UTF_8)
                )))
                .orElse(ResponseEntity.notFound().build());
    }

    // New endpoint that returns base64 encoded SVG content as JSON
    @GetMapping("/svg/{iconName}")
    public ResponseEntity<?> getIconByName(@PathVariable String iconName) {
        return svgIconService.getIconByName(iconName)
                .map(icon -> {
                    String svgContent = new String(icon.getSvgContent(), StandardCharsets.UTF_8);
                    // Return as JSON with the SVG content
                    return ResponseEntity.ok()
                            .contentType(MediaType.APPLICATION_JSON)
                            .body(new SvgIconDto(
                                    icon.getId(),
                                    icon.getDescription(),
                                    icon.getIconName(),
                                    svgContent
                            ));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PostMapping("/url")
    public ResponseEntity<?> saveIconFromUrl(
        @RequestBody SvgInfoDto dto) {
        
        try {
            SvgIcon savedIcon;
            if (dto.getSvgContent() != null && !dto.getSvgContent().isEmpty()) {
                // Save from provided SVG content
                savedIcon = svgIconService.saveIconFromContent(dto.getSvgContent(), dto.getIconName(), dto.getDescription());
            } else {
                // Download from URL
                savedIcon = svgIconService.saveIconFromUrl(dto.getUrl(), dto.getIconName(), dto.getDescription());

            }
            
            return ResponseEntity.status(HttpStatus.CREATED).body(savedIcon);
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body("Error processing SVG: " + e.getMessage());
        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(e.getMessage());
        }
    
    }}