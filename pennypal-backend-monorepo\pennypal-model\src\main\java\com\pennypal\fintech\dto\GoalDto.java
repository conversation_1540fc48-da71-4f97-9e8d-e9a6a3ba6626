package com.pennypal.fintech.dto;
import java.util.Map;

import com.pennypal.fintech.entity.Goal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

public class GoalDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CreateGoalRequest {
        private String goalName;
        private Double goalAmount;
        private LocalDate startDate;
        private LocalDate targetDate;
        private Goal.GoalType goalType;
        private String description;
        private List<GoalAccountAllocation> accountAllocations;
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalAccountAllocation {
        private Integer accountId;
        private Double allocationPercentage;
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UpdateGoalRequest {
        private String goalName;
        private Double goalAmount;
        private LocalDate targetDate;
        private Goal.GoalType goalType;
        private String description;
        private List<GoalAccountAllocation> accountAllocations;
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalResponse {
        private Integer id;
        private String goalName;
        private Double goalAmount;
        private Double currentAmount;
        private Double progressPercentage;
        private LocalDate startDate;
        private LocalDate targetDate;
        private Goal.GoalStatus status;
        private Goal.GoalType goalType;
        private String description;
        private List<GoalAccountResponse> accounts;
        private String timeRemaining;
        private Boolean isCompletedThisMonth;
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalAccountResponse {
        private Integer accountId;
        private String accountName;
        private Double allocationPercentage;
        private Double allocatedAmount;
        private Double currentContribution;
        private Double progressPercentage;
        // NEW: Virtual balance fields
        private Double goalAccountBalance;        // Current virtual balance in goal account
        private Double availableForContribution; // Amount available for contributions
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalSummaryResponse {
        private Integer totalGoals;
        private Integer completedGoals;
        private Integer inProgressGoals;
        private Integer failedGoals;
        private List<GoalResponse> upcomingGoals;
    }
        
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ContributeToGoalRequest {
        private Integer goalId;
        private Integer accountId;
        private Double amount;
    }

    // NEW: Request to allocate money from main account to goal account
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AllocateToGoalRequest {
        private Integer goalId;
        private Integer accountId;
        private Double amount;
        private String note; // Optional note for allocation
    }

    // NEW: Request to deallocate money from goal account back to available balance
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeallocateFromGoalRequest {
        private Integer goalId;
        private Integer accountId;
        private Double amount;
        private String reason; // Optional reason for deallocation
    }

    // NEW: Account allocation summary showing how account balance is distributed
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AccountAllocationSummary {
      private int accountId;
    private String accountName;
    private double totalBalance;
    private double totalAllocated;
    private double availableBalance;
    private List<AllocationDetail> allocations;
}
@Data
@Builder
public static class AllocationDetail {
    private int goalId;
    private String goalName;
    private double allocatedAmount;
    private double allocationPercentage;
}
    // NEW: Detail of allocation for a specific goal
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalAllocationDetail {
        private Integer goalId;
        private String goalName;
        private Double allocatedBalance;       // Amount allocated to this goal
        private Double contributedAmount;     // Amount already contributed
        private Double allocationPercentage;  // Percentage allocation for this goal
    }

    // NEW: Response for available balance check
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AvailableBalanceResponse {
        private Integer accountId;
        private String accountName;
        private Double mainAccountBalance;
        private Double totalAllocated;
        private Double availableForAllocation;
        private List<GoalAllocationSummary> existingAllocations;
    }

    // NEW: Summary of allocation for each goal
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalAllocationSummary {
        private Integer goalId;
        private String goalName;
        private Double allocatedAmount;
        private Double contributedAmount;
        private Double remainingBalance;
    }

    // NEW: Bulk allocation request for multiple goals
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkAllocateRequest {
        private Integer accountId;
        private List<BulkAllocationItem> allocations;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkAllocationItem {
        private Integer goalId;
        private Double amount;
    }

    // NEW: Response for bulk allocation
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BulkAllocateResponse {
        private Integer accountId;
        private Double totalAllocated;
        private List<GoalAllocationResult> results;
        private String message;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoalAllocationResult {
        private Integer goalId;
        private String goalName;
        private Double allocatedAmount;
        private Boolean success;
        private String message;
    }
    @Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public static class GoalProgressAnalytics {
    private int totalGoals;
    private double totalGoalAmount;
    private double totalCurrentAmount;
    private double overallProgressPercentage;
    private double averageProgressPercentage;
    private Map<String, Integer> goalsByMonth;
}

}