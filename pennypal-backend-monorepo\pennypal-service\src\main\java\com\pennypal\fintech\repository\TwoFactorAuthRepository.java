package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.TwoFactorAuth;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface TwoFactorAuthRepository extends JpaRepository<TwoFactorAuth, Integer> {
    
    Optional<TwoFactorAuth> findByUserId(Integer userId);
    
    @Query("SELECT t FROM TwoFactorAuth t WHERE t.userId = :userId AND t.isEnabled = true")
    Optional<TwoFactorAuth> findEnabledByUserId(@Param("userId") Integer userId);
    
    boolean existsByUserId(Integer userId);
    
    void deleteByUserId(Integer userId);
}
