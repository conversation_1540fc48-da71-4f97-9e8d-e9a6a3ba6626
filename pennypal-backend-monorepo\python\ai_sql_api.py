from flask import Flask, request, jsonify
from flask_limiter import Limiter
from flask_limiter.errors import RateLimitExceeded
from flask_limiter.util import get_remote_address
import google.generativeai as genai
import mysql.connector
import json
import logging
from dotenv import load_dotenv
import os

from faiss_server import add_entry, search
from category_mapping import store_category_mapping, search_category_mapping

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Load environment variables from .env
load_dotenv()

# Configure Gemini
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))
model = genai.GenerativeModel()

def get_db_connection():
    return mysql.connector.connect(
        host=os.getenv("MYSQL_HOST"),
        port=os.getenv("MYSQL_PORT"),
        user=os.getenv("MYSQL_USER"),
        password=os.getenv("MYSQL_PASSWORD"),
        database=os.getenv("MYSQL_DATABASE")
    )

app = Flask(__name__)

# Item Categorization Prompt
ITEM_CATEGORIZATION_PROMPT = """
You are an expert at categorizing shopping items for personal finance tracking. 
Given a list of receipt items, categorize each one into one of these standard categories:

[GROCERIES, DINING, TRANSPORTATION, ENTERTAINMENT, SHOPPING, UTILITIES, HEALTH, TRAVEL, EDUCATION, OTHER]

Consider the following guidelines:
1. Food items typically belong to GROCERIES
2. Restaurant meals belong to DINING
3. Gasoline, public transport, taxis belong to TRANSPORTATION
4. Movies, games, hobbies belong to ENTERTAINMENT
5. Clothing, electronics belong to SHOPPING
6. Electricity, water, internet belong to UTILITIES
7. Medicine, doctor visits belong to HEALTH
8. Flights, hotels belong to TRAVEL
9. Books, courses belong to EDUCATION
10. When in doubt, use OTHER

Return a JSON object where keys are the item names and values are the categories.

Items to categorize: {items}
"""
# Use user_id from request for per-user rate limiting
def get_user_key():
    try:
        return str(request.json.get("user_id"))
    except:
        return "anonymous"

# Configure rate limiter: 10 requests per hour per user
limiter = Limiter(
    app=app,
    key_func=get_remote_address,  # Using built-in function
    default_limits=["10 per hour"],
    storage_uri="memory://"  # Correct parameter name
)

# Custom handler for rate limit errors
@app.errorhandler(RateLimitExceeded)
def rate_limit_handler(e):
    return jsonify({
        "error": "Rate limit exceeded",
        "message": (
            "You've reached your limit of 10 requests per hour. "
            "Please try again later or upgrade your plan for higher limits."
        )
    }), 429

@app.route('/generate-sql', methods=['POST'])
@limiter.limit("10 per hour")
def generate_sql():
    data = request.get_json()
    user_query = data.get("user_query")
    previous_queries = data.get("previous_queries", [])
    user_id = data.get("user_id", 0)

    # Construct the prompt
    prompt = f"""
    You are an expert MySQL generator.
    Convert this natural language query to MySQL SELECT query:
    {user_query}

    Here are the user's previous 5 queries for context:
    {previous_queries}

    1. Always add a WHERE user_id = {user_id} filter to all tables.
    2. Use only the relevant tables. Return only the SQL query.
    3. If the query involves modifying the database (i.e., any `UPDATE`, `INSERT`, or `DELETE` operations), you must respond with the following message:
    > "I cannot process requests that modify the database. Please ensure your query is only for retrieving data and not for altering or adding information."
    4. If the query is ambiguous or cannot be answered with the provided data schema, you must respond with the following message:
    > "I'm sorry, but I cannot answer that question with the information provided."

    Table: transactions
    Columns: user_id, account_id, transaction_date, transaction_amount, description (which has merchant name), category.
    If users asks to filter by a generic name like paychecks, airlines, taxi, etc, filter by category column in transactions table.
    
    For generic names, consider synonyms.
    For example, 
    (1) If user asks for "airlines", also consider "aviation", "flight", "airfare" etc.
    (2) If user asks for "paychecks", also consider "payroll", "salary", "income" etc.
    Consider other synonyms as well.
    
    If user asks to filter by a specific merchant name, filter by description column only.
    Don't filter by exact match. Use LIKE operator and wildcards.

    Table: accounts
    Columns: user_id, id, account_category (cash/checking/savings), account_name (Bank of America, Plaid Cash, Plaid Checking, Plaid Saving etc), account_type (credit/depository/loan/investments)

    Table: account_balances
    Columns: user_id, account_id, balance, timestamp
    account_id is Foreign Key of 'id' in accounts table
    Each account_id will have mutiple entries based on date. Pick the latest entry unless a specific date mentioned by user,

    Table: budget
    Columns: user_id, category_id, sub_category_id, date, actual, allocated, date
    Each category_id + sub_category_id will have mutiple entries based on date. Pick the latest entry unless a specific date mentioned by user.
    Pick category and sub_category names from respective tables based on category_id and sub_category_id

    Table: category
    Columns: id, category (which has name)
    id is Foreign Key of 'category_id' in budget table
    category other than 'income' is considered as 'expense'

    Table: sub_category
    Columns: id, sub_category (which has name)
    id is Foreign Key of 'sub_category_id' in budget table
    """

    # Get the SQL query from Gemini
    response = model.generate_content(prompt)
    if "I cannot process requests that modify the database" in response.text:
        return jsonify({"error": "I cannot process requests that modify the database. Please ensure your query is only for retrieving data and not for altering or adding information."})
    sql_query = response.text.strip().strip("```sql").strip("```")

    # Run the query on MySQL
    try:
        conn = mysql.connector.connect(
            host=os.getenv("MYSQL_HOST"),
            port=os.getenv("MYSQL_PORT"),
            user=os.getenv("MYSQL_USER"),
            password=os.getenv("MYSQL_PASSWORD"),
            database=os.getenv("MYSQL_DATABASE")
        )
        cursor = conn.cursor()
        cursor.execute(sql_query)
        result = cursor.fetchall()
        conn.close()
        
        natural_language_prompt = f"""
        You are a financial assistant.

        Given:
        - user_query: {user_query}
        - SQL result (list of tuples): {result}

        Return JSON with:
        - summary: Natural language brief summary
        - visualization (optional): Only include if helpful

        Chart rules:
        - pie_chart:
          Use for breakdown, split, distribution, proportion, share
          One category + one value (e.g., spending by category)
        - bar_chart:
          Compare values across categories (e.g., spending by month, count per merchant)
        - line_chart:
          Trends over time (e.g., balances by date)
        - table:
          Detailed records with 2+ columns (e.g., list of transactions)
        - None:
          Single value results (e.g., total balance)

        If using visualization, include:
        - type: "pie_chart" | "bar_chart" | "line_chart" | "table"
        - title: Short chart title
        - data: List of (label, value) in dicts or rows (for table)

        Respond only with valid JSON.
        """

        response = model.generate_content(natural_language_prompt)
        
        try:
            json_response = json.loads(response.text)
        except Exception as e:
            # fallback if LLM doesn't return clean JSON
            return jsonify({
                "sql_query": sql_query,
                "result": result,
                "response": response.text.strip()
            })

        # Save in vector DB
        add_entry_route(user_query, sql_query)

        return jsonify(
            {
                "sql_query": sql_query,
                "result": result,
                "response": json_response.get("summary", ""),
                "visualization": json_response.get("visualization", {})
            }
        )

    except Exception as e:
        return jsonify({"error": str(e)}), 500

@app.route('/categorize-items', methods=['POST'])
@limiter.limit("10 per hour")
def categorize_items():
    try:
        data = request.get_json()
        items = data.get("items", [])
        
        if not items:
            return jsonify({"error": "No items provided"}), 400
        
        prompt = ITEM_CATEGORIZATION_PROMPT.format(items=items)
        response = model.generate_content(prompt)
        
        try:
            categories = json.loads(response.text)
            return jsonify(categories)
        except json.JSONDecodeError:
            return jsonify({
                "error": "Invalid response format",
                "response": response.text
            }), 500
            
    except Exception as e:
        logger.error(f"Item categorization error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@app.route('/categorize-receipt-items', methods=['POST'])
@limiter.limit("10 per hour")
def categorize_receipt_items():
    try:
        data = request.get_json()
        items = data.get("items", [])
        user_id = data.get("user_id", 0)

        if not items:
            return jsonify({"error": "No items provided"}), 400

        conn = get_db_connection()
        cursor = conn.cursor(dictionary=True)

        categorized_items = []
        items_to_categorize = []

        # Step 1: Check transaction history
        for item in items:
            item_name = item.get("name", "")
            cursor.execute("""
                SELECT category 
                FROM transactions 
                WHERE user_id = %s AND description LIKE %s
                ORDER BY transaction_date DESC
                LIMIT 1
            """, (user_id, f"%{item_name}%"))
            result = cursor.fetchone()

            if result:
                categorized_items.append({
                    "item": item_name,
                    "category": result["category"],
                    "source": "transaction_history"
                })
            else:
                items_to_categorize.append(item_name)

        # Step 2: Check category mapping
        if items_to_categorize:
            category_mapping_response = search_category_mapping(items_to_categorize)
            if isinstance(category_mapping_response, dict):
                for item_name in items_to_categorize:
                    if item_name in category_mapping_response:
                        categorized_items.append({
                            "item": item_name,
                            "category": category_mapping_response[item_name],
                            "source": "category_mapping"
                        })
                    else:
                        categorized_items.append({
                            "item": item_name,
                            "category": None,
                            "source": "not_found"
                        })

        # Step 3: Use AI for remaining items
        uncategorized_items = [item["item"] for item in categorized_items if item["category"] is None]
        if uncategorized_items:
            ai_response = categorize_items_helper(uncategorized_items)
            if isinstance(ai_response, dict):
                for item in categorized_items:
                    if item["category"] is None and item["item"] in ai_response:
                        item["category"] = ai_response[item["item"]]
                        item["source"] = "ai_categorization"

        # ✅ Log the items (inside try block)
        for item in categorized_items:
            print(f"[RECEIPT ITEM] Item: {item['item']} | Category: {item['category']} | Source: {item['source']}")

        conn.close()
        return jsonify({"items": categorized_items})

    except Exception as e:
        logger.error(f"Receipt categorization error: {str(e)}")
        return jsonify({"error": str(e)}), 500


def categorize_items_helper(items):
    try:
        prompt = ITEM_CATEGORIZATION_PROMPT.format(items=items)
        response = model.generate_content(prompt)
        return json.loads(response.text)
    except Exception as e:
        logger.error(f"AI categorization helper error: {str(e)}")
        return {item: "OTHER" for item in items}
    
@app.route('/add-entry', methods=['POST'])
def add_entry_route(user_query, sql_query):
    return add_entry(user_query, sql_query)

@app.route('/search', methods=['POST'])
def search_route():
    return search()

@app.route('/store-category', methods=['POST'])
def store_category():
    return store_category_mapping()

@app.route('/search-category', methods=['POST'])
def search_category():
    data = request.get_json()
    queries = data.get("queries")
    logger.info("Queries: %s", queries)
    return search_category_mapping(queries)

if __name__ == '__main__':
    app.run(port=5000, debug=True)