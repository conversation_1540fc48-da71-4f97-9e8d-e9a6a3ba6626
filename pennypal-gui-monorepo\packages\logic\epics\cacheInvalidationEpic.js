import { ofType, combineEpics } from 'redux-observable';
import { of, from } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import {
  invalidateTransactionCache,
  invalidateRecurringTransactionCache,
  invalidateBudgetCache,
  invalidateAllTransactionRelatedCache,
  invalidateR<PERSON><PERSON>ptCache,
  invalidateC<PERSON>botCache,
  invalidateSubcategoryCache,
  invalidateAccountCache,
  fetchTransactionsStart,
  fetchRecurringTransactionsStart,
  fetchFutureRecurringTransactionsStart,
  fetchBudgetSummaryStart,
  fetchBudgetDataStart,
  fetchTransactionSummaryStart,
  fetchHiddenTransactionsStart,
  fetchReconcileDataStart,
  fetchReceiptTransactionIdsStart,
  fetchReceiptItemsStart,
  fetchReceiptSummaryStart,
  fetchUserReceiptsStart,
  fetchChatbotHistoryStart,
  fetchDistinctSubcategoriesStart,
  fetchAccountIdsStart,
  fetchUserAccountsStart,
  fetchAccountBalancesInvestmentStart,
  fetchAccountBalancesDepositoryStart,
  fetchAccountBalancesLoanStart,
  fetchAccountBalancesCreditStart,
  invalidatePaymentSubscriptionCache,
  invalidatePaymentMethodsCache,
  fetchPaymentSubscriptionStart,
  fetchPaymentMethodsStart,
  fetchPaymentProductsStart,
  fetchPaymentInvoicesStart,
  fetchUpcomingInvoiceStart
} from '../redux/cacheSlice';

// Epic to invalidate cache when transactions are added/modified/deleted
export const invalidateOnTransactionChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      // Transaction actions that actually exist in the codebase
      'transactions/addTransactionSuccess',
      'transactions/updateTransactionSuccess',
      'transactions/deleteTransactionSuccess',
      'transactions/hideFromBudgetSuccess',

      // Split transaction actions that exist
      'splitTransaction/submitSplitSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Transaction data changed, invalidating related cache:', action.type);

      // Invalidate transaction and budget related cache (includes subcategories)
      return of(invalidateAllTransactionRelatedCache());
    })
  );

// Epic to invalidate cache when recurring transactions/contributions are modified
export const invalidateOnRecurringTransactionChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      // Recurring contribution actions that actually modify data
      'recurring/setupRecurringContributionSuccess',
      'recurring/updateRecurringContributionSuccess',
      'recurring/deleteRecurringContributionSuccess',
      'recurring/triggerContributionSuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 Recurring transaction data changed, invalidating recurring cache:', action.type);

      // Only invalidate recurring transaction cache, not all transaction data
      return of(invalidateRecurringTransactionCache());
    })
  );

// Epic to invalidate cache when accounts are synced (new transactions imported)
export const invalidateOnAccountSyncEpic = (action$) =>
  action$.pipe(
    ofType(
      // Account sync actions that actually import new transactions
      'accounts/refreshAllAccounts/fulfilled',
      'accounts/syncAccount/fulfilled',
      'accounts/exchangePublicToken/fulfilled'
    ),
    mergeMap((action) => {
      console.log('🔄 Account sync completed, invalidating all transaction and account-related cache:', action.type);

      // When accounts are synced, new transactions may be imported and account data changes
      // Invalidate all transaction, budget, and account related cache
      return from([
        invalidateAllTransactionRelatedCache(),
        invalidateAccountCache()
      ]);
    })
  );

// Epic to invalidate cache when budget is created/modified
export const invalidateOnBudgetChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      // Budget actions that actually exist in the codebase
      'budget/addBudget',
      'budget/updateBudget',
      'budget/deleteSubcategory',
      'budget/saveBudget',
      'budget/addBudgetItem'
    ),
    mergeMap((action) => {
      console.log('🔄 Budget data changed, invalidating budget and subcategory cache:', action.type);

      // Invalidate budget and subcategory cache (budget changes can affect subcategories)
      return from([
        invalidateBudgetCache(),
        invalidateSubcategoryCache()
      ]);
    })
  );

// Epic to invalidate cache when receipts are uploaded/saved/modified
export const invalidateOnReceiptChangeEpic = (action$) =>
  action$.pipe(
    ofType(
      // Receipt actions that exist in the codebase
      'receipts/uploadReceiptSuccess',
      'receipts/saveReceiptSuccess',
      'receipts/addNewTransaction'
    ),
    mergeMap((action) => {
      console.log('🔄 Receipt data changed, invalidating receipt cache:', action.type);

      // Invalidate receipt-related cache
      return of(invalidateReceiptCache());
    })
  );

// Epic to invalidate cache when new chatbot queries are made
export const invalidateOnChatbotQueryEpic = (action$) =>
  action$.pipe(
    ofType(
      // Listen for successful chatbot queries
      'chatbot/querySuccess'
    ),
    mergeMap((action) => {
      console.log('🔄 New chatbot query, invalidating chatbot cache:', action.type);

      // Invalidate chatbot history cache
      return of(invalidateChatbotCache());
    })
  );

// Epic to invalidate payment subscription cache when subscription is added/updated
export const invalidatePaymentSubscriptionEpic = (action$) =>
  action$.pipe(
    ofType(
      'payment/startSubscription',
      'payment/updateSubscription',
      'payment/cancelSubscription',
      'payment/subscriptionUpdated'
    ),
    mergeMap((action) => {
      console.log('🔄 Subscription changed, invalidating payment subscription cache:', action.type);

      // Invalidate subscription, invoices, and upcoming invoice cache
      return of(invalidatePaymentSubscriptionCache());
    })
  );

// Epic to invalidate payment methods cache when payment method is added or default changed
export const invalidatePaymentMethodsEpic = (action$) =>
  action$.pipe(
    ofType(
      'payment/addPaymentMethod',
      'payment/setDefaultPaymentMethod',
      'payment/removePaymentMethod',
      'payment/paymentMethodUpdated'
    ),
    mergeMap((action) => {
      console.log('🔄 Payment method changed, invalidating payment methods cache:', action.type);

      // Invalidate payment methods cache
      return of(invalidatePaymentMethodsCache());
    })
  );

// Epic to automatically refetch data after cache invalidation
export const refetchAfterInvalidationEpic = (action$, state$) =>
  action$.pipe(
    ofType(
      invalidateAllTransactionRelatedCache.type,
      invalidateTransactionCache.type,
      invalidateRecurringTransactionCache.type,
      invalidateBudgetCache.type,
      invalidateReceiptCache.type,
      invalidateChatbotCache.type,
      invalidateSubcategoryCache.type,
      invalidateAccountCache.type,
      invalidatePaymentSubscriptionCache.type,
      invalidatePaymentMethodsCache.type
    ),
    mergeMap((action) => {
      console.log('🔄 Cache invalidated, triggering refetch:', action.type);
      
      const userId = state$.value?.auth?.user?.id;
      if (!userId) {
        console.warn('⚠️ User ID not found, skipping refetch');
        return of({ type: 'cache/refetchSkipped' });
      }
      
      const actions = [];
      
      // Determine what to refetch based on what was invalidated
      if (action.type === invalidateAllTransactionRelatedCache.type) {
        // Refetch everything transaction-related
        actions.push(
          fetchTransactionsStart(),
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart(),
          fetchBudgetDataStart({ userId }),
          fetchTransactionSummaryStart({ userId }),
          fetchHiddenTransactionsStart({ userId }),
          fetchReconcileDataStart()
        );

        // Also refetch budget summary for current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        actions.push(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth }));

      } else if (action.type === invalidateTransactionCache.type) {
        actions.push(fetchTransactionsStart());

      } else if (action.type === invalidateRecurringTransactionCache.type) {
        actions.push(
          fetchRecurringTransactionsStart(),
          fetchFutureRecurringTransactionsStart()
        );

      } else if (action.type === invalidateBudgetCache.type) {
        actions.push(fetchBudgetDataStart({ userId }));

        // Also refetch budget summary for current month
        const now = new Date();
        const currentYear = now.getFullYear();
        const currentMonth = now.getMonth() + 1;
        actions.push(fetchBudgetSummaryStart({ userId, year: currentYear, month: currentMonth }));

      } else if (action.type === invalidateReceiptCache.type) {
        // Refetch all receipt-related data
        actions.push(
          fetchReceiptTransactionIdsStart(),
          fetchReceiptItemsStart(),
          fetchReceiptSummaryStart(),
          fetchUserReceiptsStart({ userId })
        );

      } else if (action.type === invalidateChatbotCache.type) {
        // Refetch chatbot history
        actions.push(fetchChatbotHistoryStart({ userId }));

      } else if (action.type === invalidateSubcategoryCache.type) {
        // Refetch distinct subcategories
        actions.push(fetchDistinctSubcategoriesStart({ userId }));

      } else if (action.type === invalidateAccountCache.type) {
        // Refetch all account-related data with default x=12, y=30 parameters
        actions.push(
          fetchAccountIdsStart({ userId }),
          fetchUserAccountsStart({ userId }),
          fetchAccountBalancesInvestmentStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesDepositoryStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesLoanStart({ userId, x: 12, y: 30 }),
          fetchAccountBalancesCreditStart({ userId, x: 12, y: 30 })
        );

      } else if (action.type === invalidatePaymentSubscriptionCache.type) {
        // Refetch payment subscription and invoices
        // Note: upcoming invoice will be automatically fetched after subscription is loaded
        // via fetchUpcomingInvoiceAfterSubscriptionEpic
        actions.push(
          fetchPaymentSubscriptionStart({ userId }),
          fetchPaymentInvoicesStart({ userId })
        );

      } else if (action.type === invalidatePaymentMethodsCache.type) {
        // Refetch payment methods
        actions.push(fetchPaymentMethodsStart({ userId }));
      }

      // Return all refetch actions
      return actions.length > 0 ? of(...actions) : of({ type: 'cache/noRefetchNeeded' });
    })
  );

// Combined cache invalidation epic
export const cacheInvalidationEpic = combineEpics(
  invalidateOnTransactionChangeEpic,
  invalidateOnRecurringTransactionChangeEpic,
  invalidateOnAccountSyncEpic,
  invalidateOnBudgetChangeEpic,
  invalidateOnReceiptChangeEpic,
  invalidateOnChatbotQueryEpic,
  invalidatePaymentSubscriptionEpic,
  invalidatePaymentMethodsEpic,
  refetchAfterInvalidationEpic
);

export default cacheInvalidationEpic;