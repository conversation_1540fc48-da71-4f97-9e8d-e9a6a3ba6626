package com.pennypal.fintech.entity;

import java.time.LocalDateTime;

import jakarta.persistence.*;

import lombok.Data;

@Entity
@Table(name = "users_group")
@Data
public class UsersGroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Integer userId;

    @ManyToOne
    @JoinColumn(name = "group_id", foreignKey = @ForeignKey(name = "users_group_ibfk_2"))
    private AclGroups group;

    private String status;

    private LocalDateTime expiresAt;
}