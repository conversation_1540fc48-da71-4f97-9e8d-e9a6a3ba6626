package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.ReceiptDto;
import com.pennypal.fintech.service.ReceiptService;
// import com.pennypal.fintech.util.FileCompressionUtil;
import com.pennypal.fintech.entity.Receipts;
import com.pennypal.fintech.entity.Transactions;
import com.pennypal.fintech.repository.ReceiptRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.io.IOException;
import java.nio.file.*;
import java.util.*;

@RestController
@RequestMapping("/api/receipts")
public class ReceiptController {
    @Autowired
    private ReceiptService receiptService;

    
    @Autowired
    private ReceiptRepository receiptRepository;
   
    @Value("${file.upload-dir}")
    private String uploadDir;

   @PostMapping("/uploadReceipt")
    public ResponseEntity<Map<String, Object>> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("docName") String docName,
            @RequestParam("docType") String docType,
            @RequestParam("category") String category,
            @RequestParam(value = "qrData", required = false) String qrData,
            @RequestParam("size") Double size) {
        try {
            if (file.isEmpty()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "No file uploaded");
            }
          
            String originalFileName = file.getOriginalFilename();
            String uuid = UUID.randomUUID().toString();
            String newFileName = uuid + "_" + originalFileName;
    
            // Save file locally
            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            Path filePath = uploadPath.resolve(newFileName);
            Files.write(filePath, file.getBytes());
   
            System.out.println("File saved to: " + filePath.toString());

            String imageUrl = "/uploads/" + newFileName;
    
            // Process the file based on document type
            Map<String, Object> responseData;
            if (docType.equalsIgnoreCase("receipt")) {
                responseData = receiptService.processReceipt(file);
            } else if (docType.equalsIgnoreCase("id")) {
                responseData = receiptService.processIdDocument(file);
            } else if (docType.equalsIgnoreCase("invoice")) {
                responseData = receiptService.processInvoiceDocument(file);
            } else if (docType.equalsIgnoreCase("tax")) {
                responseData = receiptService.processTaxDocument(file);
            } else if (docType.equalsIgnoreCase("contract")) {
                responseData = receiptService.processContractDocument(file);
            } else {
                responseData = new HashMap<>();
            }
            
            // Add common document data
            responseData.put("savedFilePath", imageUrl);
            responseData.put("docName", docName);
            responseData.put("docType", docType);
            responseData.put("category", category);
            responseData.put("size", size);
            responseData.put("qrData", qrData);
            
            return ResponseEntity.ok(responseData);
    
        } catch (IOException e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "File upload failed", e);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error processing document", e);
        }
    }

    // Simplified endpoint for receipts only
 @PostMapping("/upload")
public ResponseEntity<?> uploadReceipts(@RequestParam("file") List<MultipartFile> files) {
    if (files == null || files.isEmpty()) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", "No files provided"));
    }

    List<Map<String, Object>> results = new ArrayList<>();
    for (MultipartFile file : files) {
        if (file.isEmpty()) {
            results.add(Map.of("error", "Empty file: " + file.getOriginalFilename()));
            continue;
        }

        try {
            String originalFileName = file.getOriginalFilename();
            String uuid = UUID.randomUUID().toString();
            String newFileName = uuid + "_" + originalFileName;

            Path uploadPath = Paths.get(uploadDir);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }

            Path filePath = uploadPath.resolve(newFileName);
            Files.write(filePath, file.getBytes());
            System.out.println("File saved to: " + filePath.toString());

            String imageUrl = "/uploads/" + newFileName;

            Map<String, Object> receiptResponse = receiptService.processReceipt(file);
            receiptResponse.put("savedFilePath", imageUrl);
            receiptResponse.put("docType", "receipt");

            results.add(receiptResponse);
        } catch (IOException e) {
            System.err.println("IO error for file " + file.getOriginalFilename() + ": " + e.getMessage());
            results.add(Map.of("error", "Upload failed for " + file.getOriginalFilename() + ": " + e.getMessage()));
        } catch (Exception e) {
            System.err.println("Processing error for file " + file.getOriginalFilename() + ": " + e.getMessage());
            results.add(Map.of("error", "Processing error for " + file.getOriginalFilename() + ": " + e.getMessage()));
        }
    }

    return results.stream().anyMatch(r -> r.containsKey("error"))
        ? ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(results)
        : ResponseEntity.ok(results);
}

    // Common save endpoint for both
    @PostMapping("/saveReceipt")
    public ResponseEntity<Map<String, Object>> saveReceipt(@RequestBody Map<String, Object> receiptData) {
        try {
            System.out.println("Received receipt data: " + receiptData);  
            
            String savedFilePath = (String) receiptData.get("savedFilePath");
            
            if (savedFilePath != null && !savedFilePath.isEmpty()) {
                Path originalPath = Paths.get(uploadDir, Paths.get(savedFilePath).getFileName().toString());
                Path scannedDir = Paths.get(uploadDir, "scanned-copy");
                if (!Files.exists(scannedDir)) {
                    Files.createDirectories(scannedDir);
                }
    
                Path scannedPath = scannedDir.resolve(originalPath.getFileName());
                Files.copy(originalPath, scannedPath, StandardCopyOption.REPLACE_EXISTING);
    
                String scannedFileUrl = "/uploads/scanned-copy/" + scannedPath.getFileName().toString();
                receiptData.put("scannedCopyPath", scannedFileUrl);
            }
            
            Receipts savedReceipt = receiptService.saveReceiptToDatabase(receiptData);
            
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Receipt saved successfully");
            response.put("id", savedReceipt.getId());
            response.put("docName", savedReceipt.getDocName());
            response.put("filePath", receiptData.get("savedFilePath"));
            response.put("scannedCopyPath", receiptData.get("scannedCopyPath"));
            response.put("transTotal", savedReceipt.getTransTotal());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error saving receipt: " + e.getMessage(), e);
        }
    }

    @DeleteMapping("/delete/{id}")
public ResponseEntity<?> deleteReceipt(@PathVariable Integer id) {
    try {
        boolean deleted = receiptService.deleteReceiptById(id);
        if (deleted) {
            return ResponseEntity.ok().body(Map.of("message", "Receipt deleted successfully", "id", id));
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("error", "Receipt not found"));
        }
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "Error deleting receipt"));
    }
}

    @GetMapping("/all")
public ResponseEntity<List<Receipts>> getAllReceipts() {
    try {
        List<Receipts> receipts = receiptRepository.findAll();
        return ResponseEntity.ok(receipts);
    } catch (Exception e) {
        e.printStackTrace();
        throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch receipts", e);
    }
}
//user based fetch
@GetMapping("/user/{userId}")
public ResponseEntity<List<Receipts>> getReceiptsByUserId(@PathVariable Long userId) {
    try {
        List<Receipts> receipts = receiptRepository.findByUserId(userId);
        return ResponseEntity.ok(receipts);
    } catch (Exception e) {
        e.printStackTrace();
        throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Failed to fetch receipts", e);
    }
}


 @GetMapping("/getReceiptDetails/{transactionId}")
    public ResponseEntity<Map<String, Object>> getReceiptDetails(@PathVariable int transactionId) {
        try {
            Map<String, Object> receiptDetails = receiptService.getReceiptDetailsByTransactionId(transactionId);
            return ResponseEntity.ok(receiptDetails);
        } catch (Exception e) {
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error fetching receipt details", e);
        }
    }
    @GetMapping("/getReceiptTransactionIds")
    public ResponseEntity<List<Integer>> getReceiptTransactionIds() {
        List<Integer> transactionIds = receiptService.getReceiptTransactionIds();
        return ResponseEntity.ok(transactionIds);
    }

    @GetMapping("/getDocumentDetails/{documentId}")
public ResponseEntity<Map<String, Object>> getDocumentDetails(@PathVariable int documentId) {
    try {
        Map<String, Object> documentDetails = receiptService.getDocumentDetails(documentId);
         // For receipts, ensure amount is included
         if ("receipt".equalsIgnoreCase((String)documentDetails.get("docType"))) {
            Double transTotal = documentDetails.get("transTotal") != null ? 
                Double.parseDouble(documentDetails.get("transTotal").toString()) : 0.0;
            documentDetails.put("amount", transTotal);
        } else {
            documentDetails.put("amount", 0.0);
        }
        
        return ResponseEntity.ok(documentDetails);
    } catch (Exception e) {
        throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error fetching document details", e);
    }
}
    
}
