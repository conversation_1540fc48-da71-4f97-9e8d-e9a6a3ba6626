package com.pennypal.fintech.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.SplitTransaction;

import io.lettuce.core.dynamic.annotation.Param;

@Repository
public interface SplitTransactionRepository extends JpaRepository<SplitTransaction, Integer> {
@Query("SELECT st FROM SplitTransaction st JOIN st.user u WHERE u.id = :userId")
    List<SplitTransaction> findByUserId(@Param("userId") Integer userId);
}