package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.InvoicePreviewResponseDto;
import com.pennypal.fintech.dto.InvoiceUpcomingResponseDto;
import com.pennypal.fintech.dto.InvoiceLineItemDto;
import com.pennypal.fintech.dto.StripeInvoiceDto;
import com.pennypal.fintech.dto.StripeProductsDto;
import com.pennypal.fintech.service.PaymentService;
import com.pennypal.fintech.repository.StripeSubscriptionRepository;

import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.Invoice;
import com.stripe.model.InvoiceLineItem;
import com.stripe.model.PaymentIntent;
import com.stripe.model.PaymentMethod;
import com.stripe.model.SetupIntent;
import com.stripe.model.Subscription;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import com.stripe.param.CustomerUpdateParams;
import com.stripe.param.InvoiceUpcomingParams;
import com.stripe.param.PaymentIntentCreateParams;
import com.stripe.param.PaymentMethodAttachParams;
import com.stripe.param.PaymentMethodListParams;
import com.stripe.param.SetupIntentCreateParams;
import com.stripe.param.SubscriptionCreateParams;
import com.stripe.param.SubscriptionUpdateParams;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
@RequestMapping("/api/v1/payment")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private StripeSubscriptionRepository stripeSubscriptionRepository;

    @PostMapping("/create-checkout-session/{userId}")
    public Map<String, String> createCheckoutSession(
            @PathVariable Integer userId,
            @RequestBody Map<String, Object> request
    ) throws Exception {
        String productName = request.get("productName").toString();
        Long amount = Long.valueOf(request.get("amount").toString());
        
        SessionCreateParams params = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl("https://example.com/success")
                .setCancelUrl("https://example.com/cancel")
                .putMetadata("userId", String.valueOf(userId))
                .putMetadata("productName", productName)
                .addLineItem(
                        SessionCreateParams.LineItem.builder()
                                .setQuantity(1L)
                                .setPriceData(
                                        SessionCreateParams.LineItem.PriceData.builder()
                                                .setCurrency("usd")
                                                .setUnitAmount(amount)
                                                .setProductData(
                                                        SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                                                .setName(productName)
                                                                .build()
                                                )
                                                .build()
                                )
                                .build()
                )
                .build();

        Session session = Session.create(params);

        Map<String, String> response = new HashMap<>();
        response.put("message", "Checkout session created successfully.");
        response.put("checkoutUrl", session.getUrl());
        return response;
    }

    @PostMapping("create-payment-intent")
    public Map<String, String> createPaymentIntent(
        @PathVariable Integer userId,
        @RequestBody Map<String, Object> request) throws Exception {

        // String Stripe.apiKey = System.getenv("STRIPE_API_KEY");
        
        Long amount = Long.valueOf(request.get("amount").toString());
        boolean saveCard = (boolean) request.getOrDefault("saveCard", false);

        try {
                PaymentIntentCreateParams.Builder createParamsBuilder = PaymentIntentCreateParams.builder()
                .setAmount(amount)
                .setCurrency("usd")
                .putMetadata("userId", String.valueOf(userId))
                .setAutomaticPaymentMethods(
                        PaymentIntentCreateParams.AutomaticPaymentMethods.builder()
                        .setEnabled(true)
                        .build()
                );
    
                // If saveCard is true, set setup_future_usage to off_session - tells Stripe to save the card for future use
                if (saveCard) {
                        createParamsBuilder.setSetupFutureUsage(PaymentIntentCreateParams.SetupFutureUsage.OFF_SESSION);
                }
        
                PaymentIntent paymentIntent = PaymentIntent.create(createParamsBuilder.build());
        
                Map<String, String> responseData = new HashMap<>();
                responseData.put("clientSecret", paymentIntent.getClientSecret());
        
                return responseData;

        } catch (StripeException e) {
            e.printStackTrace();
            return null;
        }
    }

    // This method is used to create a SetupIntent which is used to save a card for future use
    // and/or to make a payment
    @PostMapping("/create-setup-intent")
    public ResponseEntity<Map<String, String>> createSetupIntent(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside createSetupIntent method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String customerId = paymentService.getOrCreateStripeCustomer(userId);
        log.info("customerId: " + customerId);

        SetupIntent setupIntent = SetupIntent.create(
            SetupIntentCreateParams.builder()
                .setCustomer(customerId)
                .addPaymentMethodType("card")
                .setUsage(SetupIntentCreateParams.Usage.OFF_SESSION)
                .putMetadata("userId", String.valueOf(userId))
                .build()
        );
        log.info("setupIntent: " + setupIntent);
        log.info("setupIntent.getClientSecret(): " + setupIntent.getClientSecret());

        return ResponseEntity.ok(Map.of("clientSecret", setupIntent.getClientSecret()));
    }

    // This method is used to start a subscription for a user
    @PostMapping("/start-subscription")
    public ResponseEntity<String> startSubscription(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside startSubscription method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String paymentMethodId = payload.get("paymentMethodId");
        log.info("paymentMethodId: " + paymentMethodId);
        String priceId = payload.get("priceId");
        log.info("priceId: " + priceId);
        String frequency = payload.get("frequency");
        log.info("frequency: " + frequency);

        String customerId = paymentService.getOrCreateStripeCustomer(userId);
        log.info("customerId: " + customerId);

        // Attach payment method
        PaymentMethod pm = PaymentMethod.retrieve(paymentMethodId);
        log.info("pm: " + pm);
        pm.attach(PaymentMethodAttachParams.builder().setCustomer(customerId).build());
        log.info("pm attached");

        // Set default payment method
        Customer customer = Customer.retrieve(customerId);
        customer.update(CustomerUpdateParams.builder()
            .setInvoiceSettings(CustomerUpdateParams.InvoiceSettings.builder()
                .setDefaultPaymentMethod(paymentMethodId)
                .build())
            .build());
        log.info("Customer updated");
        log.info("Customer: " + customer);

        // Detect new user
        if (!stripeSubscriptionRepository.existsByUserId(userId)) {
            log.info("New user detected");
            SubscriptionCreateParams.Builder subBuilder = SubscriptionCreateParams.builder()
                .setCustomer(customer.getId())
                .addItem(SubscriptionCreateParams.Item.builder().setPrice(priceId).build())
                .putMetadata("userId", String.valueOf(userId));

            // Apply trial and discount for new users
            log.info("frequency: " + frequency);
            if ("yearly".equalsIgnoreCase(frequency)) {
                // Apply 3-month trial (approx. 90 days)
                // subBuilder.setTrialPeriodDays(90L);
                // Apply 20% off using promo coupon
                subBuilder.setCoupon("Xv1wam6J");
            } else if ("monthly".equalsIgnoreCase(frequency)) {
                // Apply 1-month trial
                // subBuilder.setTrialPeriodDays(30L);
            }
            Subscription.create(subBuilder.build());
            return ResponseEntity.ok("Subscription started");
        }

        Subscription subscription = Subscription.create(
            SubscriptionCreateParams.builder()
                .setCustomer(customerId)
                .addItem(SubscriptionCreateParams.Item.builder().setPrice(priceId).build())
                .putMetadata("userId", String.valueOf(userId))
                .build()
        );

        log.info("Subscription created");
        log.info("Subscription ID: " + subscription.getId());
        log.info("Subscription status: " + subscription.getStatus());
        log.info("Subscription current period start: " + subscription.getCurrentPeriodStart());
        log.info("Subscription current period end: " + subscription.getCurrentPeriodEnd());

        return ResponseEntity.ok("Subscription started");
    }

    // This method is used to get all the saved payment methods for a user
    @GetMapping("/payment-methods")
    public List<Map<String, String>> getPaymentMethods(
        @RequestParam Integer userId) throws StripeException {

        log.info("Inside getPaymentMethods method of PaymentController");
        log.info("userId: " + userId);
        String customerId = paymentService.getStripeCustomerId(userId);
        log.info("customerId: " + customerId);

        try {
            PaymentMethodListParams params = PaymentMethodListParams.builder()
                .setCustomer(customerId)
                .setType(PaymentMethodListParams.Type.CARD)
                .build();

            List<PaymentMethod> paymentMethods = PaymentMethod.list(params).getData();
            log.info("paymentMethods: " + paymentMethods);

            Customer customer = Customer.retrieve(customerId);
            String defaultPmId = customer.getInvoiceSettings().getDefaultPaymentMethod();

            return paymentMethods.stream()
                .map(pm -> Map.of(
                    "id", pm.getId(),
                    "brand", pm.getCard().getBrand(),
                    "last4", pm.getCard().getLast4(),
                    "expMonth", String.valueOf(pm.getCard().getExpMonth()),
                    "expYear", String.valueOf(pm.getCard().getExpYear()),
                    "isDefault", defaultPmId != null && defaultPmId.equals(pm.getId()) ? "true" : "false"
                ))
            .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error fetching payment methods: " + e.getMessage());
            return Collections.emptyList();
        }
    }

    // Method to set a payment method as the default payment method for a user
    @PostMapping("/set-default-payment-method")
    public ResponseEntity<String> setDefaultPaymentMethod(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside setDefaultPaymentMethod method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String paymentMethodId = payload.get("paymentMethodId");
        log.info("paymentMethodId: " + paymentMethodId);

        String customerId = paymentService.getStripeCustomerId(userId);

        try {
            Customer customer = Customer.retrieve(customerId);
            customer.update(CustomerUpdateParams.builder()
                .setInvoiceSettings(CustomerUpdateParams.InvoiceSettings.builder()
                    .setDefaultPaymentMethod(paymentMethodId)
                    .build())
                .build());

            return ResponseEntity.ok("Default payment method updated");
        } catch (Exception e) {
            return ResponseEntity.badRequest().body("Failed to update default payment method: " + e.getMessage());
        }
    }

    // Method to delete a payment method for a user
    @DeleteMapping("/delete-payment-method")
    public ResponseEntity<String> deletePaymentMethod(@RequestParam String paymentMethodId) throws StripeException {
        PaymentMethod pm = PaymentMethod.retrieve(paymentMethodId);
        pm.detach();
        return ResponseEntity.ok("Payment method detached");
    }

    // Method to get subscription info
    @GetMapping("/subscription")
    public ResponseEntity<Map<String, String>> getSubscriptionInfo(
        @RequestParam Integer userId) throws StripeException {

        log.info("Inside getSubscriptionInfo method of PaymentController");
        log.info("userId: " + userId);

        Map<String, String> subscriptionInfo = paymentService.getSubscriptionInfo(userId);

        return ResponseEntity.ok(subscriptionInfo);
    }

    // Method for generating invoice preview
    @PostMapping("/generate-invoice-preview")
    public ResponseEntity<?> generateInvoicePreview(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside generateInvoicePreview method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String customerId = payload.get("customerId");
        log.info("customerId: " + customerId);
        String subscriptionId = payload.get("subscriptionId");
        log.info("subscriptionId: " + subscriptionId);
        String priceId = payload.get("priceId");
        log.info("priceId: " + priceId);

        Subscription subscription = Subscription.retrieve(subscriptionId);
        String subscriptionItemId = subscription.getItems().getData().get(0).getId();
        log.info("subscriptionItemId: " + subscriptionItemId);

        InvoiceUpcomingParams params = InvoiceUpcomingParams.builder()
            .setCustomer(customerId)
            .setSubscription(subscriptionId)
            .setSubscriptionProrationBehavior(InvoiceUpcomingParams.SubscriptionProrationBehavior.CREATE_PRORATIONS)
            .setSubscriptionBillingCycleAnchor(InvoiceUpcomingParams.SubscriptionBillingCycleAnchor.NOW)
            .addSubscriptionItem(
                InvoiceUpcomingParams.SubscriptionItem.builder()
                    .setId(subscriptionItemId) // existing subscription item ID
                    .setPrice(priceId) // new price ID
                    .build()
            )
            .build();

        Invoice upcomingInvoice = Invoice.upcoming(params);
        List<InvoiceLineItemDto> lineItems = new ArrayList<>();

        int total = 0;
        String currency = "USD";

        for (InvoiceLineItem item : upcomingInvoice.getLines().getData()) {
            Long amount = item.getAmount();
            currency = item.getCurrency();
            total += amount;

            BigDecimal formattedAmount = BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(100));
            boolean isCredit = amount < 0;

            lineItems.add(new InvoiceLineItemDto(
                item.getDescription(),
                (isCredit ? "-" : "+") + formattedAmount.abs().toString(),
                isCredit
            ));
        }

        String totalFormatted = BigDecimal.valueOf(total).divide(BigDecimal.valueOf(100)).toString();

        InvoicePreviewResponseDto response = new InvoicePreviewResponseDto();
        response.setItems(lineItems);
        response.setTotalAmount(totalFormatted);
        response.setCurrency(currency.toUpperCase());

        return ResponseEntity.ok(response);
    }

    // Method to update subscription
    @PostMapping("/update-subscription")
    public ResponseEntity<String> updateSubscription(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside updateSubscription method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String subscriptionId = payload.get("subscriptionId");
        log.info("subscriptionId: " + subscriptionId);
        String priceId = payload.get("priceId");
        log.info("priceId: " + priceId);

        Subscription subscription = Subscription.retrieve(subscriptionId);
    String subscriptionItemId = subscription.getItems().getData().get(0).getId();

    SubscriptionUpdateParams.Builder builder = SubscriptionUpdateParams.builder()
        .setCancelAtPeriodEnd(false)
        .addItem(
            SubscriptionUpdateParams.Item.builder()
                .setId(subscriptionItemId)
                .setPrice(priceId)
                .build()
        )
        .setProrationBehavior(SubscriptionUpdateParams.ProrationBehavior.CREATE_PRORATIONS)
        .setBillingCycleAnchor(SubscriptionUpdateParams.BillingCycleAnchor.NOW)
        .putMetadata("userId", String.valueOf(userId));

        // End trial immediately if the subscription is in trialing state
        if ("trialing".equals(subscription.getStatus())) {
            builder.setTrialEnd(SubscriptionUpdateParams.TrialEnd.NOW);
        }

        Subscription updatedSubscription = subscription.update(builder.build());

        return ResponseEntity.ok("Subscription updated");
    }

    // Method to cancel subscription
    @PostMapping("/cancel-subscription")
    public ResponseEntity<String> cancelSubscription(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside cancelSubscription method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String subscriptionId = payload.get("subscriptionId");
        log.info("subscriptionId: " + subscriptionId);
        String priceId = payload.get("priceId");
        log.info("priceId: " + priceId);

        Subscription subscription = Subscription.retrieve(subscriptionId);
        subscription.update(SubscriptionUpdateParams.builder()
            .setCancelAtPeriodEnd(true)
            .putMetadata("userId", String.valueOf(userId))
            .build());
        
        return ResponseEntity.ok("Subscription canceled");
    }

    // Method to resume subscription
    @PostMapping("/resume-subscription")
    public ResponseEntity<String> resumeSubscription(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside resumeSubscription method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String subscriptionId = payload.get("subscriptionId");
        log.info("subscriptionId: " + subscriptionId);
        String priceId = payload.get("priceId");
        log.info("priceId: " + priceId);

        Subscription subscription = Subscription.retrieve(subscriptionId);
        subscription.update(SubscriptionUpdateParams.builder()
            .setCancelAtPeriodEnd(false)
            .putMetadata("userId", String.valueOf(userId))
            .build());

        return ResponseEntity.ok("Subscription resumed");
    }

    // Method to get all PennyPal Stripe products
    @GetMapping("/products")
    public ResponseEntity<List<StripeProductsDto>> getProducts() {
        List<StripeProductsDto> products = paymentService.fetchProducts();
        return ResponseEntity.ok(products);
    }

    // Method to get all invoices for a user with active subscription
    @GetMapping("/invoices")
    public ResponseEntity<List<StripeInvoiceDto>> getInvoices(
        @RequestParam Integer userId) {
        List<StripeInvoiceDto> invoices = paymentService.fetchInvoices(userId);
        return ResponseEntity.ok(invoices);
    }

    // Method to get upcoming invoice for a user
    @PostMapping("/invoice/upcoming")
    public ResponseEntity<?> getUpcomingInvoice(
        @RequestBody Map<String, String> payload) throws StripeException {

        log.info("Inside getUpcomingInvoice method of PaymentController");
        log.info("Payload: " + payload);
        Integer userId = Integer.parseInt(payload.get("userId"));
        log.info("userId: " + userId);
        String customerId = payload.get("customerId");
        log.info("customerId: " + customerId);
        String subscriptionId = payload.get("subscriptionId");
        log.info("subscriptionId: " + subscriptionId);

        try {
            InvoiceUpcomingResponseDto response = paymentService.getUpcomingInvoice(customerId, subscriptionId);

            return ResponseEntity.ok(response);

        } catch (StripeException e) {
            log.error("Error fetching upcoming invoice: " + e.getMessage(), e);
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("message", "No upcoming invoice");
            errorResponse.put("error", e.getMessage());
            return ResponseEntity.ok(errorResponse);
        }
    }
}