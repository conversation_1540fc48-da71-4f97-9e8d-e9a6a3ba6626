package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.DeletedAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.Optional;

@Repository
public interface DeletedAccountRepository extends JpaRepository<DeletedAccount, Integer> {
    
    Optional<DeletedAccount> findByOriginalUserId(Integer originalUserId);
    
    Optional<DeletedAccount> findByEmailId(String emailId);
    
    boolean existsByEmailId(String emailId);
    
    boolean existsByOriginalUserId(Integer originalUserId);
}
