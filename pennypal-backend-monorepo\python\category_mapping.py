from dotenv import load_dotenv
import faiss
from flask import jsonify
import mysql.connector
import numpy as np
import os
import pickle
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import normalize

# Load environment variables from .env
load_dotenv()

# DB connection config
connection = mysql.connector.connect(
    host=os.getenv("MYSQL_HOST"),
    port=os.getenv("MYSQL_PORT"),
    user=os.getenv("MYSQL_USER"),
    password=os.getenv("MYSQL_PASSWORD"),
    database=os.getenv("MYSQL_DATABASE")
)

# Step 1: Connect to MySQL and fetch data
def fetch_data():
    cursor = connection.cursor(dictionary=True)
    cursor.execute("""
        SELECT legacy_category, legacy_category_parent, primary_pfc_clean, detailed_pfc_clean
        FROM category_mapping
        WHERE legacy_category IS NOT NULL
    """)
    rows = cursor.fetchall()
    cursor.close()
    connection.close()
    return rows

# Step 2: Embed and index
def build_and_store_index(rows):
    model = SentenceTransformer('paraphrase-MiniLM-L12-v2')

    embeddings = []
    metadata = []

    for row in rows:
        emb = model.encode(row["legacy_category"])
        embeddings.append(emb)
        metadata.append({
            "legacy_subcategory": row["legacy_category"],
            "legacy_category": row["legacy_category_parent"],
            "primary_pfc": row["primary_pfc_clean"],
            "detailed_pfc": row["detailed_pfc_clean"]
        })

    embedding_matrix = normalize(np.array(embeddings).astype('float32'), axis=1)
    dimension = embedding_matrix.shape[1]
    index = faiss.IndexFlatIP(dimension)
    index.add(embedding_matrix)

    # Save to disk
    faiss.write_index(index, "category_mapping.index")
    with open("category_mapping_metadata.pkl", "wb") as f:
        pickle.dump(metadata, f)
    print("Index and metadata stored successfully.")

def store_category_mapping():
    rows = fetch_data()
    build_and_store_index(rows)
    return jsonify({"message": "Category mappings stored successfully."})

def search_category_mapping(queries):
    try:
        if not queries or not isinstance(queries, list):
            return jsonify({"error": "Invalid queries format"}), 400
        
        index = faiss.read_index("category_mapping.index")
        with open("category_mapping_metadata.pkl", "rb") as f:
            metadata = pickle.load(f)

        model = SentenceTransformer('paraphrase-MiniLM-L12-v2')

        query_emb = model.encode(queries, show_progress_bar=False)
        query_emb = normalize(query_emb.astype('float32'), axis=1)
        scores, indices = index.search(query_emb, 1)

        results_map = {}
        for q_idx, query in enumerate(queries):
            idx = indices[q_idx][0]
            if idx < len(metadata):
                results_map[query] = metadata[idx]["detailed_pfc"]
            else:
                results_map[query] = "Uncategorized"

        return jsonify(results_map)
    except FileNotFoundError:
        return jsonify({"error": "Index files not found. Run store_category_mapping first."}), 500
    except Exception as e:
        return jsonify({"error": str(e)}), 500