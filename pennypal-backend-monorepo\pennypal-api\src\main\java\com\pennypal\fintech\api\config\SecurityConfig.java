package com.pennypal.fintech.api.config;



import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;

import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;



@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Autowired
    private JWTAuthenticationFilter jwtFilter;

    @Autowired
    private UserDetailsService userDetailsService;

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
   

    @Bean
    public AuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider(){
            @Override
            protected void additionalAuthenticationChecks(UserDetails userDetails, 
                    UsernamePasswordAuthenticationToken authentication) throws AuthenticationException {
                if (authentication.getCredentials() == null) {
                    logger.debug("Failed to authenticate since no credentials provided");
                    throw new BadCredentialsException("Bad credentials");
                }
                
                String presentedPassword = authentication.getCredentials().toString();
                if (!passwordEncoder().matches(presentedPassword, userDetails.getPassword())) {
                    logger.debug("Failed to authenticate since password does not match stored value");
                    throw new BadCredentialsException("Bad credentials");
                }
            }
        };
        provider.setPasswordEncoder(new BCryptPasswordEncoder(12));
        provider.setUserDetailsService(userDetailsService);


        return provider;
    }

    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration config) throws Exception {
        return config.getAuthenticationManager();

    }

    
    AuthenticationEntryPoint sss = new  AuthenticationEntryPoint() {   
    
        @Override
        public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException authException) 
                throws IOException {
            System.err.println("Unauthorized error: "+ authException.getMessage());
            response.setContentType("application/json");
            response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
            response.getOutputStream().println("{ \"error\": \"" + authException.getMessage() + "\" }");
        }
    };

    /* 
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .authenticationProvider(authenticationProvider())
            .csrf(csrf -> csrf.disable())
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .authorizeHttpRequests(auth -> auth
                    .requestMatchers("/auth/**").permitAll()
                    .anyRequest().authenticated()
            )
            .addFilterBefore(new JWTAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class)
            .build();
    } */

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {

        return http.csrf(customizer -> customizer.disable()).
                authorizeHttpRequests(request -> request
                        .requestMatchers("/api/v1/auth/signin",
                        "/api/v1/auth/register",
                        "/api/v1/auth/verify",
                        "/api/v1/auth/refresh-token",
                        "/api/v1/auth/logout",
                        "/api/v1/auth/signin/google",
                        "/api/v1/auth/register/google",
                        "/api/v1/auth/signin/apple",
                        "/api/v1/auth/register/apple",
                        "/api/v1/auth/signin/otp/generate",
                        "/api/v1/auth/signin/otp/verify",
                        "/api/v1/auth/generate-secret-key",
                        "/api/v1/auth/forgot-password/generate-otp",
                        "/api/v1/auth/forgot-password/verify-otp",
                        "/api/v1/auth/forgot-password/reset",
                        "/api/v1/stripe-webhooks/**").permitAll()
                        // To allow Swagger UI access
                        .requestMatchers("/error").permitAll()  // This is critical!
                        .requestMatchers("/swagger-ui/**").permitAll()
                        .requestMatchers("/v3/api-docs/**").permitAll()
                        .requestMatchers("/swagger-ui.html").permitAll()
                        .requestMatchers("/swagger-resources/**").permitAll()
                        .requestMatchers("/webjars/**").permitAll()
                        .requestMatchers("/uploads/**").permitAll()
                        .requestMatchers("/uploads/scanned-copy/**").permitAll()
                        // .requestMatchers("/api/v1/transaction/transactions/summary/user/**").permitAll()

                        .anyRequest().authenticated()).
                httpBasic(Customizer.withDefaults()).
                sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
                .exceptionHandling(ex -> ex.authenticationEntryPoint(sss))
                .build();


    }


}
