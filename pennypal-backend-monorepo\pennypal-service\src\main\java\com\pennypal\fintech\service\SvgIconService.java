package com.pennypal.fintech.service;


import com.pennypal.fintech.entity.SvgIcon;
import com.pennypal.fintech.repository.SvgIconRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

@Service
public class SvgIconService {

    private final SvgIconRepository svgIconRepository;

    @Autowired
    public SvgIconService(SvgIconRepository svgIconRepository) {
        this.svgIconRepository = svgIconRepository;
    }

    @Cacheable(value = "svgIconCache", key = "#root.methodName")
    public List<SvgIcon> getAllIcons() {
        return svgIconRepository.findAll();
    }

    @Cacheable(value = "svgIconCache", key = "#root.methodName" + "_" + "#id")
    public Optional<SvgIcon> getIconById(Integer id) {
        return svgIconRepository.findById(id);
    }

    @Cacheable(value = "svgIconCache", key = "#root.methodName" + "_" + "#iconName")
    public Optional<SvgIcon> getIconByName(String iconName) {
        return svgIconRepository.findByIconName(iconName);
    }

    @CacheEvict(value = "svgIconCache", allEntries = true)
 public SvgIcon saveIconFromUrl(String urlString, String iconName, String description) throws IOException {
        // Validate that the URL ends with .svg
        if (!urlString.toLowerCase().endsWith(".svg")) {
            throw new IllegalArgumentException("URL must point to an SVG file");
        }
        
        // Check if icon name already exists
        if (svgIconRepository.findByIconName(iconName).isPresent()) {
            throw new IllegalArgumentException("Icon with name '" + iconName + "' already exists");
        }
        
        // Download SVG content from URL
        URL url = new URL(urlString);
        byte[] svgContent;
        
        try (InputStream in = url.openStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            
            svgContent = out.toByteArray();
            
            // Very basic validation that it's an SVG
            String contentStart = new String(svgContent, 0, Math.min(svgContent.length, 100)).toLowerCase();
            if (!contentStart.contains("<svg") && !contentStart.contains("<?xml")) {
                throw new IllegalArgumentException("URL does not contain valid SVG content");
            }
        }
        
        // Create and save the new SVG icon
        SvgIcon icon = new SvgIcon();
        icon.setIconName(iconName);
        icon.setDescription(description);
        icon.setSvgContent(svgContent);
        
        return svgIconRepository.save(icon);
    }
    
    @CacheEvict(value = "svgIconCache", allEntries = true)
    public SvgIcon saveIconFromContent(String svgContent, String iconName, String description) throws IOException {
        // Check if icon name already exists
        if (svgIconRepository.findByIconName(iconName).isPresent()) {
            throw new IllegalArgumentException("Icon with name '" + iconName + "' already exists");
        }
        
        // Validate basic SVG structure
        String contentLower = svgContent.toLowerCase();
        if (!contentLower.contains("<svg") && !contentLower.contains("<?xml")) {
            throw new IllegalArgumentException("Content does not appear to be valid SVG");
        }
        
        // Create and save the new SVG icon
        SvgIcon icon = new SvgIcon();
        icon.setIconName(iconName);
        icon.setDescription(description);
        icon.setSvgContent(svgContent.getBytes(StandardCharsets.UTF_8));
        
        return svgIconRepository.save(icon);
    }
    
  

}