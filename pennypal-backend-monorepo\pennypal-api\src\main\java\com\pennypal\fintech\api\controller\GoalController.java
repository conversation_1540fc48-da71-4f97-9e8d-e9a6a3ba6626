package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.GoalDto;
import com.pennypal.fintech.service.GoalService;

import jakarta.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.exception.InvalidRequestException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@RestController
@RequestMapping("/api/goals")
public class GoalController {
    private static final Logger logger = LoggerFactory.getLogger(GoalController.class);

    private final GoalService goalService;

    @Autowired
    public GoalController(GoalService goalService) {
        this.goalService = goalService;
    }

    @PostMapping
    public ResponseEntity<GoalDto.GoalResponse> createGoal(
            @RequestHeader("User-Id") Integer userId,
            @RequestBody GoalDto.CreateGoalRequest request) {
        GoalDto.GoalResponse response = goalService.createGoal(userId, request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @GetMapping("/{goalId}")
    public ResponseEntity<GoalDto.GoalResponse> getGoal(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer goalId) {
        GoalDto.GoalResponse response = goalService.getGoal(userId, goalId);
        return ResponseEntity.ok(response);
    }

    @GetMapping
    public ResponseEntity<List<GoalDto.GoalResponse>> getAllGoals(
            @RequestHeader("User-Id") Integer userId) {
        List<GoalDto.GoalResponse> responses = goalService.getAllGoals(userId);
        return ResponseEntity.ok(responses);
    }

    @PutMapping("/update/{goalId}")
    public ResponseEntity<GoalDto.GoalResponse> updateGoal(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer goalId,
            @RequestBody GoalDto.UpdateGoalRequest request) {
        try {
            GoalDto.GoalResponse response = goalService.updateGoal(userId, goalId, request);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException ex) {
            throw ex; // Let the global exception handler manage this
        } catch (InvalidRequestException ex) {
            throw ex; // Let the global exception handler manage this
        } catch (Exception ex) {
            // Log the exception
            logger.error("Error updating goal: " + ex.getMessage(), ex);
            throw new RuntimeException("Error updating goal. Please try again later.", ex);
        }
    }
    

    @PostMapping("/contribute")
    public ResponseEntity<GoalDto.GoalResponse> contributeToGoal(
            @RequestHeader("User-Id") Integer userId,
            @Valid @RequestBody GoalDto.ContributeToGoalRequest request) {
        try {
            // Add logging for debugging
            logger.info("Received contribution request - UserId: {}, GoalId: {}, Amount: {}, AccountId: {}", 
                       userId, request.getGoalId(), request.getAmount(), request.getAccountId());
            
            GoalDto.GoalResponse response = goalService.contributeToGoal(userId, request);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException | InvalidRequestException ex) {
            logger.error("Validation error in contribute: " + ex.getMessage(), ex);
            throw ex;
        } catch (Exception ex) {
            logger.error("Error contributing to goal: " + ex.getMessage(), ex);
            throw new RuntimeException("Error processing contribution. Please try again later.", ex);
        }
    } 
    
    @GetMapping("/summary")
    public ResponseEntity<GoalDto.GoalSummaryResponse> getGoalsSummary(
            @RequestHeader("User-Id") Integer userId) {
        GoalDto.GoalSummaryResponse response = goalService.getGoalsSummary(userId);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/month/{year}/{month}")
    public ResponseEntity<List<GoalDto.GoalResponse>> getGoalsForMonth(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable int year,
            @PathVariable int month) {
        List<GoalDto.GoalResponse> responses = goalService.getGoalsForMonth(userId, year, month);
        return ResponseEntity.ok(responses);
    }


    @PostMapping("/next/{previousGoalId}")
    public ResponseEntity<GoalDto.GoalResponse> createNextGoal(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer previousGoalId,
            @RequestBody GoalDto.CreateGoalRequest request) {
        GoalDto.GoalResponse response = goalService.createNextGoal(userId, previousGoalId, request);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }
}