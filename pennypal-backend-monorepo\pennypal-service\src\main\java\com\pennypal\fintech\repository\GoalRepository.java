package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Goal;
import com.pennypal.fintech.entity.Users;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface GoalRepository extends JpaRepository<Goal, Integer> {
    
    List<Goal> findByUser(Users user);
    
    List<Goal> findByUserAndStatus(Users user, Goal.GoalStatus status);
    
    @Query("SELECT g FROM Goal g WHERE g.user = ?1 AND g.targetDate BETWEEN ?2 AND ?3")
    List<Goal> findGoalsWithTargetDateInMonth(Users user, LocalDate startOfMonth, LocalDate endOfMonth);
    
    @Query("SELECT g FROM Goal g WHERE g.user = ?1 AND g.targetDate <= ?2 AND g.status = ?3")
    List<Goal> findOverdueGoals(Users user, LocalDate today, Goal.GoalStatus status);
    
    @Query("SELECT COUNT(g) FROM Goal g WHERE g.user = ?1 AND g.status = ?2")
    Integer countByUserAndStatus(Users user, Goal.GoalStatus status);
    
    @Query("SELECT SUM(g.currentAmount) FROM Goal g WHERE g.user = ?1 AND g.goalType = ?2")
    Double sumCurrentAmountByUserAndGoalType(Users user, Goal.GoalType goalType);
}