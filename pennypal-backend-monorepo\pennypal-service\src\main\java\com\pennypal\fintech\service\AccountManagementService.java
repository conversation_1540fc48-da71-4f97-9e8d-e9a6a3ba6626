package com.pennypal.fintech.service;

import com.pennypal.fintech.dto.ChangePasswordDto;
import com.pennypal.fintech.dto.DeleteAccountDto;
import com.pennypal.fintech.entity.DeletedAccount;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.DeletedAccountRepository;
import com.pennypal.fintech.repository.TwoFactorAuthRepository;
import com.pennypal.fintech.repository.UserRatingRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.repository.UserRatingRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class AccountManagementService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private TwoFactorAuthRepository twoFactorAuthRepository;
    
    @Autowired
    private DeletedAccountRepository deletedAccountRepository;
    @Autowired
    private UserRatingRepository userRatingRepository;
    @Autowired
    private TwoFactorAuthService twoFactorAuthService;
    
    private BCryptPasswordEncoder encoder = new BCryptPasswordEncoder(12);
    
    @Transactional
    public boolean changePassword(ChangePasswordDto changePasswordDto) {
        log.info("Changing password for user: {}", changePasswordDto.getUserId());
        
        // Validate input
        if (!changePasswordDto.getNewPassword().equals(changePasswordDto.getConfirmPassword())) {
            throw new RuntimeException("New password and confirm password do not match");
        }
        
        Users user = userRepository.findById(changePasswordDto.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Verify current password
        if (!encoder.matches(changePasswordDto.getOldPassword(), user.getPassword())) {
            throw new RuntimeException("Current password is incorrect");
        }
        
        // Verify 2FA if enabled
        if (twoFactorAuthService.isTwoFactorEnabled(changePasswordDto.getUserId())) {
            if (changePasswordDto.getTwoFactorCode() == null || 
                !twoFactorAuthService.verifyTwoFactorCode(changePasswordDto.getUserId(), 
                                                        changePasswordDto.getTwoFactorCode())) {
                throw new RuntimeException("Invalid two-factor authentication code");
            }
        }
        
        // Update password
        user.setPassword(encoder.encode(changePasswordDto.getNewPassword()));
        userRepository.save(user);
        
        log.info("Password changed successfully for user: {}", changePasswordDto.getUserId());
        return true;
    }
    
    @Transactional
    public boolean deleteAccountPermanently(DeleteAccountDto deleteAccountDto) {
        log.info("Permanently deleting account for user: {}", deleteAccountDto.getUserId());
        
        Users user = userRepository.findById(deleteAccountDto.getUserId())
                .orElseThrow(() -> new RuntimeException("User not found"));
        
        // Verify password
        if (!encoder.matches(deleteAccountDto.getPassword(), user.getPassword())) {
            throw new RuntimeException("Password is incorrect");
        }
        
        // Verify 2FA if enabled
        if (twoFactorAuthService.isTwoFactorEnabled(deleteAccountDto.getUserId())) {
            if (deleteAccountDto.getTwoFactorCode() == null || 
                !twoFactorAuthService.verifyTwoFactorCode(deleteAccountDto.getUserId(), 
                                                        deleteAccountDto.getTwoFactorCode())) {
                throw new RuntimeException("Invalid two-factor authentication code");
            }
        }
        
        // Confirm deletion
        if (deleteAccountDto.getConfirmDeletion() == null || !deleteAccountDto.getConfirmDeletion()) {
            throw new RuntimeException("Account deletion must be confirmed");
        }
        
        try {
            // Store deletion record
            DeletedAccount deletedAccount = new DeletedAccount();
            deletedAccount.setOriginalUserId(user.getId());
            deletedAccount.setEmailId(user.getEmailId());
            deletedAccount.setPhoneNumber(user.getPhoneNumber());
            deletedAccount.setName(user.getName());
            deletedAccount.setDeletionReason(deleteAccountDto.getReason());
            deletedAccountRepository.save(deletedAccount);

                if (userRatingRepository.existsByUserId(user.getId())) {
                userRatingRepository.deleteByUserId(user.getId());
                log.info("User rating records deleted for user: {}", user.getId());
            }
            
            // Delete 2FA records
            twoFactorAuthRepository.deleteByUserId(user.getId());
            
            // Delete user account
            userRepository.delete(user);
            
            log.info("Account permanently deleted for user: {}", deleteAccountDto.getUserId());
            return true;
            
        } catch (Exception e) {
            log.error("Error deleting account for user: {}", deleteAccountDto.getUserId(), e);
            throw new RuntimeException("Failed to delete account: " + e.getMessage());
        }
    }
    
    public boolean isAccountDeleted(String emailId) {
        return deletedAccountRepository.existsByEmailId(emailId);
    }
    
    public boolean isAccountDeleted(Integer userId) {
        return deletedAccountRepository.existsByOriginalUserId(userId);
    }
}
