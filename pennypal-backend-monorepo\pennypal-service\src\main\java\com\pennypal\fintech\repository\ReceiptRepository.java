package com.pennypal.fintech.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.pennypal.fintech.entity.Receipts;

@Repository
public interface ReceiptRepository extends JpaRepository<Receipts, Integer> {
    boolean existsByTransactionId(int transactionId);

    Optional<Receipts> findByTransactionId(int transactionId);
    @Query("SELECT t.id FROM Receipts r JOIN r.transaction t") // Changed from 'Receipt' to 'Receipts'
    List<Integer> findAllTransactionIds();
     List<Receipts> findByUserId(Long userId);

    // Custom queries can be added here if necessary
}
