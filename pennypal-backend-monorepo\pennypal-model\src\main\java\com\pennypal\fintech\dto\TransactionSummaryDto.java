package com.pennypal.fintech.dto;

import java.math.BigDecimal;
import java.util.Date;

public class TransactionSummaryDto {
     private Long id;
    private Long userId;
    private Integer totalTransaction;
    private BigDecimal totalCredit;
    private BigDecimal totalDebit;
    private BigDecimal totalAmount;
    private BigDecimal largestTransaction;
    private Integer disputeTransaction;
    private Date firstTransactionDate;
    private Date latestTransactionDate;

    
}
