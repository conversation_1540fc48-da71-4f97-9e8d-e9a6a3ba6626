package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.ChatbotService;

import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

@RestController
@RequestMapping("/api/v1/chatbot")
@RequiredArgsConstructor
public class ChatbotController {

    private final ChatbotService chatbotService;
    
    private final RestTemplate restTemplate;

    @PostMapping("/query")
    public ResponseEntity<?> queryAI(@RequestBody Map<String, Object> request) {
        System.out.println("Querying AI with request: " + request);
        System.out.println("User ID: " + request.get("user_id"));
        System.out.println("User Query: " + request.get("user_query"));
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // Get previous queries of the user
        List<String> previousQueries = chatbotService.getPreviousQueries(Integer.parseInt((String) request.get("user_id")));

        Map<String, Object> body = new HashMap<>();
        body.put("user_query", request.get("user_query"));
        body.put("previous_queries", previousQueries);
        body.put("user_id", request.get("user_id"));

        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(body, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(
            "http://localhost:5000/generate-sql", entity, String.class
        );

        // Add to context
        chatbotService.addQuery(Integer.parseInt((String) request.get("user_id")), (String) request.get("user_query"));

        // Add to history
        Map<String, Object> modifiedResponse = chatbotService.addHistory(request, response);

        return ResponseEntity.ok(modifiedResponse);
    }

    @GetMapping("/history/{userId}")
    public ResponseEntity<?> getHistory(@PathVariable Integer userId) {
        System.out.println("Getting history for user: " + userId);
        System.out.println("User ID: " + userId);
        return ResponseEntity.ok(chatbotService.getHistory(userId));
    }
}