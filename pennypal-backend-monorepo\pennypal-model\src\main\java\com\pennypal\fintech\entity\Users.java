package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ForeignKey;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;

import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonManagedReference;

import lombok.Data;


@Data
@Entity
@Table(name = "users")
public class Users {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "email_id")
    private String emailId;

    @Column(name = "password")
    private String password;

    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "source_of_auth", foreignKey = @ForeignKey(name = "user_ibfk_1"))
    private EnumAuthProvider authProvider;
    
    @Column(name = "insert_datetime")
    private LocalDateTime insertDateTime;
    
    @Column(name = "update_datetime")
    private LocalDateTime updateDateTime;

    @Column(name = "name", nullable = true)
    private String name;
 @Column(name = "is_primary", nullable = false, columnDefinition = "BOOLEAN DEFAULT TRUE")
    private Boolean isPrimary = true;
  	@OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Accounts> accounts;
	@OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
@JsonManagedReference
private List<Investment> investments;
private String address;
    private String bio;
  // Profile picture fields
    @Column(name = "profile_picture_url")
    private String profilePictureUrl;
    
    @Lob
    @Column(name = "profile_picture_data", columnDefinition = "LONGBLOB")
    private byte[] profilePictureData;
    
    @Column(name = "profile_picture_content_type")
    private String profilePictureContentType;
    
    @Column(name = "profile_picture_filename")
    private String profilePictureFilename;
    
    @Column(name = "profile_picture_size")
    private Long profilePictureSize;
    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBio() {
        return bio;
    }

    public void setBio(String bio) {
        this.bio = bio;
    }
    // One-to-Many relationship with Transaction
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL)
    private List<Transactions> transactions;

    private LocalDateTime lastRefreshTime;


    @Override
    public String toString() {
        return "User{" +    

                "id=" + id +
                "name =" + name +
                ", emailId='" + emailId + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", authProviderId=" + (authProvider != null ? authProvider.getId() : null) +
                ", insertDateTime=" + insertDateTime +
                ", updateDateTime=" + updateDateTime +
                '}';
    }
}
