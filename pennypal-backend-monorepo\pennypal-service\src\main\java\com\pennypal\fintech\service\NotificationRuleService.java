package com.pennypal.fintech.service;

import com.pennypal.fintech.entity.Notification;
import com.pennypal.fintech.entity.NotificationRules;
import com.pennypal.fintech.entity.NotificationTracking;
import com.pennypal.fintech.entity.Notification.NotificationState;

import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Budget;
import com.pennypal.fintech.entity.Category;
import com.pennypal.fintech.entity.UserNotificationRules;

import com.pennypal.fintech.repository.*;
import com.pennypal.fintech.dto.FutureRecurringTransactionDto;
import com.pennypal.fintech.dto.TransactionDto;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NotificationRuleService {

    private final NotificationRepository notificationRepository;
    private final NotificationRuleRepository notificationRuleRepository;
    private final NotificationTrackingRepository notificationTrackingRepository;
    private final BudgetRepository budgetRepository;
    private final UserRepository userRepository;
    private final AccountRepository accounts;
    private final RecurringTransactionService recurringTransactionService;
    private final UserNotificationRulesRepository userNotificationRulesRepository;
    private final AccountRepository accountRepository;
    
    public NotificationRuleService(NotificationRepository notificationRepository,
                                   NotificationRuleRepository notificationRuleRepository,
                                   NotificationTrackingRepository notificationTrackingRepository,
                                   BudgetRepository budgetRepository,
                                   UserRepository userRepository,
                                   AccountRepository accounts,
                                   RecurringTransactionService recurringTransactionService,
                                   UserNotificationRulesRepository userNotificationRulesRepository,
                                   AccountRepository accountRepository) {
        this.notificationRepository = notificationRepository;
        this.notificationRuleRepository = notificationRuleRepository;
        this.notificationTrackingRepository = notificationTrackingRepository;
        this.budgetRepository = budgetRepository;
        this.userRepository = userRepository;
        this.accounts = accounts;
        this.recurringTransactionService = recurringTransactionService;
        this.userNotificationRulesRepository = userNotificationRulesRepository;
        this.accountRepository = accountRepository;
    }

    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #userId",
                allEntries = true)
    // This will be checked daily - to create notifications for upcoming recurring transactions
    public void checkRecurringTransactionRules(Integer userId) {
        log.info("Checking recurring transaction rules for user: {}", userId);
        // Check if user exists
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // Fetch future recurring transactions
        List<FutureRecurringTransactionDto> futureTxns = 
            recurringTransactionService.fetchFutureRecurringTransactions(userId);

        // If no future recurring transactions, return
        if (futureTxns == null || futureTxns.isEmpty()) {
            return;
        }
        log.info("Future recurring transactions: {}", futureTxns);
        
        // Fetch the notification rule for RECURRING_PAYMENT_DUE
        Integer daysThreshold;
        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(userId, "RECURRING_PAYMENT_DUE")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("RECURRING_PAYMENT_DUE");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return; // Skip processing if rule is inactive
            }
            log.info("Fetched master notification rule: {}", rule1);
            daysThreshold = rule1.getDaysThreshold();
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(userId, "RECURRING_PAYMENT_DUE");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return; // Skip processing if rule is not enabled
            }
            log.info("Fetched user notification rule: {}", rule2);
            daysThreshold = rule2.getDaysThreshold();
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }
        log.info("Days threshold: {}", daysThreshold);
        log.info("Message template: {}", messageTemplate);
        log.info("Rule type: {}", ruleType);
        log.info("Severity: {}", severity);

        // Filter future transactions based on rule's days threshold + buffer
        LocalDate now = LocalDate.now();
        LocalDate thresholdBufferDaysFromNow = now.plusDays(daysThreshold + 3);
        log.info("Threshold buffer days from now: {}", thresholdBufferDaysFromNow);
        futureTxns = futureTxns.stream()
            .filter(txn -> !txn.getNextDate().isBefore(now) && !txn.getNextDate().isAfter(thresholdBufferDaysFromNow))
            .collect(Collectors.toList());
        log.info("Filtered future transactions: {}", futureTxns);
        
        // Check each future transaction if it's within the rule's days threshold
        for (FutureRecurringTransactionDto txn : futureTxns) {
            long daysUntilDue = ChronoUnit.DAYS.between(LocalDate.now(), txn.getNextDate());
            
            if (daysUntilDue <= daysThreshold) {
                log.info("User has to be notified for upcoming recurring transaction: {}", txn);
                String message = messageTemplate
                    .replace("{description}", txn.getDescription())
                    .replace("{transactionAmount}", txn.getTransactionAmount().toString())
                    .replace("{nextDate}", txn.getNextDate().toString());

                
                // If notification not already sent, create and save it
                if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(userId, ruleType, message)) {
                    log.info("Creating notification for user: {} with message: {}", userId, message);
                    createNotification(userId, message, severity, ruleType);
                } else {
                    log.info("Notification already sent for user: {} with message: {}", userId, message);
                } 
            }
        }
    }

    @CacheEvict(value = "notificationCache",
               key = "'getUserNotifications' + '_' + #userId",
               allEntries = true)
    public void checkBudgetRules(Integer userId) {
        log.info("Checking budget rules for user: {}", userId);
        // Check if user exists
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }

        // Fetch current month's budgets
        List<Budget> budgets = budgetRepository.getCurrMonthBudgets(userId);
        
        // If no budgets, return
        if (budgets == null || budgets.isEmpty()) {
            log.info("No budgets found for user: {}. Returning...", userId);
            return;
        }
        log.info("Budgets: {}", budgets);

        // Fetch the notification rule for BUDGET_ALERT
        BigDecimal percentageThreshold;
        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(userId, "BUDGET_ALERT")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("BUDGET_ALERT");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return; // Skip processing if rule is inactive
            }
            log.info("Fetched master notification rule: {}", rule1);
            percentageThreshold = rule1.getPercentageThreshold();
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(userId, "BUDGET_ALERT");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return; // Skip processing if rule is not enabled
            }
            log.info("Fetched user notification rule: {}", rule2);
            percentageThreshold = rule2.getPercentageThreshold();
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }

        String currentMonth = LocalDate.now().getMonth().name();
        currentMonth = currentMonth.substring(0, 1).toUpperCase() + currentMonth.substring(1).toLowerCase();
        
        // Check each budget if it exceeds the rule's percentage threshold
        for (Budget budget : budgets) {
            BigDecimal usagePercentage = budget.getActual()
                .divide(budget.getAllocated(), 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));

            if (usagePercentage.compareTo(percentageThreshold) >= 0) {
                log.info("Budget {} exceeds threshold - creating notification", budget);
                Category category = budget.getCategory();
                
                String categoryName = category.getCategory();
                
                String message = messageTemplate
                    .replace("{allocated}", budget.getAllocated().toString())
                    .replace("{category}", categoryName)
                    .replace("{month}", currentMonth);
                
                // If notification not already sent, create and save it
                if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(userId, ruleType, message)) {
                    log.info("Creating notification for user: {} with message: {}", userId, message);
                    createNotification(userId, message, severity, ruleType);
                } else {
                    log.info("Notification already sent for user: {} with message: {}", userId, message);
                }
            }
        }
    }

    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #transaction.userId",
                allEntries = true)
    // This will be checked when new transactions come via event payload
    public void checkLargeTransaction(TransactionDto transaction) {
        log.info("Checking large transaction for user: {}", transaction.getUserId());
        // Fetch the notification rule for LARGE_TRANSACTION
        Double amountThreshold;
        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(transaction.getUserId(), "LARGE_TRANSACTION_ALERT")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("LARGE_TRANSACTION_ALERT");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched master notification rule: {}", rule1);
            amountThreshold = rule1.getAmountThreshold();
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(transaction.getUserId(), "LARGE_TRANSACTION_ALERT");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched user notification rule: {}", rule2);
            amountThreshold = rule2.getAmountThreshold();
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }
        log.info("Amount threshold: {}", amountThreshold);
        log.info("Message template: {}", messageTemplate);
        log.info("Rule type: {}", ruleType);
        log.info("Severity: {}", severity);

        // Check if transaction amount exceeds the rule's amount threshold
        if (transaction.getTransactionAmount() > amountThreshold) {
            log.info("Transaction amount {} exceeds the rule's amount threshold {}", transaction, amountThreshold);
            String accountMask = accounts.getById(transaction.getAccountId()).getAccountMask();
            String message = messageTemplate
                .replace("{amount}", String.valueOf(transaction.getTransactionAmount()))
                .replace("{merchant}", transaction.getDescription())
                .replace("{date}", transaction.getTransactionDate().toLocalDate().toString())
                .replace("{mask}", accountMask);

            // If notification not already sent, create and save it
            if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(transaction.getUserId(), ruleType, message)) {
                log.info("Creating notification for user: {} with message: {}", transaction.getUserId(), message);
                createNotification(transaction.getUserId(), message, severity, ruleType);
            } else {
                log.info("Notification already sent for user: {} with message: {}", transaction.getUserId(), message);
            }
        }
    }

    // Generate notifications for out of sync
    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #userId",
                allEntries = true)
    // PENDING - CRON SCHEDULE
    public void checkOutOfSync(Integer userId) {
        log.info("Checking out of sync for user: {}", userId);
        // Fetch the notification rule for OUT_OF_SYNC

        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(userId, "OUT_OF_SYNC_ALERT")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("OUT_OF_SYNC_ALERT");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched master notification rule: {}", rule1);
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(userId, "OUT_OF_SYNC_ALERT");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched user notification rule: {}", rule2);
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }
        log.info("Message template: {}", messageTemplate);
        log.info("Rule type: {}", ruleType);
        log.info("Severity: {}", severity);
        
        // Check if any accounts are out of sync
        List<Accounts> userAccounts = accountRepository.findByUserId(userId);
        log.info("User accounts: {}", userAccounts);

        // Out of sync if last_sync_time more than 24hrs from current time
        for (Accounts account : userAccounts) {
            if (account.getLastSyncTime() == null || account.getLastSyncTime().plusHours(24).isBefore(LocalDateTime.now())) {
                log.info("Account: {} is out of sync", account);
                String accountName = account.getAccountName() + " - ****" + account.getAccountMask();
                String message = messageTemplate
                    .replace("{accountName}", accountName);

                // If notification not already sent, create and save it
                if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(userId, ruleType, message)) {
                    log.info("Creating notification for user: {} with message: {}", userId);
                    createNotification(userId, message, severity, ruleType);
                } else {
                    log.info("Notification already sent for user: {} with message: {}", userId, message);
                }
            }
        }
    }

    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #userId",
                allEntries = true)
    public void createNotification(Integer userId, String message, NotificationRules.NotificationSeverity severity, String ruleType) {
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setDescription(message);
        notification.setSeverity(Notification.NotificationSeverity.valueOf(severity.name().toUpperCase()));
        notification.setState(NotificationState.unread);
        notification.setCreateTimestamp(LocalDateTime.now());
        
        notification = notificationRepository.save(notification);

        // Save notification tracking record
        NotificationTracking tracking = new NotificationTracking();
        tracking.setUserId(userId);
        tracking.setNotificationId(notification.getNotificationId());
        tracking.setRuleType(ruleType);
        tracking.setMessage(message);
        tracking.setCreatedTimestamp(notification.getCreateTimestamp());
        
        notificationTrackingRepository.save(tracking);
    }
}