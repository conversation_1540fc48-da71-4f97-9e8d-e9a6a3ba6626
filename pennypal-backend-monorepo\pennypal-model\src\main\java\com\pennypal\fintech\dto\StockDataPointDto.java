package com.pennypal.fintech.dto;

import java.time.LocalDate;

public class StockDataPointDto {
    private LocalDate date;
    private Double avgPrice;
    private Double totalQuantity;
    private Double totalValue;
    private Double avgCostBasis;
    private Double totalCostBasis;
    private Double gainLoss;
    private Double gainLossPercent;
    private String status;

    public StockDataPointDto() {}

    public StockDataPointDto(LocalDate date, Double avgPrice, Double totalQuantity, 
                             Double avgCostBasis, Double totalCostBasis) {
        this.date = date;
        this.avgPrice = avgPrice;
        this.totalQuantity = totalQuantity;
        this.avgCostBasis = avgCostBasis;
        this.totalCostBasis = totalCostBasis;
        this.totalValue = (avgPrice != null && totalQuantity != null) ? avgPrice * totalQuantity : 0.0;
        calculateGainLoss();
    }

    private void calculateGainLoss() {
        if (totalValue != null && totalCostBasis != null && totalCostBasis > 0) {
            this.gainLoss = totalValue - totalCostBasis;
            this.gainLossPercent = (gainLoss / totalCostBasis) * 100;
            this.status = gainLoss >= 0 ? "GAIN" : "LOSS";
        } else {
            this.gainLoss = 0.0;
            this.gainLossPercent = 0.0;
            this.status = "NEUTRAL";
        }
    }

    public LocalDate getDate() { return date; }
    public void setDate(LocalDate date) { this.date = date; }

    public Double getAvgPrice() { return avgPrice; }
    public void setAvgPrice(Double avgPrice) { this.avgPrice = avgPrice; }

    public Double getTotalQuantity() { return totalQuantity; }
    public void setTotalQuantity(Double totalQuantity) { this.totalQuantity = totalQuantity; }

    public Double getTotalValue() { return totalValue; }
    public void setTotalValue(Double totalValue) { this.totalValue = totalValue; }

    public Double getAvgCostBasis() { return avgCostBasis; }
    public void setAvgCostBasis(Double avgCostBasis) { this.avgCostBasis = avgCostBasis; }

    public Double getTotalCostBasis() { return totalCostBasis; }
    public void setTotalCostBasis(Double totalCostBasis) { this.totalCostBasis = totalCostBasis; }

    public Double getGainLoss() { return gainLoss; }
    public void setGainLoss(Double gainLoss) { this.gainLoss = gainLoss; }

    public Double getGainLossPercent() { return gainLossPercent; }
    public void setGainLossPercent(Double gainLossPercent) { this.gainLossPercent = gainLossPercent; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }
}
