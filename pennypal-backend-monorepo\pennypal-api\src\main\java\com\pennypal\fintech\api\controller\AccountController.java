package com.pennypal.fintech.api.controller;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.dto.AccountBalanceDeltaDto;
import com.pennypal.fintech.dto.AccountBalanceSummary;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.service.AccountBalanceService;
import com.pennypal.fintech.service.UserService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/v1/account")
@Tag(name = "Account", description = "Account/Account Balance management APIs")
public class AccountController {
  private static final Logger log = LoggerFactory.getLogger(AccountController.class);
    @Autowired
    private final AccountBalanceService accountService;

    @Autowired
    private final UserService userService;

     @Autowired
    private final AccountRepository accountRepository;

    public AccountController(AccountBalanceService accountService, UserService userService,AccountRepository accountRepository) {
        this.accountService = accountService;
        this.userService = userService;
        this.accountRepository = accountRepository;
    }
   

    @GetMapping("/{userId}/balances/monthly")
    public ResponseEntity< Map<String, List<AccountBalanceSummary>>> getMonthlyBalances(@PathVariable Long userId) {
        Map<String, List<AccountBalanceSummary>> balances = accountService.getBalancesGroupedByAccountType(userId);
        return ResponseEntity.ok(balances);

    }
	
	@Operation(
        summary = "Fetch aggregated account balances",
        description = "Retrieves aggregated account balances for a user over a specified time period and interval. " +
                      "The data is grouped into intervals of 'y' days over a period of 'x' months."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated balances",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "Empty reponse",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/balances/agg/{userId}/{duration}/{interval}")
    // x - duration; y - interval
    public ResponseEntity<?> getAggregatedBalances(
        @Parameter(
            description = "ID of the user to get balances for",
            required = true
        )
        @PathVariable Integer userId,

        @Parameter(
            description = "Duration in months to look back",
            required = true
        )
        @PathVariable Integer duration,

        @Parameter(
            description = "Interval in days for grouping the data",
            required = true
        )
        @PathVariable Integer interval) {
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                if (duration == null) {
                    return ResponseEntity.badRequest().body("Duration is required.");
                }

                if (duration < 1) {
                    return ResponseEntity.badRequest().body("Duration must be at least 1 month.");
                }

                if (interval == null) {
                    return ResponseEntity.badRequest().body("Interval is required.");
                }

                if (interval < 1) {
                    return ResponseEntity.badRequest().body("Interval must be at least 1 day.");
                }

                List<Map<String, Object>> response = accountService.getAggregatedBalances(userId, duration, interval);
                
                if (response.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
                }
                
                return ResponseEntity.ok(response);
                
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch aggregated balances by account type",
        description = "Retrieves aggregated account balances for a specific account type for a user over a specified time period and interval. " +
                      "The data is grouped into intervals of 'y' days over a period of 'x' months for account type 'z'."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved aggregated balances",
            content = @Content(
                mediaType = "application/json")
        ),
        @ApiResponse(
            responseCode = "204",
            description = "Empty response",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/balances/agg/{userId}/{duration}/{interval}/{accountType}")
    public ResponseEntity<?> getAggregatedBalancesByAccountType(
        @Parameter(
            description = "ID of the user to get balances for",
            required = true
        )
        @PathVariable Integer userId,

        @Parameter(
            description = "Duration in months to look back",
            required = true
        )
        @PathVariable Integer duration,

        @Parameter(
            description = "Interval in days for grouping the data",
            required = true
        )
        @PathVariable Integer interval,

        @Parameter(
            description = "Account type to filter by. Must be one of: credit, depository, loan, and investment",
            required = true
        )
        @PathVariable
            @Pattern(
                regexp = "^(credit|depository|loan|investment)$",
                message = "Invalid account type. Must be one of: credit, depository, loan, and investment"
            ) String accountType) {

            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                if (duration == null) {
                    return ResponseEntity.badRequest().body("Duration is required.");
                }

                if (duration < 1) {
                    return ResponseEntity.badRequest().body("Duration must be at least 1 month.");
                }

                if (interval == null) {
                    return ResponseEntity.badRequest().body("Interval is required.");
                }

                if (interval < 1) {
                    return ResponseEntity.badRequest().body("Interval must be at least 1 day.");
                }
    
                List<Map<String, Object>> response = accountService.getAggregatedBalancesByAccountType(userId, duration, interval, accountType);
                
                if (response.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
                }
                
                return ResponseEntity.ok(response);
                
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch account balance deltas for provided month and year",
        description = "Retrieves month-over-month changes in balances across accounts, income, and expenses. " +
                      "Calculates both absolute and percentage changes comparing the specified month to the previous month."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Successfully retrieved monthly balance deltas",
            content = @Content(
                mediaType = "application/json"
            )
        ),
        @ApiResponse(
            responseCode = "204",
            description = "Empty response",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid parameters provided",
            content = @Content
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error while processing the request",
            content = @Content
        )
    })
    @GetMapping("/balances/delta/monthly/{userId}/{year}/{month}")
    public ResponseEntity<?> getAccountBalanceMonthlyDeltas(
        @Parameter(
            description = "ID of the user to get deltas for",
            required = true
        )
        @PathVariable Integer userId,

        @Parameter(
            description = "Year for which to calculate deltas (in YYYY format)",
            required = true
        )
		
        @PathVariable
            /*@Pattern(
                regexp = "^[0-9]{4}$",
                message = "Year must be in YYYY format"
            )*/
        Integer year,

        @Parameter(
            description = "Month for which to calculate deltas",
            required = true
        )
        @PathVariable Integer month) {
            
            try {
                if (userId == null) {
                    return ResponseEntity.badRequest().body("User ID is required.");
                }

                if (year == null) {
                    return ResponseEntity.badRequest().body("Year is required.");
                }

                if (month == null) {
                    return ResponseEntity.badRequest().body("Month is required.");
                }

                if (month < 1 || month > 12) {
                    return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
                }

                List<AccountBalanceDeltaDto> deltas = accountService.getAccountBalanceMonthlyDeltas(userId, year, month);
            
                if (deltas.isEmpty()) {
                    return ResponseEntity.status(HttpStatus.NO_CONTENT).body("No expenses found for the user");
                }
            
                return ResponseEntity.ok(deltas);
            
            } catch (IllegalArgumentException e) {
                return ResponseEntity.badRequest().body(e.getMessage());
            } catch (Exception e) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error processing request: " + e.getMessage());
            }
        }

    @Operation(
        summary = "Fetch aggregated account balances grouped by account ID",
        description = "Retrieves aggregated account balances for each account ID over a specified time period and interval. " +
                        "The data is grouped into intervals of 'y' days over a period of 'x' months."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved aggregated balances"),
        @ApiResponse(responseCode = "204", description = "No data found for the specified parameters"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/balances/agg/accountid/{userId}/{duration}/{interval}")
    public ResponseEntity<?> getAggregatedBalancesGroupedByAccountId(
        @Parameter(description = "ID of the user to get balances for", required = true)
        @PathVariable Integer userId,

        @Parameter(description = "Duration in months to look back", required = true)
        @PathVariable @Min(1) Integer duration,

        @Parameter(description = "Interval in days for grouping the data", required = true)
        @PathVariable @Min(1) Integer interval) {
            
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }

            if (duration == null) {
                return ResponseEntity.badRequest().body("Duration is required.");
            }

            if (duration < 1) {
                return ResponseEntity.badRequest().body("Duration must be at least 1 month.");
            }
            
            if (interval == null) {
                return ResponseEntity.badRequest().body("Interval is required.");
            }

            if (interval < 1) {
                return ResponseEntity.badRequest().body("Interval must be at least 1 day.");
            }

            List<Map<String, Object>> response = accountService
                .getAggregatedBalancesGroupedByAccountId(userId, duration, interval);
            
            if (response.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error processing request: " + e.getMessage());
        }
    }

    @Operation(
        summary = "Fetch account balance deltas by account ID",
        description = "Retrieves month-over-month changes in balances for each individual account ID. " +
                        "Calculates both absolute and percentage changes comparing the specified month to the previous month."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved balance deltas"),
        @ApiResponse(responseCode = "204", description = "No data found for the specified parameters"),
        @ApiResponse(responseCode = "400", description = "Invalid parameters provided"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/balances/delta/monthly/accountid/{userId}/{year}/{month}")
    public ResponseEntity<?> getAccountBalanceMonthlyDeltasGroupedByAccountId(
        @Parameter(description = "ID of the user to get deltas for", required = true)
        @PathVariable Integer userId,

        @Parameter(description = "Year for which to calculate deltas", required = true)
        @PathVariable Integer year,

        @Parameter(description = "Month for which to calculate deltas (1-12)", required = true)
        @PathVariable @Min(1) @Max(12) Integer month) {
            
        try {
            if (userId == null) {
                return ResponseEntity.badRequest().body("User ID is required.");
            }
            if (year == null) {
                return ResponseEntity.badRequest().body("Year is required.");
            }
            if (month == null) {
                return ResponseEntity.badRequest().body("Month is required.");
            }
            if (month < 1 || month > 12) {
                return ResponseEntity.badRequest().body("Month must be between 1 and 12.");
            }

            List<Map<String, Object>> response = accountService
                .getAccountBalanceMonthlyDeltasGroupedByAccountId(userId, year, month);
            
            if (response.isEmpty()) {
                return ResponseEntity.noContent().build();
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Error processing request: " + e.getMessage());
        }
    }

    // To fetch list of unique account IDs and their names for a user
    @Operation(
        summary = "Fetch unique account IDs and names for a user",
        description = "Retrieves a list of unique account IDs and their names for a specific user."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Successfully retrieved account IDs and names"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @GetMapping("/account/ids/{userId}")
public ResponseEntity<List<Map<String, Object>>> getUniqueAccountIds(
    @PathVariable int userId
) {
    try {
        List<Accounts> accounts = accountRepository.findByUserId(userId);
        
        List<Map<String, Object>> response = accounts.stream()
            .map(account -> {
                Map<String, Object> accountMap = new HashMap<>();
                accountMap.put("accountId", account.getId());
                accountMap.put("accountName", account.getAccountName());
                accountMap.put("accountMask", account.getAccountMask());
                accountMap.put("accountType", account.getAccountType());
                accountMap.put("balance", account.getBalance());
                return accountMap;
            })
            .collect(Collectors.toList());
            
        return ResponseEntity.ok(response);
    } catch (Exception e) {
        return ResponseEntity.internalServerError().build();
    }
}
// Add these new endpoints to your AccountController class
// Add these new endpoints to your AccountController class

  @Operation(
    summary = "Fetch account balance deltas for past 3 months",
    description = "Compares current 3-month period balances (Mar 3, 2025 to Jun 2, 2025) with past 3-month period balances (Dec 3, 2024 to Mar 2, 2025)"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 3-month deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/3months/accountid/{userId}")
public ResponseEntity<?> getAccountBalance3MonthDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalancePeriodDelta(userId, 3);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        LocalDate currentPeriodStart = currentDate.minusMonths(3).plusDays(1); // 2025-03-03
        LocalDate pastPeriodEnd = currentDate.minusMonths(3); // 2025-03-02
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(3).plusDays(1); // 2024-12-03
        
        // Add metadata for 3-month comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "3month"); // Changed from "3_MONTHS"
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "Current 3-month balance vs past 3-month balance"); // Updated description
        responseWithMetadata.put("accounts", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas for past 6 months",
    description = "Compares current 6-month period balances (Dec 3, 2024 to Jun 2, 2025) with past 6-month period balances (Jun 3, 2024 to Dec 2, 2024)"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 6-month deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/6months/accountid/{userId}")
public ResponseEntity<?> getAccountBalance6MonthDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalancePeriodDelta(userId, 6);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        LocalDate currentPeriodStart = currentDate.minusMonths(6).plusDays(1); // 2024-12-03
        LocalDate pastPeriodEnd = currentDate.minusMonths(6); // 2024-12-02
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(6).plusDays(1); // 2024-06-03
        
        // Add metadata for 6-month comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "6month"); // Changed from "6_MONTHS"
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "Current 6-month balance vs past 6-month balance"); // Updated description
        responseWithMetadata.put("accounts", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas for past 1 year",
    description = "Compares current 1-year period balances (Jun 3, 2024 to Jun 2, 2025) with past 1-year period balances (Jun 3, 2023 to Jun 2, 2024)"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 1-year deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/1year/accountid/{userId}")
public ResponseEntity<?> getAccountBalance1YearDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalancePeriodDelta(userId, 12);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        LocalDate currentPeriodStart = currentDate.minusMonths(12).plusDays(1); // 2024-06-03
        LocalDate pastPeriodEnd = currentDate.minusMonths(12); // 2024-06-02
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(12).plusDays(1); // 2023-06-03
        
        // Add metadata for 1-year comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "1year"); // Changed from "1_YEAR"
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "Current year balance vs past year balance"); // Updated description
        responseWithMetadata.put("accounts", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas for past 1 month",
    description = "Compares current 30-day period balances (May 4, 2025 to Jun 2, 2025) with past 30-day period balances (Apr 4, 2025 to May 3, 2025)"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 1-month deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/1month/accountid/{userId}")
public ResponseEntity<?> getAccountBalance1MonthDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalancePeriodDelta(userId, 1);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        LocalDate currentPeriodStart = currentDate.minusDays(29); // 2025-05-04 (30 days including today)
        LocalDate pastPeriodEnd = currentDate.minusDays(30); // 2025-05-03
        LocalDate pastPeriodStart = pastPeriodEnd.minusDays(29); // 2025-04-04 (30 days)
        
        // Add metadata for 1-month comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "1month");
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "Current 30-day balance vs past 30-day balance");
        responseWithMetadata.put("accounts", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas for Year to Date",
    description = "Compares current YTD period balances (Jan 1, 2025 to Jun 2, 2025 - 153 days) with past equivalent period balances (Aug 1, 2024 to Dec 31, 2024 - 153 days)"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved YTD deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/ytd/accountid/{userId}")
public ResponseEntity<?> getAccountBalanceYTDDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        LocalDate currentDate = LocalDate.now(); // 2025-06-02
        LocalDate ytdStart = LocalDate.of(currentDate.getYear(), 1, 1); // 2025-01-01
        int ytdDays = (int) ChronoUnit.DAYS.between(ytdStart, currentDate) + 1; // 153 days
        
        // Past period: 153 days before 2025-01-01
        LocalDate pastPeriodEnd = ytdStart.minusDays(1); // 2024-12-31
        LocalDate pastPeriodStart = pastPeriodEnd.minusDays(ytdDays - 1); // 2024-08-01 (153 days before)
        
        List<Map<String, Object>> response = accountService
            .getAccountBalanceYTDDelta(userId, ytdStart, currentDate, pastPeriodStart, pastPeriodEnd);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        // Add metadata for YTD comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "ytd");
        responseWithMetadata.put("currentPeriodDate", ytdStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "Year to Date balance vs equivalent past period balance (" + ytdDays + " days each)");
        responseWithMetadata.put("ytdDays", ytdDays);
        responseWithMetadata.put("accounts", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch quarterly account balance deltas with corrected rolling comparison",
    description = "Rolling quarterly comparison based on current date (2025-06-02): Q2 2025 vs Q1 2025, Q1 2025 vs Q4 2024, Q4 2024 vs Q3 2024, Q3 2024 vs Q2 2024"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved quarterly deltas"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/quarterly-rolling/accountid/{userId}")
public ResponseEntity<?> getAccountBalanceQuarterlyRollingDeltas(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        Map<String, List<Map<String, Object>>> response = accountService
            .getAccountBalanceQuarterlyRollingDeltasGroupedByAccountId(userId);
        
        if (response.isEmpty() || response.values().stream().allMatch(List::isEmpty)) {
            return ResponseEntity.noContent().build();
        }
        
        // Add metadata for quarterly rolling comparison
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "quarterly-rolling");
        responseWithMetadata.put("description", "Rolling quarterly comparison based on current date (Jun 2, 2025)");
        responseWithMetadata.put("quarters", response);
        
        // Add corrected quarterly date ranges and comparisons
        Map<String, Object> quarterDetails = new HashMap<>();
        quarterDetails.put("Q1", Map.of(
            "range", "Jan 1 - Mar 31, 2025",
            "comparison", "Q1 2025 vs Q4 2024"
        ));
        quarterDetails.put("Q2", Map.of(
            "range", "Apr 1 - Jun 30, 2025", 
            "comparison", "Q2 2025 vs Q1 2025"
        ));
        quarterDetails.put("Q3", Map.of(
            "range", "Jul 1 - Sep 30, 2024",
            "comparison", "Q3 2024 vs Q2 2024"
        ));
        quarterDetails.put("Q4", Map.of(
            "range", "Oct 1 - Dec 31, 2024",
            "comparison", "Q4 2024 vs Q3 2024"
        ));
        responseWithMetadata.put("quarterDetails", quarterDetails);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}
@Operation(
    summary = "Fetch account balance deltas aggregated by account type for past 1 month",
    description = "Aggregates current 30-day period balances vs past 30-day period balances by account type"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 1-month deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/1month/account-type/{userId}")
public ResponseEntity<?> getAccountBalance1MonthDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalanceDeltaByAccountType(userId, 1);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now();
        LocalDate currentPeriodStart = currentDate.minusDays(29);
        LocalDate pastPeriodEnd = currentDate.minusDays(30);
        LocalDate pastPeriodStart = pastPeriodEnd.minusDays(29);
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "1month");
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "1-month delta amounts aggregated by account type");
        responseWithMetadata.put("accountTypes", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas aggregated by account type for past 3 months",
    description = "Aggregates current 3-month period balances vs past 3-month period balances by account type"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 3-month deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/3months/account-type/{userId}")
public ResponseEntity<?> getAccountBalance3MonthDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalanceDeltaByAccountType(userId, 3);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now();
        LocalDate currentPeriodStart = currentDate.minusMonths(3).plusDays(1);
        LocalDate pastPeriodEnd = currentDate.minusMonths(3);
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(3).plusDays(1);
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "3month");
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "3-month delta amounts aggregated by account type");
        responseWithMetadata.put("accountTypes", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas aggregated by account type for past 6 months",
    description = "Aggregates current 6-month period balances vs past 6-month period balances by account type"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 6-month deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/6months/account-type/{userId}")
public ResponseEntity<?> getAccountBalance6MonthDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalanceDeltaByAccountType(userId, 6);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now();
        LocalDate currentPeriodStart = currentDate.minusMonths(6).plusDays(1);
        LocalDate pastPeriodEnd = currentDate.minusMonths(6);
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(6).plusDays(1);
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "6month");
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "6-month delta amounts aggregated by account type");
        responseWithMetadata.put("accountTypes", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas aggregated by account type for past 1 year",
    description = "Aggregates current 1-year period balances vs past 1-year period balances by account type"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved 1-year deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/1year/account-type/{userId}")
public ResponseEntity<?> getAccountBalance1YearDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalanceDeltaByAccountType(userId, 12);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now();
        LocalDate currentPeriodStart = currentDate.minusMonths(12).plusDays(1);
        LocalDate pastPeriodEnd = currentDate.minusMonths(12);
        LocalDate pastPeriodStart = pastPeriodEnd.minusMonths(12).plusDays(1);
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "1year");
        responseWithMetadata.put("currentPeriodDate", currentPeriodStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "1-year delta amounts aggregated by account type");
        responseWithMetadata.put("accountTypes", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch account balance deltas aggregated by account type for Year to Date",
    description = "Aggregates YTD period balances vs equivalent past period balances by account type"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved YTD deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/ytd/account-type/{userId}")
public ResponseEntity<?> getAccountBalanceYTDDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        List<Map<String, Object>> response = accountService
            .getAccountBalanceYTDDeltaByAccountType(userId);
        
        if (response.isEmpty()) {
            return ResponseEntity.noContent().build();
        }
        
        LocalDate currentDate = LocalDate.now();
        LocalDate ytdStart = LocalDate.of(currentDate.getYear(), 1, 1);
        int ytdDays = (int) ChronoUnit.DAYS.between(ytdStart, currentDate) + 1;
        
        LocalDate pastPeriodEnd = ytdStart.minusDays(1);
        LocalDate pastPeriodStart = pastPeriodEnd.minusDays(ytdDays - 1);
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "ytd");
        responseWithMetadata.put("currentPeriodDate", ytdStart.toString() + " to " + currentDate.toString());
        responseWithMetadata.put("pastPeriodDate", pastPeriodStart.toString() + " to " + pastPeriodEnd.toString());
        responseWithMetadata.put("description", "YTD delta amounts aggregated by account type (" + ytdDays + " days each)");
        responseWithMetadata.put("ytdDays", ytdDays);
        responseWithMetadata.put("accountTypes", response);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

@Operation(
    summary = "Fetch quarterly account balance deltas aggregated by account type",
    description = "Rolling quarterly comparison aggregated by account type: Q2 2025 vs Q1 2025, Q1 2025 vs Q4 2024, Q4 2024 vs Q3 2024, Q3 2024 vs Q2 2024"
)
@ApiResponses(value = {
    @ApiResponse(responseCode = "200", description = "Successfully retrieved quarterly deltas by account type"),
    @ApiResponse(responseCode = "204", description = "No data found"),
    @ApiResponse(responseCode = "400", description = "Invalid parameters"),
    @ApiResponse(responseCode = "500", description = "Internal server error")
})
@GetMapping("/balances/delta/quarterly-rolling/account-type/{userId}")
public ResponseEntity<?> getAccountBalanceQuarterlyRollingDeltasByAccountType(
    @Parameter(description = "ID of the user to get deltas for", required = true)
    @PathVariable @NotNull @Positive Integer userId) {
    
    try {
        Map<String, List<Map<String, Object>>> response = accountService
            .getQuarterlyDeltaByAccountType(userId);
        
        if (response.isEmpty() || response.values().stream().allMatch(List::isEmpty)) {
            return ResponseEntity.noContent().build();
        }
        
        Map<String, Object> responseWithMetadata = new HashMap<>();
        responseWithMetadata.put("comparisonType", "quarterly-rolling");
        responseWithMetadata.put("description", "Rolling quarterly delta amounts aggregated by account type");
        responseWithMetadata.put("quarters", response);
        
        // Add quarterly date ranges and comparisons
        Map<String, Object> quarterDetails = new HashMap<>();
        quarterDetails.put("Q1", Map.of(
            "range", "Jan 1 - Mar 31, 2025",
            "comparison", "Q1 2025 vs Q4 2024"
        ));
        quarterDetails.put("Q2", Map.of(
            "range", "Apr 1 - Jun 30, 2025", 
            "comparison", "Q2 2025 vs Q1 2025"
        ));
        quarterDetails.put("Q3", Map.of(
            "range", "Jul 1 - Sep 30, 2024",
            "comparison", "Q3 2024 vs Q2 2024"
        ));
        quarterDetails.put("Q4", Map.of(
            "range", "Oct 1 - Dec 31, 2024",
            "comparison", "Q4 2024 vs Q3 2024"
        ));
        responseWithMetadata.put("quarterDetails", quarterDetails);
        
        return ResponseEntity.ok(responseWithMetadata);
        
    } catch (RuntimeException e) {
        if (e.getMessage().contains("User not found")) {
            return ResponseEntity.badRequest().body("User ID " + userId + " not found.");
        }
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body("Error processing request: " + e.getMessage());
    }
}

}