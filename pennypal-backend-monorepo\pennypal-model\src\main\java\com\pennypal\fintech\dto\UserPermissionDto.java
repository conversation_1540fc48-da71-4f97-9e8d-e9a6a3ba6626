package com.pennypal.fintech.dto;

import com.pennypal.fintech.entity.AclActions;

public class UserPermissionDto {
    private String page;
    private AclActions.ActionName action;

    public UserPermissionDto(String page, AclActions.ActionName action) {
        this.page = page;
        this.action = action;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public AclActions.ActionName getAction() {
        return action;
    }

    public void setAction(AclActions.ActionName action) {
        this.action = action;
    }

    // ✅ Add these methods for compatibility
    public String getPageName() {
        return page;
    }

    public String getActionName() {
        return action != null ? action.name() : null;
    }
}
