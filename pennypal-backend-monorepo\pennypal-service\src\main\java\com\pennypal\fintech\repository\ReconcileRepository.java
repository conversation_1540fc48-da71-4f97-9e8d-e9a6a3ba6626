package com.pennypal.fintech.repository;

import com.pennypal.fintech.entity.Reconcile;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReconcileRepository extends JpaRepository<Reconcile, Long> {

     List<Reconcile> findAllByRemovedFalse();
    List<Reconcile> findByReconcileId(String reconcileId);
    // Reconcile findByTransactionId(Long transactionId);    
    
}
