package com.pennypal.fintech.dto;

import lombok.Data;

@Data
public class FinicityTransaction {
    private long id;
    private double amount;
    private long accountId;
    private long customerId;
    private String status;
    private String description;
    private String memo;
    private String type;
    private long postedDate;
    private long transactionDate;
    private long createdDate;
    private Categorization categorization;
    private String investmentTransactionType;
    private Long ofxCheckNumber;

    @Data
    public static class Categorization {
        private String normalizedPayeeName;
        private String category;
        private String bestRepresentation;
        private String country;
    }
}