package com.pennypal.fintech.repository;

import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class ChatbotRepository {
    private final JdbcTemplate jdbcTemplate;

    public List<Map<String, Object>> getTransactions(Integer userId) {
        System.out.println("Fetching transactions for userId: " + userId);
        return jdbcTemplate.queryForList(
            "SELECT DATE(transaction_date) AS date, description, category, transaction_amount AS amount " +
			"FROM transactions " +
            "WHERE user_id = ? " +
            "ORDER BY transaction_date DESC",
            userId
        );
    }

    public List<Map<String, Object>> getAccountBalances(Integer userId) {
        System.out.println("Fetching account balances for userId: " + userId);
        return jdbcTemplate.queryForList(
            "WITH accounts_temp AS( " +
            "SELECT a.account_category, a.account_name, a.account_subtype, a.account_type, " +
            "ab.balance, ab.timestamp, " +
            "ROW_NUMBER() OVER (PARTITION BY a.id ORDER BY ab.timestamp DESC) AS rn " +
            "FROM accounts a JOIN account_balances ab " +
            "ON a.id = ab.account_id " +
            "WHERE a.user_id = ?) " +
            "SELECT account_category, account_name, account_subtype, account_type, balance " +
            "FROM accounts_temp " +
            "WHERE rn = 1",
            userId
        );
    }

    public List<Map<String, Object>> getBudgets(Integer userId) {
        System.out.println("Fetching budgets for userId: " + userId);
        return jdbcTemplate.queryForList(
            "WITH budget_temp AS ( " +
			"SELECT c.category, sc.sub_category, b.allocated, b.actual, b.date, " +
			"ROW_NUMBER() OVER(PARTITION BY b.category_id, b.sub_category_id ORDER BY date DESC) AS rn " +
			"FROM budget b " +
			"JOIN category c ON b.category_id = c.id " +
			"JOIN sub_category sc ON b.sub_category_id = sc.id " +
			"WHERE user_id = ? " +
			"ORDER BY date DESC) " +
			"SELECT category, sub_category, allocated, actual FROM budget_temp WHERE rn = 1",
            userId
        );
    }
}