package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "transaction_summary")
public class TransactionSummary {
     @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "total_transaction")
    private Integer totalTransaction;

    @Column(name = "total_credit", precision = 15, scale = 2)
    private BigDecimal totalCredit;

    @Column(name = "total_debit", precision = 15, scale = 2)
    private BigDecimal totalDebit;

    @Column(name = "total_amount", precision = 15, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "largest_transaction", precision = 15, scale = 2)
    private BigDecimal largestTransaction;

    @Column(name = "dispute_transaction")
    private Integer disputeTransaction;

   @Column(name = "first_transaction_date")
private LocalDateTime firstTransactionDate;

@Column(name = "latest_transaction_date")
private LocalDateTime latestTransactionDate;

     public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getTotalTransaction() {
        return totalTransaction;
    }

    public void setTotalTransaction(Integer totalTransaction) {
        this.totalTransaction = totalTransaction;
    }

    public BigDecimal getTotalCredit() {
        return totalCredit;
    }

    public void setTotalCredit(BigDecimal totalCredit) {
        this.totalCredit = totalCredit;
    }

    public BigDecimal getTotalDebit() {
        return totalDebit;
    }

    public void setTotalDebit(BigDecimal totalDebit) {
        this.totalDebit = totalDebit;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getLargestTransaction() {
        return largestTransaction;
    }

    public void setLargestTransaction(BigDecimal largestTransaction) {
        this.largestTransaction = largestTransaction;
    }

    public Integer getDisputeTransaction() {
        return disputeTransaction;
    }

    public void setDisputeTransaction(Integer disputeTransaction) {
        this.disputeTransaction = disputeTransaction;
    }

    public LocalDateTime getFirstTransactionDate() {
    return firstTransactionDate;
}

public void setFirstTransactionDate(LocalDateTime firstTransactionDate) {
    this.firstTransactionDate = firstTransactionDate;
}

public LocalDateTime getLatestTransactionDate() {
    return latestTransactionDate;
}

public void setLatestTransactionDate(LocalDateTime latestTransactionDate) {
    this.latestTransactionDate = latestTransactionDate;
}

    
}
