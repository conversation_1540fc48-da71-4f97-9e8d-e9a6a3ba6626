package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Column;
import jakarta.persistence.Table;

import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.FetchType;
import jakarta.persistence.Lob;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonIgnore;

@Entity
@Table(name = "receipts")

public class Receipts {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "transaction_id")
    @JsonIgnore
    private Transactions transaction; // Assuming you have a Transaction entity.

    @Column(name = "user_id", nullable = false)
    private Long userId;
   
    @Column(name = "receipt_name")
    private String savedFilePath;

    @Column(name = "insert_time")
    private LocalDateTime insertTime;

    @Column(name = "merchant_name")
    private String merchantName;

    @Column(name = "merchant_address")
    private String merchantAddress;

    @Column(name = "merchant_phno")
    private String merchantPhno;

    @Column(name = "transaction_date")
    private LocalDateTime transactionDate;

    @Column(name = "transaction_subtotal")
    private BigDecimal transactionSubtotal;

    @Column(name = "trans_tax")
    private BigDecimal transTax;

    @Column(name = "trans_total")
    private BigDecimal transTotal;

    @Column(name = "trans_returnby")
    private String transReturnby;
    
    @Column(name = "doc_type", nullable = false)
    private String docType = "receipt";

    @Column(name = "qr_data")
    private String qrData;

    @Column(name = "category", nullable = false)
    private String category = "Uncategorized";

    @Column(name = "size", nullable = false)
    private long size = 0L;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "upload_date")
    private LocalDate uploadDate;

 @Lob
@Column(name = "raw_blob", length = 16777215)
private byte[] rawBlob;

@Column(name = "scanned_copy_path")
private String scannedCopyPath;

@Column(name = "doc_name")
private String docName;
    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
 public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public Transactions getTransaction() {
        return transaction;
    }

    public void setTransaction(Transactions transaction) {
        this.transaction = transaction;
    }

    public String getSavedFilePath() {
        return savedFilePath;
    }

    public void setSavedFilePath(String savedFilePath) {
        this.savedFilePath = savedFilePath;
    }

    public LocalDateTime getInsertTime() {
        return insertTime;
    }

    public void setInsertTime(LocalDateTime insertTime) {
        this.insertTime = insertTime;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public String getMerchantAddress() {
        return merchantAddress;
    }

    public void setMerchantAddress(String merchantAddress) {
        this.merchantAddress = merchantAddress;
    }

    public String getMerchantPhno() {
        return merchantPhno;
    }

    public void setMerchantPhno(String merchantPhno) {
        this.merchantPhno = merchantPhno;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public BigDecimal getTransactionSubtotal() {
        return transactionSubtotal;
    }

    public void setTransactionSubtotal(BigDecimal transactionSubtotal) {
        this.transactionSubtotal = transactionSubtotal;
    }

    public BigDecimal getTransTax() {
        return transTax;
    }

    public void setTransTax(BigDecimal transTax) {
        this.transTax = transTax;
    }

    public BigDecimal getTransTotal() {
        return transTotal;
    }

    public void setTransTotal(BigDecimal transTotal) {
        this.transTotal = transTotal;
    }

    public String getTransReturnby() {
        return transReturnby;
    }

    public void setTransReturnby(String transReturnby) {
        this.transReturnby = transReturnby;
    }
    public String getDocType() {
        return docType;
    }

    public void setDocType(String docType) {
        this.docType = docType;
    }

    public String getQrData() {
        return qrData;
    }

    public void setQrData(String qrData) {
        this.qrData = qrData;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public long getSize() {
        return size;
    }

    public void setSize(long size) {
        this.size = size;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public LocalDate getUploadDate() {
        return uploadDate;
    }

    public void setUploadDate(LocalDate uploadDate) {
        this.uploadDate = uploadDate;
    }

    public byte[] getRawBlob() {
        return rawBlob;
    }
    
    public void setRawBlob(byte[] rawBlob) {
        this.rawBlob = rawBlob;
    }

    public String getScannedCopyPath() {
        return scannedCopyPath;
    }

    public void setScannedCopyPath(String scannedCopyPath) {
        this.scannedCopyPath = scannedCopyPath;
    }

    public String getDocName() {
        return docName;
    }
    
    public void setDocName(String docName) {
        this.docName = docName;
    }
    
}
