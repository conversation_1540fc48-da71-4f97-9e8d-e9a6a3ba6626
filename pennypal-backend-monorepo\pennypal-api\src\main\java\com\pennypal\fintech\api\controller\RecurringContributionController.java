package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.RecurringContributionDto;
import com.pennypal.fintech.exception.InsufficientFundsException;
import com.pennypal.fintech.exception.InvalidRequestException;
import com.pennypal.fintech.exception.ResourceNotFoundException;
import com.pennypal.fintech.service.RecurringContributionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/recurring-contributions")
public class RecurringContributionController {
    private static final Logger logger = LoggerFactory.getLogger(RecurringContributionController.class);

    private final RecurringContributionService recurringContributionService;

    @Autowired
    public RecurringContributionController(RecurringContributionService recurringContributionService) {
        this.recurringContributionService = recurringContributionService;
    }

    @PostMapping
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> createRecurringContribution(
            @RequestHeader("User-Id") Integer userId,
            @RequestBody RecurringContributionDto.CreateRecurringContributionRequest request) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.setupRecurringContribution(userId, request);
            return new ResponseEntity<>(response, HttpStatus.CREATED);
        } catch (ResourceNotFoundException | InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error creating recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error creating recurring contribution. Please try again later.", e);
        }
    }

    @GetMapping("/goal/{goalId}")
    public ResponseEntity<List<RecurringContributionDto.RecurringContributionResponse>> getRecurringContributionsForGoal(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer goalId) {
        try {
            List<RecurringContributionDto.RecurringContributionResponse> responses = 
                    recurringContributionService.getRecurringContributionsForGoal(userId, goalId);
            return ResponseEntity.ok(responses);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error retrieving recurring contributions: " + e.getMessage(), e);
            throw new RuntimeException("Error retrieving recurring contributions. Please try again later.", e);
        }
    }

    @GetMapping
    public ResponseEntity<List<RecurringContributionDto.RecurringContributionResponse>> getAllRecurringContributions(
            @RequestHeader("User-Id") Integer userId) {
        try {
            List<RecurringContributionDto.RecurringContributionResponse> responses = 
                    recurringContributionService.getAllRecurringContributions(userId);
            return ResponseEntity.ok(responses);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error retrieving all recurring contributions: " + e.getMessage(), e);
            throw new RuntimeException("Error retrieving all recurring contributions. Please try again later.", e);
        }
    }

    @PutMapping("/{contributionId}")
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> updateRecurringContribution(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer contributionId,
            @RequestBody RecurringContributionDto.UpdateRecurringContributionRequest request) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.updateRecurringContribution(userId, contributionId, request);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException | InvalidRequestException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error updating recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error updating recurring contribution. Please try again later.", e);
        }
    }

    @DeleteMapping("/{contributionId}")
    public ResponseEntity<Void> deleteRecurringContribution(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer contributionId) {
        try {
            recurringContributionService.deleteRecurringContribution(userId, contributionId);
            return ResponseEntity.noContent().build();
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error deleting recurring contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error deleting recurring contribution. Please try again later.", e);
        }
    }

    @PostMapping("/{contributionId}/trigger")
    public ResponseEntity<RecurringContributionDto.RecurringContributionResponse> triggerContribution(
            @RequestHeader("User-Id") Integer userId,
            @PathVariable Integer contributionId) {
        try {
            RecurringContributionDto.RecurringContributionResponse response = 
                    recurringContributionService.manuallyTriggerContribution(userId, contributionId);
            return ResponseEntity.ok(response);
        } catch (ResourceNotFoundException e) {
            throw e;
        } catch (InsufficientFundsException e) {
            return ResponseEntity.status(HttpStatus.PAYMENT_REQUIRED)
                    .body(null); // Consider returning more details about insufficient funds
        } catch (Exception e) {
            logger.error("Error triggering contribution: " + e.getMessage(), e);
            throw new RuntimeException("Error triggering contribution. Please try again later.", e);
        }
    }
}