package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "custom_sub_category")
public class CustomSubCategory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "custom_sub_category_id")
    private Integer customSubCategoryId;
    
    @Column(name = "user_id", nullable = false)
    private Integer userId;
    
    @Column(name = "custom_sub_category_name", nullable = false)
    private String customSubCategoryName;
    
    @Column(name = "custom_sub_category_icon")
    private String customSubCategoryIcon;

    @Column(name = "category_id")
    private Integer categoryId;
    
    @Override
    public String toString() {
        return "CustomSubCategory{" +
                "customSubCategoryId=" + customSubCategoryId +
                ", userId=" + userId +
                ", customSubCategoryName='" + customSubCategoryName + '\'' +
                ", customSubCategoryIcon='" + customSubCategoryIcon + '\'' +
                '}';
    }
}