package com.pennypal.fintech.service;


import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import com.pennypal.fintech.dto.UserPermissionDto;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class JWTService {


    // private String secretkey = "";
    // Switching to static secret key
    // @Value("${jwt.secret}")
    // private String secretkey;
    private final AppConfigService appConfigService;

    public JWTService(AppConfigService appConfigService) {
        this.appConfigService = appConfigService;
    }

    private String secretkey;
    private long JWTTokenExpirationMs;
    private long refreshTokenExpirationMs;

    @EventListener(ContextRefreshedEvent.class)
    public void loadJWTConfigs() {
        secretkey = appConfigService.getConfig("jwt_secret");
        JWTTokenExpirationMs = Long.parseLong(appConfigService.getConfig("jwt_token_expiration_ms"));
        refreshTokenExpirationMs = Long.parseLong(appConfigService.getConfig("refresh_token_expiration_ms"));
        System.out.println("JWT configs loaded");
        System.out.println("JWT Token Expiration Ms: " + JWTTokenExpirationMs);
        System.out.println("Refresh Token Expiration Ms: " + refreshTokenExpirationMs);
    }

    @EventListener(AppConfigService.ConfigurationRefreshedEvent.class)
    public void reloadJWTConfigs(AppConfigService.ConfigurationRefreshedEvent event) {
        loadJWTConfigs();
    }

    // public JWTService() {

        /** Switching to static secret key - commented out */
        // try {
        //     KeyGenerator keyGen = KeyGenerator.getInstance("HmacSHA256");
        //     SecretKey sk = keyGen.generateKey();
        //     secretkey = Base64.getEncoder().encodeToString(sk.getEncoded());
        // } catch (NoSuchAlgorithmException e) {
        //     throw new RuntimeException(e);
        // }
    // }
/* 
    public String generateToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        return Jwts.builder()
                .addClaims(claims)
                .setSubject(username)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                // For 30 days
                .setExpiration(new Date(System.currentTimeMillis() + 1000 * 60 * 60 * 24 * 30))
                .signWith(getKey())
                .compact();

    }
*/
    public String generateToken(String username, Integer userId, List<UserPermissionDto> permissions) {
        log.info("Generating JWT token for user: " + username + " with userId: " + userId);
        Map<String, Object> claims = new HashMap<>();
        // Add user id to claims
        claims.put("userId", userId);

        // Add permissions to claims
        List<Map<String, String>> permissionsList = permissions.stream()
            .map(p -> {
                Map<String, String> permMap = new HashMap<>();
                permMap.put("page", p.getPage());
                permMap.put("action", p.getAction().name());
                return permMap;
            })
            .collect(Collectors.toList());
        
        claims.put("permissions", permissionsList);

        long currentTimeMillis = System.currentTimeMillis();
        // long expirationTimeMillis = currentTimeMillis + (1000L * 60 * 60 * 24 * 30); // Using long literal to prevent overflow
        long expirationTimeMillis = currentTimeMillis + JWTTokenExpirationMs;
        
        // Sanity check to ensure expiration is after issuance
        if (expirationTimeMillis <= currentTimeMillis) {
            // Log error and use a safe fallback
            System.err.println("Error: Token expiration calculation error detected");
            expirationTimeMillis = currentTimeMillis + (1000L * 60 * 60 * 24); // Fallback to 1 day
        }

        System.out.println("Current time: " + new Date(currentTimeMillis));
        System.out.println("Expiration time: " + new Date(expirationTimeMillis));
        
        return Jwts.builder()
                .addClaims(claims)
                .setSubject(username)
                .setIssuedAt(new Date(currentTimeMillis))
                .setExpiration(new Date(expirationTimeMillis))
                .signWith(getKey())
                .compact();
    }

    private SecretKey getKey() {
        byte[] keyBytes = Decoders.BASE64.decode(secretkey);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    public String extractUserName(String token) {
        // extract the username from jwt token
        return extractClaim(token, Claims::getSubject);
    }

    private <T> T extractClaim(String token, Function<Claims, T> claimResolver) {
        final Claims claims = extractAllClaims(token);
        return claimResolver.apply(claims);
    }
/* 
    private Claims extractAllClaims(String token) {
        return Jwts.parser()
                .verifyWith(getKey())
                .build()
                .parseSignedClaims(token)
                .getPayload();
    }*/

    private Claims extractAllClaims(String token) {
        try {
            Claims claims = Jwts.parserBuilder()
                    .setSigningKey(getKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
            
            System.out.println("\nToken Claims Details:");
            System.out.println("-------------------");
            System.out.println("All Claims: " + claims.toString());
            claims.entrySet().forEach(entry -> 
                System.out.println(entry.getKey() + ": " + entry.getValue())
            );
            
            return claims;
        } catch (Exception e) {
            System.err.println("Error extracting claims: " + e.getMessage());
            throw e;
        }
    }

    public boolean validateToken(String token, UserDetails userDetails) {
        try {
            final String userName = extractUserName(token);
            Claims claims = extractAllClaims(token);
            
            System.out.println("Token Validation Details:");
            System.out.println("------------------------");
            System.out.println("Username from token: " + userName);
            System.out.println("Username from UserDetails: " + userDetails.getUsername());
            System.out.println("Token Issue Date: " + claims.getIssuedAt());
            System.out.println("Token Expiration: " + claims.getExpiration());
            System.out.println("Current Time: " + new Date());
            System.out.println("Is Token Expired: " + isTokenExpired(token));
            
            boolean isValid = (userName.equals(userDetails.getUsername()) && !isTokenExpired(token));
            System.out.println("Is Token Valid: " + isValid);
            
            return isValid;
        } catch (Exception e) {
            System.err.println("Token Validation Error: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    private boolean isTokenExpired(String token) {
        Date expiration = extractExpiration(token);
        boolean isExpired = expiration.before(new Date());
        System.out.println("\nExpiration Check:");
        System.out.println("----------------");
        System.out.println("Token Expiration Date: " + expiration);
        System.out.println("Current Date: " + new Date());
        System.out.println("Is Expired: " + isExpired);
        return isExpired;
    }

    private Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    public String generateRefreshToken(String username, Integer userId) {
        log.info("Generating JWT refresh token for user: " + username + " with userId: " + userId);

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", userId);
        claims.put("refreshToken", true);

        long currentTimeMillis = System.currentTimeMillis();
        // long expirationTimeMillis = currentTimeMillis + (1000L * 60 * 60 * 24 * 30);
        long expirationTimeMillis = currentTimeMillis + refreshTokenExpirationMs;
        System.out.println("Current time: " + new Date(currentTimeMillis));
        System.out.println("Expiration time: " + new Date(expirationTimeMillis));

        return Jwts.builder()
                .addClaims(claims)
                .setSubject(username)
                .setIssuedAt(new Date(System.currentTimeMillis()))
                .setExpiration(new Date(expirationTimeMillis))
                .signWith(getKey())
                .compact();
    }

    public Boolean isValidRefreshToken(String token) {
        try {
            Jwts.parserBuilder()
                .setSigningKey(getKey())
                .build()
                .parseClaimsJws(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public Integer extractUserId(String token) {
        Claims claims = extractAllClaims(token);
        return claims.get("userId", Integer.class);
    }

    public String generateSecretKey() {
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("HmacSHA256");
            keyGen.init(256);
            SecretKey sk = keyGen.generateKey();
            return Base64.getEncoder().encodeToString(sk.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

}
