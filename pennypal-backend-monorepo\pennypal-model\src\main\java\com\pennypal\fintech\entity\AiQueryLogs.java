package com.pennypal.fintech.entity;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import lombok.Data;

@Data
@Entity
@Table(name = "ai_query_logs")
public class AiQueryLogs {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user_id")
    private Integer userId;

    @Column(name = "user_query", columnDefinition = "TEXT")
    private String userQuery;

    @Column(name = "sql_query", columnDefinition = "TEXT")
    private String sqlQuery;

    @Column(name = "result", columnDefinition = "TEXT")
    private String result;

    @Column(name = "response", columnDefinition = "TEXT")
    private String response;

    @Column(name = "response_code")
    private Integer responseCode;

    private LocalDateTime createdAt = LocalDateTime.now();

    @Column(name = "is_saved")
    private Boolean isSaved = false;
}