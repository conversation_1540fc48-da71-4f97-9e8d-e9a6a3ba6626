package com.pennypal.fintech.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

import lombok.Data;

@Data
public class BudgetUIDto {
    private Integer id;
    private LocalDate date;
    private Integer categoryId;
    private String categoryName;
    private Integer subcategoryId;
    private String subcategoryName;
    private Integer customSubCategoryId;
    private String customSubCategoryName;
    private BigDecimal actual;
    private BigDecimal allocated;
    private BigDecimal remaining;
    private Boolean rollover;
    private String icon;
}