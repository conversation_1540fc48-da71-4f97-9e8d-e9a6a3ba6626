package com.pennypal.fintech.dto;
import java.math.BigDecimal;

public class AccountBalanceComparisonDto {
    private Integer accountId;
    private String accountName;
    private String accountCategory;
    private String accountType;
    private String accountSubtype;
    private String accountMask;
    private String institutionId;
    private Double currentBalance;
    private Double pastBalance;
    private Double deltaAmount;
    private Double deltaPercentage;

    // Default constructor
    public AccountBalanceComparisonDto() {}

    // Constructor
    public AccountBalanceComparisonDto(Integer accountId, String accountName, String accountCategory,
                                     String accountType, String accountSubtype, String accountMask, 
                                     String institutionId, Double currentBalance, Double pastBalance,
                                     Double deltaAmount, Double deltaPercentage) {
        this.accountId = accountId;
        this.accountName = accountName;
        this.accountCategory = accountCategory;
        this.accountType = accountType;
        this.accountSubtype = accountSubtype;
        this.accountMask = accountMask;
        this.institutionId = institutionId;
        this.currentBalance = currentBalance;
        this.pastBalance = pastBalance;
        this.deltaAmount = deltaAmount;
        this.deltaPercentage = deltaPercentage;
    }

    // Getters and Setters
    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountCategory() {
        return accountCategory;
    }

    public void setAccountCategory(String accountCategory) {
        this.accountCategory = accountCategory;
    }

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public String getAccountSubtype() {
        return accountSubtype;
    }

    public void setAccountSubtype(String accountSubtype) {
        this.accountSubtype = accountSubtype;
    }

    public String getAccountMask() {
        return accountMask;
    }

    public void setAccountMask(String accountMask) {
        this.accountMask = accountMask;
    }

    public String getInstitutionId() {
        return institutionId;
    }

    public void setInstitutionId(String institutionId) {
        this.institutionId = institutionId;
    }

    public Double getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(Double currentBalance) {
        this.currentBalance = currentBalance;
    }

    public Double getPastBalance() {
        return pastBalance;
    }

    public void setPastBalance(Double pastBalance) {
        this.pastBalance = pastBalance;
    }

    public Double getDeltaAmount() {
        return deltaAmount;
    }

    public void setDeltaAmount(Double deltaAmount) {
        this.deltaAmount = deltaAmount;
    }

    public Double getDeltaPercentage() {
        return deltaPercentage;
    }

    public void setDeltaPercentage(Double deltaPercentage) {
        this.deltaPercentage = deltaPercentage;
    }

    @Override
    public String toString() {
        return "AccountBalanceComparisonDto{" +
                "accountId=" + accountId +
                ", accountName='" + accountName + '\'' +
                ", accountCategory='" + accountCategory + '\'' +
                ", accountType='" + accountType + '\'' +
                ", accountSubtype='" + accountSubtype + '\'' +
                ", accountMask='" + accountMask + '\'' +
                ", institutionId='" + institutionId + '\'' +
                ", currentBalance=" + currentBalance +
                ", pastBalance=" + pastBalance +
                ", deltaAmount=" + deltaAmount +
                ", deltaPercentage=" + deltaPercentage +
                '}';
    }
}