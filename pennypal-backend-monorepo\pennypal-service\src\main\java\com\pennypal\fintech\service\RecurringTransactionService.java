package com.pennypal.fintech.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pennypal.fintech.dto.RecurringTransactionDto;
import com.pennypal.fintech.dto.RecurringTransactionUiDto;
import com.pennypal.fintech.dto.TransactionDto;
import com.pennypal.fintech.dto.FutureRecurringTransactionDto;
import com.pennypal.fintech.entity.Notification;
import com.pennypal.fintech.entity.Notification.NotificationState;
import com.pennypal.fintech.entity.NotificationRules;
import com.pennypal.fintech.entity.NotificationTracking;
import com.pennypal.fintech.entity.RecurringTransactions;
import com.pennypal.fintech.entity.UserNotificationRules;
import com.pennypal.fintech.repository.NotificationRepository;
import com.pennypal.fintech.repository.NotificationRuleRepository;
import com.pennypal.fintech.repository.NotificationTrackingRepository;
import com.pennypal.fintech.repository.RecurringTransactionRepository;
import com.pennypal.fintech.repository.TransactionRepository;
import com.pennypal.fintech.repository.UserRepository;
import com.pennypal.fintech.repository.UserNotificationRulesRepository;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class RecurringTransactionService {

    @Autowired
    private final RecurringTransactionRepository recurringTransactionRepository;

    @Autowired
    private final UserRepository userRepository;

    @Autowired
    private final NotificationRepository notificationRepository;
    
    @Autowired
    private final NotificationRuleRepository notificationRuleRepository;

    @Autowired
    private final NotificationTrackingRepository notificationTrackingRepository;

    @Autowired
    private final UserNotificationRulesRepository userNotificationRulesRepository;

    public RecurringTransactionService(RecurringTransactionRepository recurringTransactionRepository,
                                       TransactionRepository transactionRepository,
                                       UserRepository userRepository,
                                       UserNotificationRulesRepository userNotificationRulesRepository,
                                       NotificationRuleRepository notificationRuleRepository,
                                       NotificationTrackingRepository notificationTrackingRepository,
                                       NotificationRepository notificationRepository) {
        this.recurringTransactionRepository = recurringTransactionRepository;
        this.userRepository = userRepository;
        this.userNotificationRulesRepository = userNotificationRulesRepository;
        this.notificationRuleRepository = notificationRuleRepository;
        this.notificationTrackingRepository = notificationTrackingRepository;
        this.notificationRepository = notificationRepository;
    }

    private record RecurringPattern(String description, String recurringFrequency) {}
    
    @CacheEvict(value = "transactionCache",
                key = "'fetchRecurringTransactionsWithTransactionDetails' + '_' + #userId",
                allEntries = true)
    @Transactional
    public void analyzeAndStoreRecurringTransactions(Integer userId) {
        log.info("Analyzing and storing recurring transactions for user: {}", userId);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }        

        // Execute the query to find recurring transactions
        List<Object[]> results = analyzeTransactions(userId);
        log.info("Results: {}", results);
        
        // Convert and save results to recurring_transactions table
        if (results != null) {
            for (Object[] result : results) {
            Integer userId1 = ((Number) result[0]).intValue();
            Integer transactionId = ((Number) result[1]).intValue();
            String description = (String) result[2];
            String recurringFrequency = (String) result[3];
            
            RecurringTransactions rt = new RecurringTransactions(userId1, transactionId, description, recurringFrequency);

            saveWithUniqueConstraintHandling(rt);
            }

            // Identify new recurring merchants
            if (results != null) {
        
                // Extract unique patterns using a Set
                Set<RecurringPattern> uniquePatterns = results.stream()
                    .map(result -> new RecurringPattern(
                        (String) result[2],               // description
                        (String) result[3]                // recurringFrequency
                    ))
                .collect(Collectors.toSet());

                log.info("Unique patterns: {}", uniquePatterns);
                if (uniquePatterns != null) {
                    checkNewRecurringMerchantsNotificationRules(userId, uniquePatterns);
                }
            }
        }
    }

    // "readOnly = true" for DB optimization - no write locks
    @Transactional(readOnly = true)
    private List<Object[]> analyzeTransactions(Integer userId) {
        return recurringTransactionRepository.analyzeRecurringTransactions(userId);
    }

    // Propogating new transaction everytime this method is called - not affecting existing transactions
    @Transactional(noRollbackFor = DataIntegrityViolationException.class)
    public void saveWithUniqueConstraintHandling(RecurringTransactions transaction) {
        try {
            recurringTransactionRepository.save(transaction);
        } catch (DataIntegrityViolationException e) {
            //
        }
    }

    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<RecurringTransactionDto> fetchRecurringTransactions(Integer userId) {
        log.info("Fetching recurring transactions for user: {}", userId);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }        

        return recurringTransactionRepository.findByUserId(userId);
    }

    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<RecurringTransactionUiDto> fetchRecurringTransactionsWithTransactionDetails(Integer userId) {
        log.info("Fetching recurring transactions with transaction details for user: {}", userId);
        if (!userRepository.existsById(userId)) {
            log.error("User not found");
            throw new RuntimeException("User not found");
        }        

        List<Object[]> results = recurringTransactionRepository.findByUserIdWithTransactionDetails(userId);
        return results.stream()
            .map(result -> new RecurringTransactionUiDto(
                (Integer) result[0],
                (String) result[1],
                (String) result[2],
                ((Number) result[3]).doubleValue(),
                (String) result[4],
                (result[5] != null) ? ((Date) result[5]).toLocalDate() : null,
                (String) result[6]
            ))
            .collect(Collectors.toList());
    }

    @Cacheable(value = "transactionCache", 
               key = "#root.methodName + '_' + #userId",
               unless = "#result == null")
    @Transactional(readOnly = true)
    public List<FutureRecurringTransactionDto> fetchFutureRecurringTransactions(Integer userId) {
        if (!userRepository.existsById(userId)) {
            throw new RuntimeException("User not found");
        }

        List<Object[]> results = recurringTransactionRepository.findFutureRecurringTransactions(userId);
        
        return results.stream()
            .map(result -> new FutureRecurringTransactionDto(
                (result[0] != null) ? ((Date) result[0]).toLocalDate() : null,
                (String) result[1],
                ((Number) result[2]).doubleValue(),
                (String) result[3],
                (String) result[4],
                (String) result[5]
            ))
            .collect(Collectors.toList());
    }

    // This will be triggered when new transactions come via event payload
    public void processNewTransaction(TransactionDto newTransaction) {
        Integer newId = newTransaction.getId();
        Integer newUserId = newTransaction.getUserId();
        String newDescription = newTransaction.getDescription();
        
        BigDecimal newTransactionAmount = BigDecimal.valueOf(newTransaction.getTransactionAmount());
        BigDecimal twentyPercent = newTransactionAmount.multiply(BigDecimal.valueOf(0.2));
        BigDecimal minTransAmt = newTransactionAmount.subtract(twentyPercent);
        BigDecimal maxTransAmt = newTransactionAmount.add(twentyPercent);

        LocalDate newTransactionDate = newTransaction.getTransactionDate().toLocalDate();

        List<Object[]> recurringMatches = recurringTransactionRepository.findRecurringMatches(
            newUserId, newDescription, minTransAmt, maxTransAmt);
        
        List<RecurringTransactionUiDto> newRecurringMatches = recurringMatches.stream()
            .map(result -> new RecurringTransactionUiDto(
                (Integer) result[0],
                (String) result[1],
                (String) result[2],
                ((Number) result[3]).doubleValue(),
                (String) result[4],
                (result[5] != null) ? ((Date) result[5]).toLocalDate() : null,
                (String) result[6]
            ))
            .collect(Collectors.toList());
        
        if (newRecurringMatches != null && !recurringTransactionRepository.existsByUserIdAndTransactionId(newUserId, newId)) {
            for (RecurringTransactionUiDto recurringMatch : newRecurringMatches) {
                String matchRecurringFrequency = recurringMatch.getRecurringFrequency();
                
                if (matchRecurringFrequency.equals("Monthly")) {
                    LocalDate matchTransactionDate = recurringMatch.getTransactionDate();
                    long daysBetween = ChronoUnit.DAYS.between(newTransactionDate, matchTransactionDate);
                    daysBetween = Math.abs(daysBetween);
                    if (daysBetween >= 25 && daysBetween <= 34) {
                        RecurringTransactions rt = new RecurringTransactions(newUserId, newId, newDescription, matchRecurringFrequency);
                        saveWithUniqueConstraintHandling(rt);
                        break;
                    }
                }
                if (matchRecurringFrequency.equals("Quarterly")) {
                    LocalDate matchTransactionDate = recurringMatch.getTransactionDate();
                    long daysBetween = ChronoUnit.DAYS.between(newTransactionDate, matchTransactionDate);
                    daysBetween = Math.abs(daysBetween);
                    if (daysBetween >= 85 && daysBetween <= 96) {
                        RecurringTransactions rt = new RecurringTransactions(newUserId, newId, newDescription, matchRecurringFrequency);
                        saveWithUniqueConstraintHandling(rt);
                        break;
                    }
                }
                if (matchRecurringFrequency.equals("Half-yearly")) {
                    LocalDate matchTransactionDate = recurringMatch.getTransactionDate();
                    long daysBetween = ChronoUnit.DAYS.between(newTransactionDate, matchTransactionDate);
                    daysBetween = Math.abs(daysBetween);
                    if (daysBetween >= 175 && daysBetween <= 189) {
                        RecurringTransactions rt = new RecurringTransactions(newUserId, newId, newDescription, matchRecurringFrequency);
                        saveWithUniqueConstraintHandling(rt);
                        break;
                    }
                }
                if (matchRecurringFrequency.equals("Yearly")) {
                    LocalDate matchTransactionDate = recurringMatch.getTransactionDate();
                    long daysBetween = ChronoUnit.DAYS.between(newTransactionDate, matchTransactionDate);
                    daysBetween = Math.abs(daysBetween);
                    if (daysBetween >= 362 && daysBetween <= 369) {
                        RecurringTransactions rt = new RecurringTransactions(newUserId, newId, newDescription, matchRecurringFrequency);
                        saveWithUniqueConstraintHandling(rt);
                        break;
                    }
                }
            }
        }
    }

    public void checkNewRecurringMerchantsNotificationRules(Integer userId, Set<RecurringPattern> uniquePatterns) {
        log.info("Checking new recurring merchants notification rules for user: {}", userId);

        // Fetch the notification rule for OUT_OF_SYNC
        String messageTemplate;
        String ruleType;
        NotificationRules.NotificationSeverity severity;

        if (!userNotificationRulesRepository.existsByUserIdAndRuleType(userId, "NEW_RECURRING_MERCHANT")) {
            NotificationRules rule1 = notificationRuleRepository.findByRuleType("NEW_RECURRING_MERCHANT");
            if (rule1 == null || !rule1.getIsEnabled()) {
                log.info("Fetched master notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched master notification rule: {}", rule1);
            messageTemplate = rule1.getMessageTemplate();
            ruleType = rule1.getRuleType();
            severity = rule1.getSeverity();
        } else {
            UserNotificationRules rule2 = userNotificationRulesRepository.findByUserIdAndRuleType(userId, "OUT_OF_SYNC_ALERT");
            if (rule2 == null || !rule2.getIsEnabled()) {
                log.info("Fetched user notification rule is inactive. Returning...");
                return;
            }
            log.info("Fetched user notification rule: {}", rule2);
            messageTemplate = rule2.getMessageTemplate();
            ruleType = rule2.getRuleType();
            severity = NotificationRules.NotificationSeverity.valueOf(rule2.getSeverity());
        }
        log.info("Message template: {}", messageTemplate);
        log.info("Rule type: {}", ruleType);
        log.info("Severity: {}", severity);
        
        for (RecurringPattern pattern : uniquePatterns) {
            String description = pattern.description();
            String recurringFrequency = pattern.recurringFrequency();

            if (!recurringTransactionRepository.existsByUserIdAndDescriptionAndRecurringFrequency(userId, description, recurringFrequency)) {
                String message = messageTemplate
                    .replace("{description}", description)
                    .replace("{frequency}", recurringFrequency);

                // If notification not already sent, create and save it
                if (!notificationTrackingRepository.existsByUserIdAndRuleTypeAndMessage(userId, ruleType, message)) {
                    log.info("Creating notification for user: {}", userId);
                    createNotification(userId, message, severity, ruleType);
                }                
            }
        }
    }

    @CacheEvict(value = "notificationCache",
                key = "'getUserNotifications' + '_' + #userId",
                allEntries = true)
    public void createNotification(Integer userId, String message, NotificationRules.NotificationSeverity severity, String ruleType) {
        Notification notification = new Notification();
        notification.setUserId(userId);
        notification.setDescription(message);
        notification.setSeverity(Notification.NotificationSeverity.valueOf(severity.name().toUpperCase()));
        notification.setState(NotificationState.unread);
        notification.setCreateTimestamp(LocalDateTime.now());
        
        notification = notificationRepository.save(notification);

        // Save notification tracking record
        NotificationTracking tracking = new NotificationTracking();
        tracking.setUserId(userId);
        tracking.setNotificationId(notification.getNotificationId());
        tracking.setRuleType(ruleType);
        tracking.setMessage(message);
        tracking.setCreatedTimestamp(notification.getCreateTimestamp());
        
        notificationTrackingRepository.save(tracking);
    }
}