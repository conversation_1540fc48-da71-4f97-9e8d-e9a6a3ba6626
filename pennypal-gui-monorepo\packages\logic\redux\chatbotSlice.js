import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  history: [],
  loading: false,
  error: null,
  querying: false,
};

const chatbotSlice = createSlice({
  name: 'chatbot',
  initialState,
  reducers: {
    fetchHistoryRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchHistorySuccess: (state, action) => {
      state.history = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchHistoryFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    queryRequest: (state) => {
      state.querying = true;
      state.error = null;
    },
    querySuccess: (state, action) => {
      state.querying = false;
      state.error = null;
      // Optionally update history if the response contains updated data
      if (action.payload) {
        state.history = action.payload;
      }
    },
    queryFailure: (state, action) => {
      state.querying = false;
      state.error = action.payload;
    },
  },
});

export const {
  fetchHistoryRequest,
  fetchHistorySuccess,
  fetchHistoryFailure,
  queryRequest,
  querySuccess,
  queryFailure,
} = chatbotSlice.actions;

export default chatbotSlice.reducer;