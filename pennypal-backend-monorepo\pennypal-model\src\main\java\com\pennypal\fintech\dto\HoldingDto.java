package com.pennypal.fintech.dto;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class HoldingDto {
    private String securityId;
    private String securityName;
    private String ticker;
    private String securityType;
    private Double quantity;
    private Double value;
    private Double costBasis;
    private Double currentPrice;
    private Double averagePurchasePrice;
    private String currencyCode;
    private LocalDateTime lastUpdated;
    private String accountId;
    private String investmentId;
    private Double gainLoss;
    private Double gainLossPercentage;
    
    // Additional fields that might be specific to Plaid or Finicity
    private String provider; // "plaid" or "finicity"
    private String description; // Finicity uses this instead of securityName
    private String symbol; // Finicity uses this instead of ticker
    private Double units; // Finicity uses this instead of quantity
    private Double marketValue; // Finicity uses this instead of value
    private Double unitPrice; // Finicity uses this instead of currentPrice
}