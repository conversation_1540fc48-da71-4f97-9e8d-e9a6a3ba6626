package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.service.InvestmentMxService;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.dto.InvestmentAggregationResponseDto;
import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.repository.InvestmentRepository;
import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/investments")
@Slf4j
public class InvestmentMxController {

    @Autowired
    private InvestmentMxService investmentMxService;
    
    @Autowired
    private InvestmentRepository investmentRepository;
    
    @Autowired
    private DailyInvestmentStockRepository dailyInvestmentStockRepository;

    /**
     * Sync investment holdings data from MX for a specific user
     * POST /api/investments/sync/{userId}
     */
    @PostMapping("/sync/{userId}")
    public ResponseEntity<Map<String, Object>> syncInvestmentData(@PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Received request to sync investment data for user ID: {}", userId);
            
            String result = investmentMxService.syncInvestmentData(userId);
            
            response.put("success", true);
            response.put("message", result);
            response.put("userId", userId);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (IllegalArgumentException e) {
            log.error("Invalid request for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.badRequest().body(response);
            
        } catch (Exception e) {
            log.error("Error syncing investment data for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to sync investment data: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get all investments for a specific user
     * GET /api/investments/user/{userId}
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> getUserInvestments(@PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching investments for user ID: {}", userId);
            
            List<Investment> investments = investmentRepository.findByUser_Id(userId);
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("investments", investments);
            response.put("count", investments.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching investments for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch investments: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get daily investment stock data for a specific user
     * GET /api/investments/daily/{userId}
     * Optional query parameters: date, ticker
     */
    @GetMapping("/daily/{userId}")
    public ResponseEntity<Map<String, Object>> getDailyInvestmentStocks(
            @PathVariable Integer userId,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(required = false) String ticker) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching daily investment stocks for user ID: {}, date: {}, ticker: {}", userId, date, ticker);
            
            List<DailyInvestmentStock> dailyStocks;
            
            if (date != null && ticker != null) {
                // Filter by both date and ticker
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDateAndTicker(userId, date, ticker);
            } else if (date != null) {
                // Filter by date only
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndDate(userId, date);
            } else if (ticker != null) {
                // Filter by ticker only
                dailyStocks = dailyInvestmentStockRepository.findByUserIdAndTicker(userId, ticker);
            } else {
                // Get all for user
                dailyStocks = dailyInvestmentStockRepository.findByUserId(userId);
            }
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("dailyStocks", dailyStocks);
            response.put("count", dailyStocks.size());
            response.put("filters", Map.of(
                "date", date != null ? date.toString() : "all",
                "ticker", ticker != null ? ticker : "all"
            ));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching daily investment stocks for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch daily investment stocks: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Get investment portfolio summary for a user
     * GET /api/investments/portfolio/{userId}
     */
    @GetMapping("/portfolio/{userId}")
    public ResponseEntity<Map<String, Object>> getPortfolioSummary(@PathVariable Integer userId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching portfolio summary for user ID: {}", userId);
            
            List<Investment> investments = investmentRepository.findByUser_Id(userId);
            
            // Calculate portfolio summary
            double totalValue = investments.stream()
                .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
                .sum();
                
            double totalCostBasis = investments.stream()
                .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
                .sum();
                
            double totalGainLoss = totalValue - totalCostBasis;
            double totalGainLossPercent = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0.0;
            
            Map<String, Object> summary = new HashMap<>();
            summary.put("totalValue", totalValue);
            summary.put("totalCostBasis", totalCostBasis);
            summary.put("totalGainLoss", totalGainLoss);
            summary.put("totalGainLossPercent", totalGainLossPercent);
            summary.put("totalPositions", investments.size());
            
            response.put("success", true);
            response.put("userId", userId);
            response.put("portfolioSummary", summary);
            response.put("investments", investments);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching portfolio summary for user ID: {}", userId, e);
            response.put("success", false);
            response.put("error", "Failed to fetch portfolio summary: " + e.getMessage());
            response.put("userId", userId);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    // @GetMapping("/history/{userId}/{ticker}")
    // public ResponseEntity<Map<String, Object>> getStockHistory(
    //         @PathVariable Integer userId,
    //         @PathVariable String ticker,
    //         @RequestParam(defaultValue = "1M") String period) {

    //     try {
    //         Map<String, Object> history = investmentMxService.getStockHistory(userId, ticker, period);
    //         return ResponseEntity.ok(history);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().body(Map.of("error", "Failed to fetch stock history: " + e.getMessage()));
    //     }
    // }
    
    // @GetMapping("/history/{userId}/{ticker}/custom")
    // public ResponseEntity<Map<String, Object>> getStockHistoryCustom(
    //         @PathVariable Integer userId,
    //         @PathVariable String ticker,
    //         @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate fromDate,
    //         @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate toDate) {

    //     try {
    //         Map<String, Object> history = investmentMxService.getStockHistoryByDateRange(userId, ticker, fromDate, toDate);
    //         return ResponseEntity.ok(history);
    //     } catch (IllegalArgumentException e) {
    //         return ResponseEntity.badRequest().body(Map.of("error", e.getMessage()));
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().body(Map.of("error", "Failed to fetch stock history: " + e.getMessage()));
    //     }
    // }

    // @GetMapping("/tickers/{userId}")
    // public ResponseEntity<List<String>> getUserStockTickers(@PathVariable Integer userId) {
    //     try {
    //         List<String> tickers = investmentMxService.getUserStockTickers(userId);
    //         return ResponseEntity.ok(tickers);
    //     } catch (Exception e) {
    //         return ResponseEntity.internalServerError().build();
    //     }
    // }
    /**
     * Get investment by ticker for a specific user
     * GET /api/investments/user/{userId}/ticker/{ticker}
     */
    @GetMapping("/user/{userId}/ticker/{ticker}")
    public ResponseEntity<Map<String, Object>> getInvestmentByTicker(
            @PathVariable Integer userId, 
            @PathVariable String ticker) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            log.info("Fetching investment for user ID: {} and ticker: {}", userId, ticker);
            
            List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
            Investment investment = userInvestments.stream()
                .filter(inv -> ticker.equalsIgnoreCase(inv.getTicker()))
                .findFirst()
                .orElse(null);
            
            if (investment != null) {
                response.put("success", true);
                response.put("userId", userId);
                response.put("ticker", ticker);
                response.put("investment", investment);
            } else {
                response.put("success", false);
                response.put("message", "Investment not found for ticker: " + ticker);
                response.put("userId", userId);
                response.put("ticker", ticker);
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Error fetching investment for user ID: {} and ticker: {}", userId, ticker, e);
            response.put("success", false);
            response.put("error", "Failed to fetch investment: " + e.getMessage());
            response.put("userId", userId);
            response.put("ticker", ticker);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Health check endpoint
     * GET /api/investments/health
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Investment Service");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
    /**
     * Get aggregated data for a specific stock ticker
     */
    @GetMapping("/aggregated")
    public ResponseEntity<?> getStockAggregatedData(
            @RequestParam Integer userId,
            @RequestParam String ticker,
            @RequestParam Integer months,
            @RequestParam Integer intervalDays
    ) {
        Map<String, Object> result = investmentMxService.getStockAggregatedData(userId, ticker, months, intervalDays);
        return ResponseEntity.ok(result);
    }

    /**
     * Get aggregated data for all stocks for a user
     */
  @GetMapping("/aggregated/all")
public ResponseEntity<InvestmentAggregationResponseDto> getAllStockAggregatedData(
        @RequestParam Integer userId,
        @RequestParam Integer months,
        @RequestParam Integer intervalDays
) {
    InvestmentAggregationResponseDto result = investmentMxService.getAllStocksAggregatedData(userId, months, intervalDays);
    return ResponseEntity.ok(result);
}

    /**
     * Compare current vs past portfolio performance
     */
    @GetMapping("/compare")
    public ResponseEntity<?> comparePortfolioPerformance(
            @RequestParam Integer userId,
            @RequestParam Integer currentPeriodMonths,
            @RequestParam Integer pastPeriodMonths,
            @RequestParam Integer intervalDays
    ) {
        Map<String, Object> result = investmentMxService.getPortfolioPerformanceComparison(
                userId, currentPeriodMonths, pastPeriodMonths, intervalDays);
        return ResponseEntity.ok(result);
    }

    /**
     * Get stock performance summary (gain/loss) for a given period
     */
    @GetMapping("/performance-summary")
    public ResponseEntity<?> getPerformanceSummary(
            @RequestParam Integer userId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate
    ) {
        return ResponseEntity.ok(investmentMxService.getStockPerformanceSummary(userId, startDate, endDate));
    }


}