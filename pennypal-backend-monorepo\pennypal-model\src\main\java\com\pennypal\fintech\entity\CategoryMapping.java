package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;

import lombok.Data;

@Data
@Entity
public class CategoryMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String legacyCategory;
    private String legacyCategoryParent;
    private String legacyCategoryId;
    private String primaryPfcClean;
    private String detailedPfcClean;
    private String primaryPfc;
    private String detailedPfc;
}