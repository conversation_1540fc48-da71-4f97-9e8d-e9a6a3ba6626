package com.pennypal.fintech.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Column;
import jakarta.persistence.Index;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "daily_investment_stocks", 
       indexes = {
           @Index(name = "idx_investment_date", columnList = "investment_id, date"),
           @Index(name = "idx_date_timestamp", columnList = "date, timestamp"),
           @Index(name = "idx_user_date", columnList = "user_id, date"),
           @Index(name = "idx_security_type_date", columnList = "security_type, date")
       })
public class DailyInvestmentStock {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private int id;
    
    @ManyToOne
    @JoinColumn(name = "investment_id", nullable = false)
    private Investment investment;
    
    // Add user_id for direct queries without joining investment table
    @Column(name = "user_id")
    private Integer userId;
    
    private Double price;
    private Double quantity;
    private Double value;
    private Double priceChange;
    private Double percentChange;
    
    @NotNull
    private LocalDateTime timestamp;
    
    // Add date field for daily tracking
    @NotNull
    @Column(name = "date")
    private LocalDate date;
    
    // Add security type to differentiate between stocks and mutual funds
    @Column(name = "security_type")
    private String securityType;
    
    // Add ticker symbol for easier tracking
    @Column(name = "ticker")
    private String ticker;
    
    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Investment getInvestment() {
        return investment;
    }

    public void setInvestment(Investment investment) {
        this.investment = investment;
        // Automatically set userId when investment is set
        if (investment != null && investment.getUser() != null) {
            this.userId = investment.getUser().getId();
        }
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }

    public Double getPriceChange() {
        return priceChange;
    }

    public void setPriceChange(Double priceChange) {
        this.priceChange = priceChange;
    }

    public Double getPercentChange() {
        return percentChange;
    }

    public void setPercentChange(Double percentChange) {
        this.percentChange = percentChange;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        // Automatically set date from timestamp
        if (timestamp != null) {
            this.date = timestamp.toLocalDate();
        }
    }

    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public String getSecurityType() {
        return securityType;
    }

    public void setSecurityType(String securityType) {
        this.securityType = securityType;
    }

    public String getTicker() {
        return ticker;
    }

    public void setTicker(String ticker) {
        this.ticker = ticker;
    }
}