package com.pennypal.fintech.api.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.pennypal.fintech.entity.ReceiptConsolidate;
import com.pennypal.fintech.repository.ReceiptConsolidateRepository;

@RestController
@RequestMapping("/api/v1/receipt")
public class ReceiptConsolidateController {

    @Autowired
private ReceiptConsolidateRepository receiptConsolidateRepository;

   @GetMapping("/summary")
    public ResponseEntity<?> getConsolidatedSummary() {
        try {
            List<ReceiptConsolidate> items = receiptConsolidateRepository.findAll();

            List<Map<String, Object>> response = items.stream()
                .map(item -> {
                    Map<String, Object> map = new HashMap<>();
                    map.put("itemName", item.getReceiptItem() != null ? item.getReceiptItem() : "Unknown Item");
                    map.put("totalSpent", item.getPrice() != null ? item.getPrice().doubleValue() : 0.0);
                    return map;
                })
                .collect(Collectors.toList());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            e.printStackTrace(); // Logs full stack trace in backend logs
            return ResponseEntity.status(500).body("Internal Server Error: " + e.getMessage());
        }
    }
}