package com.pennypal.fintech.repository;

import org.springframework.data.jpa.repository.JpaRepository;

import com.pennypal.fintech.entity.MxCustomers;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface MxCustomersRepository extends JpaRepository<MxCustomers, Long> {
    MxCustomers findByUserId(Integer userId);
      // Optional: Add method to find MX customer by account ID if needed
    @Query("SELECT mc FROM MxCustomers mc WHERE mc.userId IN " +
           "(SELECT a.user.id FROM Accounts a WHERE a.id = :accountId)")
    MxCustomers findByAccountId(@Param("accountId") Integer accountId);
}