package com.pennypal.fintech.service;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pennypal.fintech.dto.HoldingDto;
import com.pennypal.fintech.dto.InvestmentHoldingsResponseDto;
import com.pennypal.fintech.entity.Accounts;
import com.pennypal.fintech.entity.Investment;
import com.pennypal.fintech.entity.DailyInvestmentStock;
import com.pennypal.fintech.entity.Users;
import com.pennypal.fintech.repository.AccountRepository;
import com.pennypal.fintech.repository.InvestmentRepository;
import com.pennypal.fintech.repository.DailyInvestmentStockRepository;
import com.pennypal.fintech.repository.FinicityCustomersRepository;
import com.pennypal.fintech.repository.UserRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.Optional;
import java.util.stream.StreamSupport;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class FinicityInvestmentService {

    @Value("${finicity.partnerId}")
    private String partnerId;

    @Value("${finicity.secret}")
    private String partnerSecret;

    @Value("${finicity.appKey}")
    private String appKey;

    @Value("${finicity.baseUrl}")
    private String baseUrl;

    @Autowired
    private FinicityServicev1 finicityServicev1;

    @Autowired
    private AccountRepository accountRepository;

    @Autowired
    private InvestmentRepository investmentRepository;

    @Autowired
    private DailyInvestmentStockRepository dailyInvestmentStockRepository;

    @Autowired
    private FinicityCustomersRepository finicityCustomersRepository;

    @Autowired
    private UserRepository usersRepository;

    private String partnerAccessToken;

    /**
     * Get investment accounts for a user
     */
// Updated getInvestmentAccounts method - replace in your FinicityInvestmentService
public ResponseEntity<?> getInvestmentAccounts(Integer userId) {
    log.info("Getting investment accounts for userId: {}", userId);

    try {
        partnerAccessToken = finicityServicev1.authenticatePartner();
        log.info("Successfully authenticated with Finicity");
    } catch (Exception e) {
        log.error("Failed to authenticate with Finicity: {}", e.getMessage());
        throw new RuntimeException("Failed to authenticate with Finicity", e);
    }

    HttpHeaders headers = createHeaders();
    HttpEntity<Void> entity = new HttpEntity<>(headers);

    String customerId;
    try {
        customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();
        log.info("Found customerId: {} for userId: {}", customerId, userId);
    } catch (Exception e) {
        log.error("No Finicity customer found for userId: {}", userId);
        return ResponseEntity.badRequest().body("No Finicity customer found for user: " + userId);
    }

    try {
        String url = baseUrl + "/aggregation/v2/customers/" + customerId + "/accounts";
        log.info("Making API call to: {}", url);
        
        ResponseEntity<JsonNode> response = new RestTemplate().exchange(
            url, HttpMethod.GET, entity, JsonNode.class);

        log.info("API Response Status: {}", response.getStatusCode());
        
        if (response.getStatusCode().is2xxSuccessful()) {
            JsonNode responseBody = response.getBody();
            log.info("Full API Response: {}", responseBody);
            
            // FIX: Handle both "accounts" and "account" response structures
            JsonNode accountsNode = responseBody.get("accounts");
            if (accountsNode == null) {
                accountsNode = responseBody.get("account"); // Try singular form
            }
            
            List<Accounts> investmentAccounts = new ArrayList<>();
            List<JsonNode> allAccounts = new ArrayList<>();

            if (accountsNode != null && accountsNode.isArray()) {
                log.info("Total accounts found: {}", accountsNode.size());
                
                for (JsonNode accountNode : accountsNode) {
                    allAccounts.add(accountNode);
                    String accountType = accountNode.get("type").asText();
                    String accountId = accountNode.get("id").asText();
                    String accountName = accountNode.path("name").asText();
                    
                    log.info("Account ID: {}, Type: {}, Name: {}", accountId, accountType, accountName);
                    
                    // Check if it's an investment account
                    if (isInvestmentAccount(accountType)) {
                        log.info("Account {} identified as investment account", accountId);
                        try {
                            Accounts account = saveOrUpdateInvestmentAccount(accountNode, userId);
                            investmentAccounts.add(account);
                            log.info("Successfully saved investment account: {}", account.getId());
                        } catch (Exception e) {
                            log.error("Failed to save investment account {}: {}", accountId, e.getMessage());
                        }
                    } else {
                        log.info("Account {} is not an investment account (type: {})", accountId, accountType);
                    }
                }
                
                // Debug: Return detailed information
                Map<String, Object> debugResponse = Map.of(
                    "totalAccountsFromAPI", allAccounts.size(),
                    "investmentAccountsFound", investmentAccounts.size(),
                    "allAccountTypes", allAccounts.stream()
                        .map(account -> account.get("type").asText())
                        .distinct()
                        .toList(),
                    "investmentAccounts", investmentAccounts,
                    "allAccounts", allAccounts // Remove this in production
                );
                
                log.info("Found {} investment accounts out of {} total accounts for user {}", 
                    investmentAccounts.size(), allAccounts.size(), userId);
                return ResponseEntity.ok(debugResponse);
            } else {
                log.warn("No accounts node found in API response or accounts node is not an array");
                return ResponseEntity.ok(Map.of(
                    "message", "No accounts found in API response",
                    "response", responseBody
                ));
            }
        } else {
            log.error("API call failed with status: {} and body: {}", 
                response.getStatusCode(), response.getBody());
            throw new RuntimeException("API call failed with status: " + response.getStatusCode());
        }
    } catch (Exception e) {
        log.error("Error getting investment accounts: {}", e.getMessage(), e);
        throw new RuntimeException("Failed to get investment accounts", e);
    }
}

// Updated isInvestmentAccount method - replace in your FinicityInvestmentService
private boolean isInvestmentAccount(String accountType) {
    boolean isInvestment = accountType != null && (
        accountType.equalsIgnoreCase("investment") ||
        accountType.equalsIgnoreCase("brokerage") ||
        accountType.equalsIgnoreCase("ira") ||
        accountType.equalsIgnoreCase("401k") ||
        accountType.equalsIgnoreCase("roth") ||
        accountType.equalsIgnoreCase("pension") ||
        accountType.equalsIgnoreCase("retirement") ||
        accountType.equalsIgnoreCase("investmentTaxDeferred") || // ADD THIS
        accountType.toLowerCase().contains("invest") ||
        accountType.toLowerCase().contains("broker") ||
        accountType.toLowerCase().contains("401") ||
        accountType.toLowerCase().contains("ira") ||
        accountType.toLowerCase().contains("roth") // ADD THIS
    );
    
    log.debug("Account type '{}' is investment account: {}", accountType, isInvestment);
    return isInvestment;
}


// Enhanced saveOrUpdateInvestment method with better field mapping
private Investment saveOrUpdateInvestment(JsonNode holdingNode, Integer userId, String accountId) {
    // Try different ID fields that Finicity might use
    String holdingId = null;
    if (holdingNode.has("id")) {
        holdingId = holdingNode.get("id").asText();
    } else if (holdingNode.has("holdingId")) {
        holdingId = holdingNode.get("holdingId").asText();
    } else if (holdingNode.has("positionId")) {
        holdingId = holdingNode.get("positionId").asText();
    } else {
        // Create a unique ID from account + security
        String securityId = holdingNode.path("securityId").asText();
        String symbol = holdingNode.path("symbol").asText();
        holdingId = accountId + "_" + (securityId.isEmpty() ? symbol : securityId);
    }
    
    log.info("Saving investment with holdingId: {} for account: {}", holdingId, accountId);
    
    Optional<Investment> existingInvestment = investmentRepository.findByInvestmentId(holdingId);
    Investment investment;
    
    if (existingInvestment.isPresent()) {
        investment = existingInvestment.get();
        log.info("Updating existing investment: {}", holdingId);
    } else {
        investment = new Investment();
        log.info("Creating new investment: {}", holdingId);
        
        // Set user
        Users user = usersRepository.findById(userId)
            .orElseThrow(() -> new RuntimeException("User not found: " + userId));
        investment.setUser(user);
        
        // Set account
        Accounts account = accountRepository.findByPlaidUniqueNo(accountId)
            .orElseThrow(() -> new RuntimeException("Account not found: " + accountId));
        investment.setAccount(account);
        
        investment.setInvestmentId(holdingId);
        investment.setInsertDateTime(LocalDateTime.now());
    }

    // Update investment details with multiple field mappings
    String securityId = holdingNode.path("securityId").asText();
    if (securityId.isEmpty()) {
        securityId = holdingNode.path("cusip").asText();
    }
    investment.setSecurityId(securityId);
    
    String securityName = holdingNode.path("description").asText();
    if (securityName.isEmpty()) {
        securityName = holdingNode.path("longName").asText();
    }
    if (securityName.isEmpty()) {
        securityName = holdingNode.path("securityName").asText();
    }
    investment.setSecurityName(securityName);
    
    String ticker = holdingNode.path("symbol").asText();
    if (ticker.isEmpty()) {
        ticker = holdingNode.path("ticker").asText();
    }
    investment.setTicker(ticker);
    
    String securityType = holdingNode.path("securityType").asText();
    if (securityType.isEmpty()) {
        securityType = holdingNode.path("type").asText();
    }
    investment.setSecurityType(securityType);
    
    // Handle quantity fields
    Double quantity = null;
    if (holdingNode.has("units") && !holdingNode.get("units").isNull()) {
        quantity = holdingNode.get("units").asDouble();
    } else if (holdingNode.has("quantity") && !holdingNode.get("quantity").isNull()) {
        quantity = holdingNode.get("quantity").asDouble();
    } else if (holdingNode.has("shares") && !holdingNode.get("shares").isNull()) {
        quantity = holdingNode.get("shares").asDouble();
    }
    investment.setQuantity(quantity);
    
    // Handle value fields
    Double marketValue = null;
    if (holdingNode.has("marketValue") && !holdingNode.get("marketValue").isNull()) {
        marketValue = holdingNode.get("marketValue").asDouble();
    } else if (holdingNode.has("value") && !holdingNode.get("value").isNull()) {
        marketValue = holdingNode.get("value").asDouble();
    } else if (holdingNode.has("currentValue") && !holdingNode.get("currentValue").isNull()) {
        marketValue = holdingNode.get("currentValue").asDouble();
    }
    investment.setValue(marketValue);
    
    // Handle cost basis
    Double costBasis = null;
    if (holdingNode.has("costBasis") && !holdingNode.get("costBasis").isNull()) {
        costBasis = holdingNode.get("costBasis").asDouble();
    } else if (holdingNode.has("originalPurchasePrice") && !holdingNode.get("originalPurchasePrice").isNull()) {
        costBasis = holdingNode.get("originalPurchasePrice").asDouble();
    }
    investment.setCostBasis(costBasis);
    
    // Handle current price
    Double currentPrice = null;
    if (holdingNode.has("unitPrice") && !holdingNode.get("unitPrice").isNull()) {
        currentPrice = holdingNode.get("unitPrice").asDouble();
    } else if (holdingNode.has("currentPrice") && !holdingNode.get("currentPrice").isNull()) {
        currentPrice = holdingNode.get("currentPrice").asDouble();
    } else if (holdingNode.has("price") && !holdingNode.get("price").isNull()) {
        currentPrice = holdingNode.get("price").asDouble();
    }
    investment.setCurrentPrice(currentPrice);
    
    String currencyCode = holdingNode.path("currencySymbol").asText("USD");
    if (currencyCode.isEmpty()) {
        currencyCode = holdingNode.path("currency").asText("USD");
    }
    investment.setCurrencyCode(currencyCode);
    
    investment.setLastUpdated(LocalDateTime.now());
    
    // Calculate average purchase price if possible
    if (quantity != null && costBasis != null && quantity > 0) {
        investment.setAveragePurchasePrice(costBasis / quantity);
    }

    log.info("Saving investment: {} - {} shares of {} @ ${}", 
        securityName, quantity, ticker, currentPrice);
    
    return investmentRepository.save(investment);
}


/**
 * Convert JsonNode holding to generic HoldingDto
 */
private HoldingDto convertJsonToHoldingDto(JsonNode holdingNode) {
    HoldingDto dto = new HoldingDto();
    
    // Set basic fields (mapping Finicity fields to common fields)
    dto.setSecurityId(holdingNode.path("securityId").asText());
    dto.setSecurityName(holdingNode.path("description").asText());
    dto.setDescription(holdingNode.path("description").asText()); // Finicity specific
    dto.setTicker(holdingNode.path("symbol").asText());
    dto.setSymbol(holdingNode.path("symbol").asText()); // Finicity specific
    dto.setSecurityType(holdingNode.path("securityType").asText());
    dto.setQuantity(holdingNode.path("units").asDouble());
    dto.setUnits(holdingNode.path("units").asDouble()); // Finicity specific
    dto.setValue(holdingNode.path("marketValue").asDouble());
    dto.setMarketValue(holdingNode.path("marketValue").asDouble()); // Finicity specific
    dto.setCostBasis(holdingNode.path("costBasis").asDouble());
    dto.setCurrentPrice(holdingNode.path("unitPrice").asDouble());
    dto.setUnitPrice(holdingNode.path("unitPrice").asDouble()); // Finicity specific
    dto.setCurrencyCode(holdingNode.path("currencySymbol").asText("USD"));
    dto.setLastUpdated(LocalDateTime.now());
    dto.setInvestmentId(holdingNode.path("id").asText());
    dto.setProvider("finicity"); // Indicate this is from Finicity
    
    // Calculate average purchase price if possible
    Double quantity = dto.getQuantity();
    Double costBasis = dto.getCostBasis();
    if (quantity != null && costBasis != null && quantity > 0) {
        dto.setAveragePurchasePrice(costBasis / quantity);
    }
    
    // Calculate gain/loss
    if (dto.getValue() != null && dto.getCostBasis() != null) {
        Double gainLoss = dto.getValue() - dto.getCostBasis();
        dto.setGainLoss(gainLoss);
        
        if (dto.getCostBasis() > 0) {
            Double gainLossPercentage = (gainLoss / dto.getCostBasis()) * 100;
            dto.setGainLossPercentage(gainLossPercentage);
        }
    }
    
    return dto;
}

    /**
     * Sync all investment holdings for a user
     */
    public ResponseEntity<?> syncInvestmentHoldings(Integer userId) {
        log.info("Syncing investment holdings for userId: {}", userId);

        // Get all investment accounts for the user
        List<Accounts> investmentAccounts = accountRepository.findByUserIdAndAuthPartner(userId, "finicity")
            .stream()
            .filter(account -> isInvestmentAccount(account.getAccountType()))
            .toList();

        log.info("Found {} investment accounts for user {}", investmentAccounts.size(), userId);

        int totalHoldings = 0;
        for (Accounts account : investmentAccounts) {
            try {
                ResponseEntity<?> holdingsResponse = getAccountHoldings(userId, account.getPlaidUniqueNo());
                if (holdingsResponse.getBody() instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<Investment> holdings = (List<Investment>) holdingsResponse.getBody();
                    totalHoldings += holdings.size();
                }
            } catch (Exception e) {
                log.error("Failed to sync holdings for account {}: {}", account.getId(), e.getMessage());
            }
        }

        log.info("Successfully synced {} total holdings for user {}", totalHoldings, userId);
        return ResponseEntity.ok("Investment holdings synced successfully. Total holdings: " + totalHoldings);
    }

    /**
     * Get investment transactions for an account
     */
    public ResponseEntity<?> getInvestmentTransactions(Integer userId, String accountId) {
        log.info("Getting investment transactions for account: {} and user: {}", accountId, userId);

        try {
            partnerAccessToken = finicityServicev1.authenticatePartner();
            log.info("Successfully authenticated with Finicity");
        } catch (Exception e) {
            log.error("Failed to authenticate with Finicity: {}", e.getMessage());
            throw new RuntimeException("Failed to authenticate with Finicity", e);
        }

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> entity = new HttpEntity<>(headers);

        String customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();

        // Get transactions from last 90 days
        long fromDate = Instant.now().minus(90, java.time.temporal.ChronoUnit.DAYS).getEpochSecond();
        long toDate = Instant.now().getEpochSecond();

        try {
            String url = String.format("%s/aggregation/v2/customers/%s/accounts/%s/transactions?fromDate=%d&toDate=%d",
                baseUrl, customerId, accountId, fromDate, toDate);

            ResponseEntity<JsonNode> response = new RestTemplate().exchange(
                url, HttpMethod.GET, entity, JsonNode.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                JsonNode transactionsNode = response.getBody().get("transactions");
                List<JsonNode> investmentTransactions = new ArrayList<>();

                if (transactionsNode != null && transactionsNode.isArray()) {
                    for (JsonNode transactionNode : transactionsNode) {
                        String investmentType = transactionNode.path("investmentTransactionType").asText();
                        if (!investmentType.isEmpty()) {
                            investmentTransactions.add(transactionNode);
                        }
                    }
                }

                log.info("Found {} investment transactions for account {}", investmentTransactions.size(), accountId);
                return ResponseEntity.ok(investmentTransactions);
            } else {
                throw new RuntimeException("API call failed with status: " + response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Error getting investment transactions: {}", e.getMessage());
            throw new RuntimeException("Failed to get investment transactions", e);
        }
    }

    /**
     * Get user's investment portfolio summary
     */
    public ResponseEntity<?> getInvestmentPortfolio(Integer userId) {
        log.info("Getting investment portfolio for userId: {}", userId);

        List<Investment> userInvestments = investmentRepository.findByUser_Id(userId);
        
        if (userInvestments.isEmpty()) {
            return ResponseEntity.ok("No investments found for user. Please sync investment holdings first.");
        }

        // Calculate portfolio summary
        double totalValue = userInvestments.stream()
            .mapToDouble(inv -> inv.getValue() != null ? inv.getValue() : 0.0)
            .sum();

        double totalCostBasis = userInvestments.stream()
            .mapToDouble(inv -> inv.getCostBasis() != null ? inv.getCostBasis() : 0.0)
            .sum();

        double totalGainLoss = totalValue - totalCostBasis;
        double gainLossPercentage = totalCostBasis > 0 ? (totalGainLoss / totalCostBasis) * 100 : 0;

        Map<String, Object> portfolioSummary = Map.of(
            "totalValue", totalValue,
            "totalCostBasis", totalCostBasis,
            "totalGainLoss", totalGainLoss,
            "gainLossPercentage", gainLossPercentage,
            "numberOfHoldings", userInvestments.size(),
            "investments", userInvestments
        );

        return ResponseEntity.ok(portfolioSummary);
    }

    private Accounts saveOrUpdateInvestmentAccount(JsonNode accountNode, Integer userId) {
        String accountId = accountNode.get("id").asText();
        
        Optional<Accounts> existingAccount = accountRepository.findByPlaidUniqueNo(accountId);
        Accounts account;
        
        if (existingAccount.isPresent()) {
            account = existingAccount.get();
        } else {
            account = new Accounts();
            Users user = usersRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found: " + userId));
            account.setUser(user);
            account.setPlaidUniqueNo(accountId);
            account.setAuthPartner("finicity");
        }

        // Update account details
        account.setAccountName(accountNode.path("name").asText());
        account.setAccountType(accountNode.path("type").asText());
        account.setAccountSubtype(accountNode.path("accountType").asText());
        account.setBalance(accountNode.path("balance").asDouble());
        account.setAccountMask(accountNode.path("accountNumberDisplay").asText());
        // account.setOfficialName(accountNode.path("displayName").asText());
        
        return accountRepository.save(account);
    }

    
    private void saveDailyInvestmentStock(Investment investment, JsonNode holdingNode) {
        DailyInvestmentStock dailyStock = new DailyInvestmentStock();
        
        dailyStock.setInvestment(investment);
        dailyStock.setPrice(holdingNode.path("unitPrice").asDouble());
        dailyStock.setQuantity(holdingNode.path("units").asDouble());
        dailyStock.setValue(holdingNode.path("marketValue").asDouble());
        dailyStock.setTimestamp(LocalDateTime.now());
        
        // Calculate price change if we have previous data
        DailyInvestmentStock latestStock = dailyInvestmentStockRepository.findLatestByInvestmentId(investment.getId());
        if (latestStock != null && latestStock.getPrice() != null) {
            Double currentPrice = dailyStock.getPrice();
            Double previousPrice = latestStock.getPrice();
            
            if (currentPrice != null && previousPrice != null && previousPrice > 0) {
                double priceChange = currentPrice - previousPrice;
                double percentChange = (priceChange / previousPrice) * 100;
                
                dailyStock.setPriceChange(priceChange);
                dailyStock.setPercentChange(percentChange);
            }
        }
        
        dailyInvestmentStockRepository.save(dailyStock);
    }
    public ResponseEntity<?> debugGetAllAccounts(Integer userId) {
    log.info("DEBUG: Getting all accounts for userId: {}", userId);

    try {
        partnerAccessToken = finicityServicev1.authenticatePartner();
    } catch (Exception e) {
        log.error("Failed to authenticate with Finicity: {}", e.getMessage());
        return ResponseEntity.status(500).body("Authentication failed");
    }

    HttpHeaders headers = createHeaders();
    HttpEntity<Void> entity = new HttpEntity<>(headers);

    try {
        String customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();
        String url = baseUrl + "/aggregation/v2/customers/" + customerId + "/accounts";
        
        ResponseEntity<JsonNode> response = new RestTemplate().exchange(
            url, HttpMethod.GET, entity, JsonNode.class);

        if (response.getStatusCode().is2xxSuccessful()) {
            return ResponseEntity.ok(response.getBody());
        } else {
            return ResponseEntity.status(response.getStatusCode()).body(response.getBody());
        }
    } catch (Exception e) {
        log.error("Error in debug method: {}", e.getMessage(), e);
        return ResponseEntity.status(500).body("Error: " + e.getMessage());
    }
}
// Updated getAccountHoldings method with correct v1 endpoint
public ResponseEntity<?> getAccountHoldings(Integer userId, String accountId) {
    log.info("Getting holdings for account: {} and user: {}", accountId, userId);

    try {
        partnerAccessToken = finicityServicev1.authenticatePartner();
        log.info("Successfully authenticated with Finicity");
    } catch (Exception e) {
        log.error("Failed to authenticate with Finicity: {}", e.getMessage());
        return ResponseEntity.status(500).body(Map.of(
            "error", "Authentication failed",
            "message", e.getMessage()
        ));
    }

    HttpHeaders headers = createHeaders();
    HttpEntity<Void> entity = new HttpEntity<>(headers);

    String customerId;
    try {
        customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();
        log.info("Found customerId: {} for userId: {}", customerId, userId);
    } catch (Exception e) {
        log.error("No Finicity customer found for userId: {}", userId);
        return ResponseEntity.status(400).body(Map.of(
            "error", "No Finicity customer found",
            "message", "User not linked to Finicity",
            "userId", userId
        ));
    }

    try {
        // Use the correct v1 holdings endpoint
        String url = String.format("%s/aggregation/v1/customers/%s/accounts/%s/holdings",
            baseUrl, customerId, accountId);
        
        log.info("Making Finicity API call to: {}", url);

        ResponseEntity<JsonNode> response = new RestTemplate().exchange(
            url, HttpMethod.GET, entity, JsonNode.class);

        log.info("Finicity API response status: {}", response.getStatusCode());
        log.info("Finicity API response body: {}", response.getBody());

        if (response.getStatusCode().is2xxSuccessful()) {
            JsonNode responseBody = response.getBody();
            
            // Handle different possible response structures
            JsonNode holdingsNode = responseBody.get("holdings");
            if (holdingsNode == null) {
                holdingsNode = responseBody.get("holding");
            }
            
            List<Investment> savedInvestments = new ArrayList<>();
            List<HoldingDto> holdingDtos = new ArrayList<>();
            double totalValue = 0.0;

            if (holdingsNode != null && holdingsNode.isArray() && holdingsNode.size() > 0) {
                log.info("Found {} holdings in API response", holdingsNode.size());
                
                for (JsonNode holdingNode : holdingsNode) {
                    try {
                        log.info("Processing holding: {}", holdingNode);
                        
                        // Save investment to database
                        Investment investment = saveOrUpdateInvestment(holdingNode, userId, accountId);
                        savedInvestments.add(investment);
                        
                        // Create HoldingDto for response
                        HoldingDto holdingDto = convertJsonToHoldingDto(holdingNode);
                        holdingDto.setAccountId(accountId);
                        holdingDtos.add(holdingDto);
                        
                        // Add to total value
                        if (holdingDto.getValue() != null) {
                            totalValue += holdingDto.getValue();
                        }
                        
                        // Save daily stock data
                        saveDailyInvestmentStock(investment, holdingNode);
                        
                        log.info("Successfully processed holding: {} - {} shares @ ${}",
                            holdingDto.getSecurityName(),
                            holdingDto.getQuantity(),
                            holdingDto.getCurrentPrice());
                        
                    } catch (Exception e) {
                        log.error("Error processing individual holding: {}", e.getMessage(), e);
                        // Continue processing other holdings
                    }
                }
            } else {
                log.warn("No holdings found in API response. Response structure: {}", responseBody);
                return ResponseEntity.ok(Map.of(
                    "message", "No holdings found for this account",
                    "accountId", accountId,
                    "totalHoldings", 0,
                    "holdings", new ArrayList<>(),
                    "apiResponse", responseBody
                ));
            }

            // Create comprehensive response
            InvestmentHoldingsResponseDto responseDto = new InvestmentHoldingsResponseDto();
            responseDto.setAccountId(accountId);
            responseDto.setHoldings(holdingDtos);
            responseDto.setTotalHoldings(holdingDtos.size());
            responseDto.setTotalValue(Math.round(totalValue * 100.0) / 100.0);
            responseDto.setLastUpdated(LocalDateTime.now());
            responseDto.setProvider("finicity");

            log.info("Successfully processed {} holdings for account {}. Total value: ${}",
                savedInvestments.size(), accountId, totalValue);
            
            return ResponseEntity.ok(responseDto);
            
        } else {
            log.error("Finicity API call failed with status: {} and response: {}", 
                response.getStatusCode(), response.getBody());
            return ResponseEntity.status(response.getStatusCode()).body(Map.of(
                "error", "Finicity API call failed",
                "statusCode", response.getStatusCode().value(),
                "message", "Failed to retrieve holdings from Finicity",
                "apiResponse", response.getBody()
            ));
        }
        
    } catch (Exception e) {
        log.error("Error getting account holdings for account {}: {}", accountId, e.getMessage(), e);
        return ResponseEntity.status(500).body(Map.of(
            "error", "Failed to get account holdings",
            "message", e.getMessage(),
            "accountId", accountId,
            "userId", userId
        ));
    }
}
// Updated getAllUserHoldings method to fix Map.of() issues
public ResponseEntity<?> getAllUserHoldings(Integer userId) {
    log.info("Getting all investment holdings for userId: {}", userId);

    try {
        partnerAccessToken = finicityServicev1.authenticatePartner();
        log.info("Successfully authenticated with Finicity");
    } catch (Exception e) {
        log.error("Failed to authenticate with Finicity: {}", e.getMessage());
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "Authentication failed");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("userId", userId);
        return ResponseEntity.status(500).body(errorResponse);
    }

    String customerId;
    try {
        customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();
        log.info("Found customerId: {} for userId: {}", customerId, userId);
    } catch (Exception e) {
        log.error("No Finicity customer found for userId: {}", userId);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "No Finicity customer found");
        errorResponse.put("message", "User not linked to Finicity");
        errorResponse.put("userId", userId);
        return ResponseEntity.status(400).body(errorResponse);
    }

    HttpHeaders headers = createHeaders();
    HttpEntity<Void> entity = new HttpEntity<>(headers);

    try {
        // Step 1: Get all accounts from Finicity
        String accountsUrl = baseUrl + "/aggregation/v2/customers/" + customerId + "/accounts";
        log.info("Getting accounts from: {}", accountsUrl);
        
        ResponseEntity<JsonNode> accountsResponse = new RestTemplate().exchange(
            accountsUrl, HttpMethod.GET, entity, JsonNode.class);

        if (!accountsResponse.getStatusCode().is2xxSuccessful()) {
            log.error("Failed to get accounts: {}", accountsResponse.getStatusCode());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("error", "Failed to retrieve accounts from Finicity");
            errorResponse.put("statusCode", accountsResponse.getStatusCode().value());
            errorResponse.put("userId", userId);
            return ResponseEntity.status(accountsResponse.getStatusCode()).body(errorResponse);
        }

        JsonNode responseBody = accountsResponse.getBody();
        JsonNode accountsNode = responseBody.get("accounts");
        if (accountsNode == null) {
            accountsNode = responseBody.get("account");
        }
        
        if (accountsNode == null || !accountsNode.isArray()) {
            log.warn("No accounts found in API response for user: {}", userId);
            Map<String, Object> noAccountsResponse = new HashMap<>();
            noAccountsResponse.put("message", "No accounts found");
            noAccountsResponse.put("userId", userId);
            noAccountsResponse.put("totalAccounts", 0);
            noAccountsResponse.put("investmentAccounts", 0);
            noAccountsResponse.put("holdings", new ArrayList<>());
            return ResponseEntity.ok(noAccountsResponse);
        }

        // Step 2: Filter investment accounts
        List<String> investmentAccountIds = new ArrayList<>();
        List<Map<String, Object>> accountSummaries = new ArrayList<>();

        for (JsonNode accountNode : accountsNode) {
            String accountType = accountNode.get("type").asText();
            String accountId = accountNode.get("id").asText();
            String accountName = accountNode.path("name").asText();
            Double accountBalance = accountNode.path("balance").asDouble();
            
            log.info("Processing account: {} - Type: {} - Name: {}", accountId, accountType, accountName);
            
            if (isInvestmentAccount(accountType)) {
                investmentAccountIds.add(accountId);
                
                try {
                    Accounts savedAccount = saveOrUpdateInvestmentAccount(accountNode, userId);
                    Map<String, Object> accountSummary = new HashMap<>();
                    accountSummary.put("accountId", accountId);
                    accountSummary.put("accountName", accountName);
                    accountSummary.put("accountType", accountType);
                    accountSummary.put("balance", accountBalance != null ? accountBalance : 0.0);
                    accountSummary.put("databaseId", savedAccount.getId());
                    accountSummaries.add(accountSummary);
                    log.info("Successfully saved investment account: {}", accountId);
                } catch (Exception e) {
                    log.error("Failed to save investment account {}: {}", accountId, e.getMessage());
                }
            }
        }

        log.info("Found {} investment accounts for user {}", investmentAccountIds.size(), userId);

        if (investmentAccountIds.isEmpty()) {
            Map<String, Object> noInvestmentAccountsResponse = new HashMap<>();
            noInvestmentAccountsResponse.put("message", "No investment accounts found");
            noInvestmentAccountsResponse.put("userId", userId);
            noInvestmentAccountsResponse.put("totalAccounts", accountsNode.size());
            noInvestmentAccountsResponse.put("investmentAccounts", 0);
            noInvestmentAccountsResponse.put("holdings", new ArrayList<>());
            return ResponseEntity.ok(noInvestmentAccountsResponse);
        }

        // Step 3: Get holdings for all investment accounts using v1 endpoint
        List<HoldingDto> allHoldings = new ArrayList<>();
        double totalPortfolioValue = 0.0;
        int totalHoldingsCount = 0;
        List<String> accountsWithHoldings = new ArrayList<>();
        List<String> accountsWithoutHoldings = new ArrayList<>();

        for (String accountId : investmentAccountIds) {
            try {
                // Use v1 holdings endpoint consistently
                String holdingsUrl = String.format("%s/aggregation/v1/customers/%s/accounts/%s/holdings",
                    baseUrl, customerId, accountId);

                log.info("Getting holdings for account: {} using URL: {}", accountId, holdingsUrl);
                ResponseEntity<JsonNode> holdingsResponse = new RestTemplate().exchange(
                    holdingsUrl, HttpMethod.GET, entity, JsonNode.class);

                log.info("Holdings response status for account {}: {}", accountId, holdingsResponse.getStatusCode());

                if (holdingsResponse.getStatusCode().is2xxSuccessful()) {
                    JsonNode holdingsBody = holdingsResponse.getBody();
                    log.info("Holdings response body for account {}: {}", accountId, holdingsBody);
                    
                    JsonNode holdingsNode = holdingsBody.get("holdings");
                    if (holdingsNode == null) {
                        holdingsNode = holdingsBody.get("holding");
                    }
                    
                    if (holdingsNode != null && holdingsNode.isArray() && holdingsNode.size() > 0) {
                        log.info("Found {} holdings for account {}", holdingsNode.size(), accountId);
                        accountsWithHoldings.add(accountId);
                        
                        for (JsonNode holdingNode : holdingsNode) {
                            try {
                                // Save investment to database
                                Investment investment = saveOrUpdateInvestment(holdingNode, userId, accountId);
                                
                                // Create HoldingDto for response
                                HoldingDto holdingDto = convertJsonToHoldingDto(holdingNode);
                                holdingDto.setAccountId(accountId);
                                allHoldings.add(holdingDto);
                                
                                // Add to total value
                                if (holdingDto.getValue() != null) {
                                    totalPortfolioValue += holdingDto.getValue();
                                }
                                
                                // Save daily stock data
                                saveDailyInvestmentStock(investment, holdingNode);
                                totalHoldingsCount++;
                                
                                log.info("Processed holding: {} - {} shares @ ${}",
                                    holdingDto.getSecurityName(),
                                    holdingDto.getQuantity(),
                                    holdingDto.getCurrentPrice());
                                    
                            } catch (Exception e) {
                                log.error("Error processing holding for account {}: {}", accountId, e.getMessage());
                            }
                        }
                    } else {
                        log.info("No holdings found for account {}", accountId);
                        accountsWithoutHoldings.add(accountId);
                    }
                } else {
                    log.warn("Failed to get holdings for account {}: HTTP {}", 
                        accountId, holdingsResponse.getStatusCode());
                    accountsWithoutHoldings.add(accountId);
                }
            } catch (Exception e) {
                log.error("Error getting holdings for account {}: {}", accountId, e.getMessage());
                accountsWithoutHoldings.add(accountId);
            }
        }

        // Step 4: Create comprehensive response using HashMap instead of Map.of()
        Map<String, Object> summaryMap = new HashMap<>();
        summaryMap.put("totalInvestmentAccounts", investmentAccountIds.size());
        summaryMap.put("accountsWithHoldings", accountsWithHoldings.size());
        summaryMap.put("accountsWithoutHoldings", accountsWithoutHoldings.size());
        summaryMap.put("totalHoldings", totalHoldingsCount);
        summaryMap.put("totalPortfolioValue", Math.round(totalPortfolioValue * 100.0) / 100.0);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", true);
        response.put("message", "Successfully retrieved investment holdings");
        response.put("userId", userId);
        response.put("summary", summaryMap);
        response.put("lastUpdated", LocalDateTime.now());
        response.put("accounts", accountSummaries);
        response.put("holdings", allHoldings);
        response.put("accountsWithHoldings", accountsWithHoldings);
        response.put("accountsWithoutHoldings", accountsWithoutHoldings);
        response.put("provider", "finicity");
        response.put("apiEndpoint", "v1/holdings");

        log.info("Successfully retrieved {} holdings from {} investment accounts for user {}. Portfolio value: ${}",
            totalHoldingsCount, investmentAccountIds.size(), userId, totalPortfolioValue);
        
        return ResponseEntity.ok(response);

    } catch (Exception e) {
        log.error("Error getting all user holdings for user {}: {}", userId, e.getMessage(), e);
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("error", "Failed to get all user holdings");
        errorResponse.put("message", e.getMessage());
        errorResponse.put("userId", userId);
        errorResponse.put("timestamp", LocalDateTime.now());
        return ResponseEntity.status(500).body(errorResponse);
    }
}
// Enhanced debug method to test the v1 holdings endpoint specifically
public ResponseEntity<?> debugGetAccountHoldings(Integer userId, String accountId) {
    log.info("DEBUG: Getting holdings for account: {} and user: {}", accountId, userId);

    try {
        partnerAccessToken = finicityServicev1.authenticatePartner();
    } catch (Exception e) {
        log.error("Failed to authenticate with Finicity: {}", e.getMessage());
        return ResponseEntity.status(500).body(Map.of(
            "error", "Authentication failed",
            "message", e.getMessage()
        ));
    }

    HttpHeaders headers = createHeaders();
    HttpEntity<Void> entity = new HttpEntity<>(headers);

    try {
        String customerId = finicityCustomersRepository.findByUserId(userId).getFinicityCustomerId();
        
        // Test the specific v1 holdings endpoint you mentioned
        String holdingsUrl = String.format("%s/aggregation/v1/customers/%s/accounts/%s/holdings",
            baseUrl, customerId, accountId);
        
        log.info("DEBUG: Testing v1 holdings endpoint: {}", holdingsUrl);
        
        ResponseEntity<JsonNode> response = new RestTemplate().exchange(
            holdingsUrl, HttpMethod.GET, entity, JsonNode.class);
        
        Map<String, Object> debugResponse = Map.of(
            "endpoint", holdingsUrl,
            "statusCode", response.getStatusCode().value(),
            "headers", response.getHeaders().toSingleValueMap(),
            "body", response.getBody(),
            "accountId", accountId,
            "customerId", customerId,
            "timestamp", LocalDateTime.now()
        );
        
        log.info("DEBUG: v1 Holdings endpoint response - Status: {}, Body: {}", 
            response.getStatusCode(), response.getBody());
        
        return ResponseEntity.ok(debugResponse);
        
    } catch (Exception e) {
        log.error("DEBUG: Error testing holdings endpoint: {}", e.getMessage(), e);
        return ResponseEntity.status(500).body(Map.of(
            "error", "Debug test failed",
            "message", e.getMessage(),
            "accountId", accountId,
            "userId", userId
        ));
    }
}

// Helper method to create HTTP headers for Finicity API calls
private HttpHeaders createHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Finicity-App-Key", appKey);
    headers.set("Finicity-App-Token", partnerAccessToken);
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.set("Accept", MediaType.APPLICATION_JSON_VALUE);
    return headers;
}
}
