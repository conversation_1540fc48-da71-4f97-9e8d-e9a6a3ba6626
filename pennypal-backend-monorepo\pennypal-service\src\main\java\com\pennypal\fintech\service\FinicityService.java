package com.pennypal.fintech.service;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

@Service
public class FinicityService {
    private static final Logger logger = LoggerFactory.getLogger(FinicityService.class);

    @Value("${finicity.partnerId}")
    private String partnerId;

    @Value("${finicity.secret}")
    private String secret;

    @Value("${finicity.appKey}")
    private String appKey;

    private final RestTemplate restTemplate = new RestTemplate();
    private String accessToken;

    public String authenticate() {
        // Add multiple logging methods to ensure visibility
        System.out.println("Starting Finicity authentication...");
        System.err.println("Debug: Finicity authentication started"); // This will show in red
        logger.info("Finicity authentication method called");
        
        try {
            // Log the credentials being used (be careful with this in production)
            logger.debug("Using partnerId: {}", partnerId);
            System.out.println("AppKey being used: " + appKey);

            String url = "https://api.finicity.com/aggregation/v2/partners/authentication";

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Finicity-App-Key", appKey);
            headers.set("Accept", "application/json");

            JSONObject requestBody = new JSONObject();
            requestBody.put("partnerId", partnerId);
            requestBody.put("partnerSecret", secret);

            HttpEntity<String> request = new HttpEntity<>(requestBody.toString(), headers);

            System.out.println("Sending request to Finicity...");
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, request, String.class);
            System.out.println("Received response from Finicity: " + response.getStatusCode());

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject responseBody = new JSONObject(response.getBody());
                accessToken = responseBody.getString("token");
                System.out.println("Successfully obtained access token");
                return accessToken;
            } else {
                System.err.println("Authentication failed with status: " + response.getStatusCode());
                throw new RuntimeException("Authentication failed: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException e) {
            HttpStatusCode statusCode = e.getStatusCode();
            String errorBody = e.getResponseBodyAsString();
            
            System.err.println("HTTP Error occurred: " + statusCode.value());
            System.err.println("Error response: " + errorBody);
            logger.error("Finicity Authentication Error: HTTP {} - {}", statusCode.value(), errorBody);

            if (statusCode.value() == HttpStatus.FORBIDDEN.value()) {
                logger.error("Access Forbidden. Check your credentials and API permissions.");
            } else if (statusCode.value() == HttpStatus.UNAUTHORIZED.value()) {
                logger.error("Unauthorized. Verify your authentication details.");
            } else {
                logger.error("Unexpected HTTP error during authentication.");
            }

            throw new RuntimeException("Authentication failed: " + statusCode, e);
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            e.printStackTrace();
            logger.error("Unexpected error during Finicity authentication", e);
            throw new RuntimeException("Unexpected authentication error", e);
        }
    }

    public boolean validateCredentials() {
        return partnerId != null && !partnerId.isEmpty() &&
               secret != null && !secret.isEmpty() &&
               appKey != null && !appKey.isEmpty();
    }

    public String getAccessToken() {
        return accessToken;
    }

    public String getCustomerAccounts(String customerId) {
        if (accessToken == null) {
            authenticate();
        }

        String url = "https://api.finicity.com/aggregation/v2/customers/" + customerId + "/accounts";

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", accessToken);
        headers.set("Accept", "application/json");

        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);

        return response.getBody();
    }

    public String getCustomerTransactions(String customerId, String accountId, long fromDate, long toDate) {
        if (accessToken == null) {
            authenticate();
        }

        String url = "https://api.finicity.com/aggregation/v3/customers/" + customerId + "/accounts/" + accountId +
                "/transactions?fromDate=" + fromDate + "&toDate=" + toDate;

        HttpHeaders headers = new HttpHeaders();
        headers.set("Finicity-App-Key", appKey);
        headers.set("Finicity-App-Token", accessToken);
        headers.set("Accept", "application/json");

        HttpEntity<String> request = new HttpEntity<>(headers);
        ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);

        return response.getBody();
    }
}
