package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.dto.FinicityConnectRequestDto;
import com.pennypal.fintech.dto.FinicityCustomerRequestDto;
import com.pennypal.fintech.service.FinicityServicev1;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/finicity")
public class FinicityControllerv1 {

    @Autowired
    private FinicityServicev1 finicityServicev1;

    @PostMapping("/create-customer")
    public ResponseEntity<String> createCustomer(@RequestBody FinicityCustomerRequestDto request) {
        System.out.println("Creating customer: " + request);
        String customerId = finicityServicev1.createCustomer(request);
        System.out.println("Customer ID: " + customerId);
        return ResponseEntity.ok(customerId);
    }

    @PostMapping("/connect-link")
    public ResponseEntity<String> getConnectLink(@RequestBody FinicityConnectRequestDto request) {
        System.out.println("Generating connect link for customer: " + request.getUserId());
        String link = finicityServicev1.generateConnectLink(request.getUserId());
        System.out.println("Connect link: " + link);
        return ResponseEntity.ok(link);
    }

    @PostMapping("/accounts")
    public ResponseEntity<?> getAccounts(@RequestBody FinicityConnectRequestDto request) {
        System.out.println("Getting accounts for customer: " + request.getUserId());
        return ResponseEntity.ok(finicityServicev1.getCustomerAccounts(request.getUserId()));
    }

    @PostMapping("/transactions/{userId}")
    public ResponseEntity<?> getTransactions(@PathVariable Integer userId) {
        System.out.println("Getting transactions for userId: " + userId);
        return ResponseEntity.ok(finicityServicev1.getTransactions(userId));
    }

    @PostMapping("/sync-transactions/{userId}")
    public ResponseEntity<?> syncTransactions(@PathVariable Integer userId) {
        System.out.println("Syncing transactions for userId: " + userId);
        return ResponseEntity.ok(finicityServicev1.syncTransactions(userId));
    }
}