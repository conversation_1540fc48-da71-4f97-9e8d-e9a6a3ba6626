package com.pennypal.fintech.dto;

import lombok.Data;

@Data
public class CategoryMappingDto {
    private String legacyCategoryId;
    private String legacyCategory;
    private String legacyCategoryParent;
    private String primaryPfc;
    private String detailedPfc;
    private String primaryPfcClean;
    private String detailedPfcClean;

    public CategoryMappingDto(String legacyCategoryId, String legacyCategory, 
                              String legacyCategoryParent, String primaryPfc, String primaryPfcClean,
                              String detailedPfc, String detailedPfcClean) {
        this.legacyCategoryId = legacyCategoryId;
        this.legacyCategory = legacyCategory;
        this.legacyCategoryParent = legacyCategoryParent;
        this.primaryPfc = primaryPfc;
        this.primaryPfcClean = primaryPfcClean;
        this.detailedPfc = detailedPfc;
        this.detailedPfcClean = detailedPfcClean;
    }

    public String getLegacyCategoryId() { return legacyCategoryId; }
    public String getLegacyCategory() { return legacyCategory; }
    public String getLegacyCategoryParent() { return legacyCategoryParent; }
    public String getPrimaryPfc() { return primaryPfc; }
    public String getPrimaryPfcClean() { return primaryPfcClean; }
    public String getDetailedPfc() { return detailedPfc; }
    public String getDetailedPfcClean() { return detailedPfcClean; }
}