package com.pennypal.fintech.api.controller;

import com.pennypal.fintech.entity.Reconcile;
import com.pennypal.fintech.repository.ReconcileRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/v1/reconcile")
public class ReconciliationController {
    @Autowired
    private ReconcileRepository reconcileRepository;

    @GetMapping("/all")
    public ResponseEntity<List<Reconcile>> getAllReconciledTransactions() {
        List<Reconcile> reconciles = reconcileRepository.findAllByRemovedFalse();
        return ResponseEntity.ok(reconciles);
    }

    @GetMapping("/by-reconcile-id/{reconcileId}")
public ResponseEntity<List<Reconcile>> getReconciledByReconcileId(@PathVariable String reconcileId) {
    System.out.println("Fetching reconciled transactions for: " + reconcileId);
    List<Reconcile> reconciled = reconcileRepository.findByReconcileId(reconcileId);
    System.out.println("Found " + reconciled.size() + " matches");
    return ResponseEntity.ok(reconciled);
}


}
